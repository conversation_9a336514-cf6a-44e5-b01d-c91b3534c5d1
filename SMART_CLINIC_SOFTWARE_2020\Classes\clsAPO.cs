﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsAPO
    {
        public static long APO_ID;
        public static long APO_CODE;
        public static string APO_DATE;
        public static string APO_TIME;
        public static string APO_NAME;
        public static string APO_NOTE;
        public static long CLI_ID;
        public static long CUST_ID;
        public static long DOC_ID;

        public static APO_TBLTableAdapter APO_DATATABLE = new APO_TBLTableAdapter();

        public DataTable APO_List()
        {
            DataTable dt = new DataTable();
            try
            {
                dt = clsAPO.APO_DATATABLE.GetData();
            }
            catch
            {
                return dt;
            }
            return dt;
        }

        public DataTable APO_LIST_F_DATE_S_DATE_CUST_DOC(string F_DATE , string S_DATE , string S_CUST_NAME, string S_DOC_NAME)
        {
            DataTable dt = new DataTable();
            try
            {
                dt = APO_DATATABLE.APO_LISTbyDATEorDOC_NAMEorCUST_NAME(F_DATE, S_DATE, S_DOC_NAME, S_CUST_NAME);

            }
            catch 
            {
                return dt;
            }
            return dt;
        }

        public DataTable APO_LIST_CUST_DOC(string S_CUST_NAME, string S_DOC_NAME)
        {
            DataTable dt = new DataTable();
            try
            {
                dt = APO_DATATABLE.APO_LISTbyCUST_NAMEorDOC_NAME(S_DOC_NAME, S_CUST_NAME);

            }
            catch
            {
                return dt;
            }
            return dt;
        }

        public DataTable Select_APO(long S_APO_CODE)
        {
            DataTable dt = new DataTable();
            try
            {
                dt = APO_DATATABLE.APO_LISTbYAPO_CODE(S_APO_CODE);
                if (dt.Rows.Count == 1)
                {
                    APO_ID = Convert.ToInt64(dt.Rows[0]["APO_ID"]);
                    APO_CODE = Convert.ToInt64(dt.Rows[0]["APO_CODE"]);
                    APO_DATE = string.Format(dt.Rows[0]["APO_DATE"].ToString(), "dd/MM/yyyy");
                    APO_TIME = (dt.Rows[0]["APO_TIME"]).ToString();
                    APO_NAME = (dt.Rows[0]["APO_NAME"]).ToString();
                    APO_NOTE = (dt.Rows[0]["APO_NOTE"]).ToString();
                    CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                    CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                    DOC_ID = Convert.ToInt64(dt.Rows[0]["DOC_ID"]);
                }
                else
                {
                    APO_ID = 0;
                    APO_CODE = 0;
                    APO_DATE = "";
                    APO_TIME = "";
                    APO_NAME = "";
                    APO_NOTE = "";
                    CLI_ID = 0;
                    CUST_ID = 0;
                    DOC_ID = 0;
                }
            }
            catch 
            {
                return dt;
            }
            
            return dt;
        }
    }
}
