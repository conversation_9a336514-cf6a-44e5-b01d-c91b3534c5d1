﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsCARD
    {
        public static long CARD_ID;
        public static long CARD_CODE;
        public static string CARD_NAME;
        public static string CARD_DATE;
        public static string CARD_STATE;
        public static int CARD_PER;
        public static string CARD_NOTE;
        public static long COM_ID;
        public static long CLI_ID;
        public static CARD_TBLTableAdapter CARD_DATATABLE = new CARD_TBLTableAdapter();

        public DataTable CARD_List()
        {
            DataTable dt = new DataTable();
            dt = clsCARD.CARD_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_CARD(string S_CARD_NAME)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsCARD.CARD_DATATABLE.CARDbyCARD_NAME(S_CARD_NAME);
            if (dt.Rows.Count == 1)
            {
                CARD_ID = Convert.ToInt64(dt.Rows[0]["CARD_ID"]);
                CARD_CODE = Convert.ToInt64(dt.Rows[0]["CARD_CODE"]);
                CARD_NAME = (dt.Rows[0]["CARD_NAME"]).ToString();
                CARD_DATE = string.Format(dt.Rows[0]["CARD_DATE"].ToString(), "dd/MM/yyyy");
                CARD_STATE = (dt.Rows[0]["CARD_STATE"]).ToString();
                CARD_PER = Convert.ToInt16(dt.Rows[0]["CARD_PER"]);
                CARD_NOTE = (dt.Rows[0]["CARD_NOTE"]).ToString();
                COM_ID = Convert.ToInt64(dt.Rows[0]["COM_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
            else
            {
                CARD_ID = 0;
                CARD_CODE = 0;
                CARD_NAME = "";
                CARD_DATE = "";
                CARD_STATE = "";
                CARD_PER = 0;
                CARD_NOTE = "";
                COM_ID = 0;
                CLI_ID = 0;
            }
            return dt;
        }

    }
}
