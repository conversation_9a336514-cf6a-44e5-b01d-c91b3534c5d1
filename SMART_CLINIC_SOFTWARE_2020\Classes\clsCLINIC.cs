﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsCLINIC
    {
        public static long CLI_ID;
        public static long CLI_CODE;
        public static string CLI_NAME;
        public static string CLI_LOC;
        public static string CLI_NOTE;
        public static string CLI_CONNECTION;
        public static string CLI_SERVER;
        public static CLINC_TBLTableAdapter CLINIC_DATATABLE = new CLINC_TBLTableAdapter();

        public DataTable CLINIC_LIST()
        {
            DataTable dt = new DataTable();
            dt = CLINIC_DATATABLE.GetData();
            return dt;
        }
        public DataTable SELECT_CLINIC(string S_CLINIC_NAME)
        {
            DataTable dt = new DataTable();
            dt = CLINIC_DATATABLE.CLINICbyNAME(S_CLINIC_NAME);
            if (dt.Rows.Count == 1)
            {
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                CLI_CODE = Convert.ToInt64(dt.Rows[0]["CLI_CODE"]);
                CLI_NAME = dt.Rows[0]["CLI_NAME"].ToString();
                CLI_LOC = dt.Rows[0]["CLI_LOC"].ToString();
                CLI_NOTE = dt.Rows[0]["CLI_NOTE"].ToString();
                CLI_CONNECTION = dt.Rows[0]["CLI_CONNECTION"].ToString();
                CLI_SERVER = dt.Rows[0]["CLI_SERVER"].ToString();
            }
            else
            {
                CLI_ID = 0;
                CLI_CODE = 0;
                CLI_NAME = "";
                CLI_LOC = "";
                CLI_NOTE = "";
                CLI_CONNECTION = "";
                CLI_SERVER = "";
            }
            return dt;
        }
    }
    
}
