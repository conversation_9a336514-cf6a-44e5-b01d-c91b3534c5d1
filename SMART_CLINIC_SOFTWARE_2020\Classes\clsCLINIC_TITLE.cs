﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsCLINIC_TITLE
    {
        public static long CLI_T_ID;
        public static string CLI_T_NAME;
        public static string CLI_T_TITLE;
        public static string CLI_T_ADDRESS;
        public static string CLI_T_MOBILE;
        public static string CLI_T_NOTE;
        public static long CLI_ID;

        public static CLINIC_TITLE_TBLTableAdapter CLINIC_T_DATATABLE = new CLINIC_TITLE_TBLTableAdapter();

        public DataTable CLI_T_List()
        {
            DataTable dt = new DataTable();
            dt = CLINIC_T_DATATABLE.GetData();
            if (dt.Rows.Count == 1)
            {
                CLI_T_ID = Convert.ToInt64(dt.Rows[0]["CLI_T_ID"]);
                CLI_T_NAME = (dt.Rows[0]["CLI_T_NAME"]).ToString();
                CLI_T_TITLE = (dt.Rows[0]["CLI_T_TITLE"]).ToString();
                CLI_T_ADDRESS = (dt.Rows[0]["CLI_T_ADDRESS"]).ToString();
                CLI_T_MOBILE = (dt.Rows[0]["CLI_T_MOBILE"]).ToString();
                CLI_T_NOTE = (dt.Rows[0]["CLI_T_NOTE"]).ToString();
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
           
            return dt;
        }


    }
}
