﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsCOMPANY
    {
        public static long COM_ID;
        public static long COM_CODE;
        public static string COM_NAME;
        public static string COM_ADDRESS;
        public static string COM_MOBILE;
        public static string COM_STATE;
        public static long CLI_ID;
        public static COMPANY_TBLTableAdapter COMPANY_DATATABLE = new COMPANY_TBLTableAdapter();

        public DataTable COMPANY_LIST()
        {
            DataTable dt = new DataTable();
            dt = COMPANY_DATATABLE.GetData();
            return dt;
        }

        public DataTable SELECT_COMPANY(string S_COM_NAME)
        {
            DataTable dt = new DataTable();
            dt = COMPANY_DATATABLE.COMPANYbyCOM_NAME(S_COM_NAME);
            if (dt.Rows.Count > 0)
            {
                Classes.clsCOMPANY.COM_ID = Convert.ToInt64(dt.Rows[0]["COM_ID"]);
                Classes.clsCOMPANY.COM_CODE = Convert.ToInt64(dt.Rows[0]["COM_CODE"]);
                Classes.clsCOMPANY.COM_NAME = dt.Rows[0]["COM_NAME"].ToString();
                Classes.clsCOMPANY.COM_ADDRESS = dt.Rows[0]["COM_ADDRESS"].ToString();
                Classes.clsCOMPANY.COM_MOBILE = dt.Rows[0]["COM_MOBILE"].ToString();
                Classes.clsCOMPANY.COM_STATE = dt.Rows[0]["COM_STATE"].ToString();
                Classes.clsCOMPANY.CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
            else
            {
                Classes.clsCOMPANY.COM_ID = 0;
                Classes.clsCOMPANY.COM_CODE = 0;
                Classes.clsCOMPANY.COM_NAME = "";
                Classes.clsCOMPANY.COM_ADDRESS = "";
                Classes.clsCOMPANY.COM_MOBILE = "";
                Classes.clsCOMPANY.COM_STATE = "";
                Classes.clsCOMPANY.CLI_ID = 0;
            }
            return dt;
        }
    }
}
