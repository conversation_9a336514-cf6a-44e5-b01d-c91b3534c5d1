﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;


namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsCUST
    {
        public static long CUST_ID;
        public static long CUST_CODE;
        public static string CUST_FULL_NAME;
        public static string CUST_F_NAME;
        public static string CUST_S_NAME;
        public static string CUST_T_NAME;
        public static string CUST_L_NAME;
        public static string CUST_AGE;
        public static string CUST_BD;
        public static string CUST_MOBILE1;
        public static string CUST_MOBILE2;
        public static string CUST_ADDRESS;
        public static string CUST_SAVE_STATE;
        public static string CUST_NOTE;
        public static long CARD_ID;
        public static long CLI_ID;
        public static string CUST_GENDER;
        public static string CUST_NATION;
        public static string CUST_AGE_MONTH;

        public static CUST_TBLTableAdapter CUST_DATATABLE = new CUST_TBLTableAdapter();
        public static CUST_DATA_LISTTableAdapter CUST_DATA_LIST = new CUST_DATA_LISTTableAdapter();
        public static int Years;
        public static int Months;
        public DataTable CUST_List()
        {
            DataTable dt = new DataTable();
            dt = clsCUST.CUST_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_CUST(string S_CUST_CODE, string S_CUST_NAME)
        {
            DataTable dt = new DataTable();
            dt = CUST_DATATABLE.CUSTbyCUST_NAMEorCUST_CODE(S_CUST_CODE, S_CUST_NAME);
            if (dt.Rows.Count > 0)
            {
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                CUST_CODE = Convert.ToInt64(dt.Rows[0]["CUST_CODE"]);
                CUST_F_NAME = (dt.Rows[0]["CUST_F_NAME"]).ToString();
                CUST_S_NAME = (dt.Rows[0]["CUST_S_NAME"]).ToString();
                CUST_T_NAME = (dt.Rows[0]["CUST_T_NAME"]).ToString();
                CUST_L_NAME = (dt.Rows[0]["CUST_L_NAME"]).ToString();
                CUST_FULL_NAME = CUST_F_NAME + " " + CUST_S_NAME + " " + CUST_T_NAME + " " + CUST_L_NAME;
                CUST_AGE = (dt.Rows[0]["CUST_AGE"]).ToString();
                CUST_BD = (dt.Rows[0]["CUST_BD"]).ToString();
                CUST_MOBILE1 = (dt.Rows[0]["CUST_MOBILE1"]).ToString();
                CUST_MOBILE2 = (dt.Rows[0]["CUST_MOBILE2"]).ToString();
                CUST_ADDRESS = (dt.Rows[0]["CUST_ADDRESS"]).ToString();
                CUST_SAVE_STATE = (dt.Rows[0]["CUST_SAVE_STATE"]).ToString();
                CUST_NOTE = (dt.Rows[0]["CUST_NOTE"]).ToString();
                CARD_ID = Convert.ToInt64(dt.Rows[0]["CARD_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                CUST_GENDER = (dt.Rows[0]["CUST_GENDER"]).ToString();
                CUST_NATION = (dt.Rows[0]["CUST_NATION"]).ToString();
                CUST_AGE_MONTH = (dt.Rows[0]["CUST_AGE_MONTH"]).ToString();
            }
            else
            {
                CUST_ID = 0;
                CUST_CODE = 0;
                CUST_F_NAME = "";
                CUST_S_NAME = "";
                CUST_T_NAME = "";
                CUST_L_NAME = "";
                CUST_FULL_NAME = "";
                CUST_AGE = "";
                CUST_BD = "";
                CUST_MOBILE1 = "";
                CUST_MOBILE2 = "";
                CUST_ADDRESS = "";
                CUST_SAVE_STATE = "";
                CUST_NOTE = "";
                CARD_ID = 0;
                CLI_ID = 0;
                CUST_GENDER = "";
                CUST_NATION = "";
                CUST_AGE_MONTH = "";
            }
            return dt;
        }

        public DataTable CUST_CARD_DATA(long S_CUST_ID, long S_CLI_ID)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsCUST.CUST_DATATABLE.CUST_CARD_DATA(S_CUST_ID, S_CLI_ID);
            return dt;
        }

        public string CalculateAge(DateTime Dob)
        {
            DateTime Now = DateTime.Now;
            Years = new DateTime(DateTime.Now.Subtract(Dob).Ticks).Year - 1;
            DateTime PastYearDate = Dob.AddYears(Years);
            Months = 0;
            for (int i = 1; i <= 12; i++)
            {
                if (PastYearDate.AddMonths(i) == Now)
                {
                    Months = i;
                    break;
                }
                else if (PastYearDate.AddMonths(i) >= Now)
                {
                    Months = i - 1;
                    break;
                }
            }
            int Days = Now.Subtract(PastYearDate.AddMonths(Months)).Days;
            int Hours = Now.Subtract(PastYearDate).Hours;
            int Minutes = Now.Subtract(PastYearDate).Minutes;
            int Seconds = Now.Subtract(PastYearDate).Seconds;
            return string.Format("" + Years, Months, Days);

        }
        public DataTable Select_CUST_ORDER(string S_CUST_ID, string S_CUST_NAME)
        {
            DataTable dt = new DataTable();
            dt = CUST_DATATABLE.CUSTbyCUST_IDorCUST_NAME(S_CUST_ID, S_CUST_NAME);
            if (dt.Rows.Count > 0)
            {
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                CUST_CODE = Convert.ToInt64(dt.Rows[0]["CUST_CODE"]);
                CUST_F_NAME = (dt.Rows[0]["CUST_F_NAME"]).ToString();
                CUST_S_NAME = (dt.Rows[0]["CUST_S_NAME"]).ToString();
                CUST_T_NAME = (dt.Rows[0]["CUST_T_NAME"]).ToString();
                CUST_L_NAME = (dt.Rows[0]["CUST_L_NAME"]).ToString();
                CUST_FULL_NAME = CUST_F_NAME + " " + CUST_S_NAME + " " + CUST_T_NAME + " " + CUST_L_NAME;
                CUST_AGE = (dt.Rows[0]["CUST_AGE"]).ToString();
                CUST_BD = (dt.Rows[0]["CUST_BD"]).ToString();
                CUST_MOBILE1 = (dt.Rows[0]["CUST_MOBILE1"]).ToString();
                CUST_MOBILE2 = (dt.Rows[0]["CUST_MOBILE2"]).ToString();
                CUST_ADDRESS = (dt.Rows[0]["CUST_ADDRESS"]).ToString();
                CUST_SAVE_STATE = (dt.Rows[0]["CUST_SAVE_STATE"]).ToString();
                CUST_NOTE = (dt.Rows[0]["CUST_NOTE"]).ToString();
                CARD_ID = Convert.ToInt64(dt.Rows[0]["CARD_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                CUST_GENDER = (dt.Rows[0]["CUST_GENDER"]).ToString();
                CUST_NATION = (dt.Rows[0]["CUST_NATION"]).ToString();
                CUST_AGE_MONTH = (dt.Rows[0]["CUST_AGE_MONTH"]).ToString();
            }
            else
            {
                CUST_ID = 0;
                CUST_CODE = 0;
                CUST_F_NAME = "";
                CUST_S_NAME = "";
                CUST_T_NAME = "";
                CUST_L_NAME = "";
                CUST_FULL_NAME = "";
                CUST_AGE = "";
                CUST_BD = "";
                CUST_MOBILE1 = "";
                CUST_MOBILE2 = "";
                CUST_ADDRESS = "";
                CUST_SAVE_STATE = "";
                CUST_NOTE = "";
                CARD_ID = 0;
                CLI_ID = 0;
                CUST_GENDER = "";
                CUST_NATION = "";
                CUST_AGE_MONTH = "";
            }
            return dt;
        }
    }
}
