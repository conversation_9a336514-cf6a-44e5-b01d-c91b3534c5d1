﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsDATABASE_CONNECTION
    {
        public void DATABASE_CONFIG(string S_CONNECTION)
        {
            Classes.clsAPO.APO_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsCARD.CARD_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsCLINIC.CLINIC_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsCOMPANY.COMPANY_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsCUST.CUST_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsCUST.CUST_DATA_LIST.Connection.ConnectionString = S_CONNECTION;
            Classes.clsDES.DES_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsDIAGNOIS.Diagnois_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsDIGLIST.DIGLIST_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsDOCTORS.DOCTABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsDOS.DOS_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsHOLIDAY.HOL_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsMEDCHEK.MEDCHEK_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsMEDCIN.MEDCIN_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsMEDLIST.MEDLIST_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsMEDREP.MEDREP_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsMEDREQ.MEDREQ_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsORDER.ORDER_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsSERLIST.SERLIST_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsSERVICE.SERVICE_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsTRANSACTION.T_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsTRANS_VIS_DATA.TRANS_VIS_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsUSERS.USER_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsVISIT.VISIT_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsCLINIC_TITLE.CLINIC_T_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsUSER_TYPE.USER_T_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsUSER_PER.USER_P_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsUSER_PER.USER_P_NAME_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsENDO.ENDO_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsSTOCK_DATA.STOCK_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsStock_AddMoney.STOCK_INSERT_DATATABLE.Connection.ConnectionString = S_CONNECTION;
            Classes.clsStock_PullMoney.STOCK_PULL_DATATABLE.Connection.ConnectionString = S_CONNECTION;

        }
    }
}
