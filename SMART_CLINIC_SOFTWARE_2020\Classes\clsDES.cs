﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsDES
    {
        public static long DES_ID;
        public static long DES_CODE;
        public static string DES_NAME;
        public static string DES_TYPE;
        public static string DES_NOTE;
        public static string DES_STATE;
        public static long CLI_ID;
        public static long CUST_ID;

        public static DES_TBLTableAdapter DES_DATATABLE = new DES_TBLTableAdapter();

        public DataTable DES_List()
        {
            DataTable dt = new DataTable();
            dt = clsDES.DES_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_DES(long S_DES_CODE)
        {
            DataTable dt = new DataTable();
            dt = DES_DATATABLE.DESbyDES_CODE(S_DES_CODE);
            if (dt.Rows.Count == 1)
            {
                DES_ID = Convert.ToInt64(dt.Rows[0]["DES_ID"]);
                DES_CODE = Convert.ToInt64(dt.Rows[0]["DES_CODE"]);
                DES_NAME = (dt.Rows[0]["DES_NAME"]).ToString();
                DES_TYPE = (dt.Rows[0]["DES_TYPE"]).ToString();
                DES_NOTE = (dt.Rows[0]["DES_NOTE"]).ToString();
                DES_STATE = (dt.Rows[0]["DES_STATE"]).ToString();
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
            }
            else
            {
                DES_ID = 0;
                DES_CODE = 0;
                DES_NAME = "";
                DES_TYPE = "";
                DES_NOTE = "";
                DES_STATE = "";
                CLI_ID = 0;
                CUST_ID = 0;
            }
            return dt;
        }
    }
}
