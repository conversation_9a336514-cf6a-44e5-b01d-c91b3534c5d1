﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsDIAGNOIS
    {
        public static long DIG_ID;
        public static long DIG_CODE;
        public static string DIG_NAME;
        public static string DIG_TYPE;
        public static string DIG_NOTE;
        public static long CLI_ID;

        public static DIAGNOIS_TBLTableAdapter Diagnois_DATATABLE = new DIAGNOIS_TBLTableAdapter();

        public DataTable Diagnois_List()
        {
            DataTable dt = new DataTable();
            dt = clsDIAGNOIS.Diagnois_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_DIAGNOIS(string S_DIG_NAME)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsDIAGNOIS.Diagnois_DATATABLE.DIAGNOISbyDIG_NAME(S_DIG_NAME);
            if (dt.Rows.Count > 0)
            {
                Classes.clsDIAGNOIS.DIG_ID = Convert.ToInt64(dt.Rows[0]["DIG_ID"]);
                Classes.clsDIAGNOIS.DIG_CODE = Convert.ToInt64(dt.Rows[0]["DIG_CODE"]);
                Classes.clsDIAGNOIS.DIG_NAME = (dt.Rows[0]["DIG_NAME"]).ToString();
                Classes.clsDIAGNOIS.DIG_TYPE = (dt.Rows[0]["DIG_TYPE"]).ToString();
                Classes.clsDIAGNOIS.DIG_NOTE = (dt.Rows[0]["DIG_NOTE"]).ToString();
                Classes.clsDIAGNOIS.CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
            else
            {
                Classes.clsDIAGNOIS.DIG_ID = 0;
                Classes.clsDIAGNOIS.DIG_CODE = 0;
                Classes.clsDIAGNOIS.DIG_NAME = "";
                Classes.clsDIAGNOIS.DIG_TYPE = "";
                Classes.clsDIAGNOIS.DIG_NOTE = "";
                Classes.clsDIAGNOIS.CLI_ID = 0;
            }
            return dt;
        }

    }
}
