﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsDIGLIST
    {
        public static long DIGLIST_ID;
        public static long DIGLIST_CODE;
        public static string DIGLIST_NAME;
        public static string DIGLIST_DATE;
        public static string DIGLIST_TIME;
        public static long DIG_ID;
        public static long CUST_ID;
        public static long VIS_ID;
        public static long CLI_ID;
        public static DIGLIST_TBLTableAdapter DIGLIST_DATATABLE = new DIGLIST_TBLTableAdapter();

        public DataTable DIG_List()
        {
            DataTable dt = new DataTable();
            dt = clsDIGLIST.DIGLIST_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_DIGLIST(long S_DIGLIST_CODE)
        {
            DataTable dt = new DataTable();
            dt = DIGLIST_DATATABLE.DIGLISTbyDIGLIST_CODE(S_DIGLIST_CODE);
            if (dt.Rows.Count == 1)
            {
                DIGLIST_ID = Convert.ToInt64(dt.Rows[0]["DIGLIST_ID"]);
                DIGLIST_CODE = Convert.ToInt64(dt.Rows[0]["DIGLIST_CODE"]);
                DIGLIST_NAME = (dt.Rows[0]["DIGLIST_NAME"]).ToString();
                DIGLIST_DATE = (dt.Rows[0]["DIGLIST_DATE"]).ToString();
                DIGLIST_TIME = (dt.Rows[0]["DIGLIST_TIME"]).ToString();
                DIG_ID = Convert.ToInt64(dt.Rows[0]["DIG_ID"]);
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                VIS_ID = Convert.ToInt64(dt.Rows[0]["VIS_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
            else
            {
                DIGLIST_ID = 0;
                DIGLIST_CODE = 0;
                DIGLIST_NAME = "";
                DIGLIST_DATE = "";
                DIGLIST_TIME = "";
                DIG_ID = 0;
                CUST_ID = 0;
                VIS_ID = 0;
                CLI_ID = 0;
            }
            return dt;
        }

    }
}
