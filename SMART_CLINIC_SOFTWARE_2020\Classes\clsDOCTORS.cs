﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsDOCTORS
    {
        public static long DOC_ID;
        public static long DOC_CODE;
        public static string DOC_NAME;
        public static string DOC_MAJOR;
        public static string DOC_EXP;
        public static string DOC_BD;
        public static string DOC_MOBILE;
        public static string DOC_ADDRESS;
        public static long CLI_ID;
        public static long USER_ID;

        public static long DOC_LOG_ID;
        public static long DOC_LOG_CODE;
        public static string DOC_LOG_NAME;
        public static string DOC_LOG_MOBILE;
        public static string DOC_LOG_ADDRESS;
        public static DOCTORS_TBLTableAdapter DOCTABLE = new DOCTORS_TBLTableAdapter();

        public DataTable DOC_LIST()
        {
            DataTable dt = new DataTable();
            dt = clsDOCTORS.DOCTABLE.GetData();
            return dt;
        }

        public DataTable SELECT_DOC (string S_DOC_NAME)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsDOCTORS.DOCTABLE.DOC_LISTbyDOC_NAME(S_DOC_NAME);
            if (dt.Rows.Count == 1)
            {
                Classes.clsDOCTORS.DOC_ID = Convert.ToInt64(dt.Rows[0]["DOC_ID"]); 
                Classes.clsDOCTORS.DOC_CODE = Convert.ToInt64(dt.Rows[0]["DOC_CODE"]); 
                Classes.clsDOCTORS.DOC_NAME = (dt.Rows[0]["DOC_NAME"]).ToString(); 
                Classes.clsDOCTORS.DOC_MAJOR = (dt.Rows[0]["DOC_MAJOR"]).ToString(); 
                Classes.clsDOCTORS.DOC_EXP = (dt.Rows[0]["DOC_EXP"]).ToString(); 
                Classes.clsDOCTORS.DOC_BD = (dt.Rows[0]["DOC_BD"]).ToString(); 
                Classes.clsDOCTORS.DOC_MOBILE = (dt.Rows[0]["DOC_MOBILE"]).ToString(); 
                Classes.clsDOCTORS.DOC_ADDRESS = (dt.Rows[0]["DOC_ADDRESS"]).ToString(); 
                Classes.clsDOCTORS.CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]); 
                Classes.clsDOCTORS.USER_ID = Convert.ToInt64(dt.Rows[0]["USER_ID"]); 
            }
            else
            {
                Classes.clsDOCTORS.DOC_ID = 0;
                Classes.clsDOCTORS.DOC_CODE = 0;
                Classes.clsDOCTORS.DOC_NAME = "";
                Classes.clsDOCTORS.DOC_MAJOR = "";
                Classes.clsDOCTORS.DOC_EXP = "";
                Classes.clsDOCTORS.DOC_BD = "";
                Classes.clsDOCTORS.DOC_MOBILE = "";
                Classes.clsDOCTORS.DOC_ADDRESS = "";
                Classes.clsDOCTORS.CLI_ID = 0;
                Classes.clsDOCTORS.USER_ID = 0;
            }
               
            return dt;
        }
        public DataTable DOC_LIST_BY_USER_ID(long USER_ID)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsDOCTORS.DOCTABLE.DOCLISTbyUSER_ID(USER_ID);
            if (dt.Rows.Count > 0)
            {
                Classes.clsDOCTORS.DOC_LOG_ID = Convert.ToInt64(dt.Rows[0]["DOC_ID"]);
                Classes.clsDOCTORS.DOC_LOG_CODE = Convert.ToInt64(dt.Rows[0]["DOC_CODE"]);
                Classes.clsDOCTORS.DOC_LOG_NAME = (dt.Rows[0]["DOC_NAME"]).ToString();
                Classes.clsDOCTORS.DOC_LOG_MOBILE = (dt.Rows[0]["DOC_MOBILE"]).ToString();
                Classes.clsDOCTORS.DOC_LOG_ADDRESS = (dt.Rows[0]["DOC_ADDRESS"]).ToString();
                Classes.clsDOCTORS.DOC_ID = Convert.ToInt64(dt.Rows[0]["DOC_ID"]);
                Classes.clsDOCTORS.DOC_CODE = Convert.ToInt64(dt.Rows[0]["DOC_CODE"]);
                Classes.clsDOCTORS.DOC_NAME = (dt.Rows[0]["DOC_NAME"]).ToString();
                Classes.clsDOCTORS.DOC_MAJOR = (dt.Rows[0]["DOC_MAJOR"]).ToString();
                Classes.clsDOCTORS.DOC_EXP = (dt.Rows[0]["DOC_EXP"]).ToString();
                Classes.clsDOCTORS.DOC_BD = (dt.Rows[0]["DOC_BD"]).ToString();
                Classes.clsDOCTORS.DOC_MOBILE = (dt.Rows[0]["DOC_MOBILE"]).ToString();
                Classes.clsDOCTORS.DOC_ADDRESS = (dt.Rows[0]["DOC_ADDRESS"]).ToString();
                Classes.clsDOCTORS.CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                Classes.clsDOCTORS.USER_ID = Convert.ToInt64(dt.Rows[0]["USER_ID"]);
            }
            else
            {
                Classes.clsDOCTORS.DOC_LOG_ID = 0;
                Classes.clsDOCTORS.DOC_LOG_CODE = 0;
                Classes.clsDOCTORS.DOC_LOG_NAME = "";
                Classes.clsDOCTORS.DOC_LOG_MOBILE = "";
                Classes.clsDOCTORS.DOC_LOG_ADDRESS = "";
                Classes.clsDOCTORS.DOC_ID = 0;
                Classes.clsDOCTORS.DOC_CODE = 0;
                Classes.clsDOCTORS.DOC_NAME = "";
                Classes.clsDOCTORS.DOC_MAJOR = "";
                Classes.clsDOCTORS.DOC_EXP = "";
                Classes.clsDOCTORS.DOC_BD = "";
                Classes.clsDOCTORS.DOC_MOBILE = "";
                Classes.clsDOCTORS.DOC_ADDRESS = "";
                Classes.clsDOCTORS.CLI_ID = 0;
                Classes.clsDOCTORS.USER_ID = 0;
            }
            return dt;
        }

    }
}
