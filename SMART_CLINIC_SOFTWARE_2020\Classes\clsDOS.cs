﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsDOS
    {
        public static long DOS_ID;
        public static long DOS_CODE;
        public static string DOS_NAME;
        public static string DOS_TYPE;
        public static long CLI_ID;

        public static DOS_TBLTableAdapter DOS_DATATABLE = new DOS_TBLTableAdapter();

        public DataTable DOS_List()
        {
            DataTable dt = new DataTable();
            dt = clsDOS.DOS_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_DOS(string S_DOS_NAME, string S_DOS_TYPE)
        {
            DataTable dt = new DataTable();
            dt = DOS_DATATABLE.DOS_LISTbyDOS_NAMEorDOS_CODE(S_DOS_NAME, S_DOS_TYPE);
            if (dt.Rows.Count == 1)
            {
                DOS_ID = Convert.ToInt64(dt.Rows[0]["DOS_ID"]);
                DOS_CODE = Convert.ToInt64(dt.Rows[0]["DOS_CODE"]);
                DOS_NAME = (dt.Rows[0]["DOS_NAME"]).ToString();
                DOS_TYPE = (dt.Rows[0]["DOS_TYPE"]).ToString();
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
            else
            {
                DOS_ID = 0;
                DOS_CODE = 0;
                DOS_NAME = "";
                DOS_TYPE = "";
                CLI_ID = 0;
            }
            return dt;
        }
    }
}
