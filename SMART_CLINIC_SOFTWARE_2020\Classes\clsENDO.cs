﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;


namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsENDO
    {
        public static long ENDO_ID;
        public static long ENDO_CODE;
        public static string TYPE_VISIT;
        public static string ENDO_DATE;
        public static string ENDO_TIME;
        public static string ONE_CANAL;
        public static string MB;
        public static string ML;
        public static string D;
        public static string P;
        public static string DB;
        public static string B;
        public static string M;
        public static string COST_VISIT;
        public static string NOTE;
        public static long CUST_ID;
        public static long VIS_ID;
        public static long CLI_ID;

        public static ENDO_TBLTableAdapter ENDO_DATATABLE = new ENDO_TBLTableAdapter();

        public DataTable ENDO_LIST()
        {
            DataTable dt = new DataTable();
            dt = ENDO_DATATABLE.GetData();
            return dt;
        }
        public DataTable SELECT_ENDO(long S_ENDO_CODE)
        {
            DataTable dt = new DataTable();
            dt = ENDO_DATATABLE.ENDObyENDO_CODE(S_ENDO_CODE);
            if (dt.Rows.Count == 1)
            {
                ENDO_ID = Convert.ToInt64(dt.Rows[0]["ENDO_ID"]);
                ENDO_CODE = Convert.ToInt64(dt.Rows[0]["ENDO_CODE"]);
                TYPE_VISIT = dt.Rows[0]["TYPE_VISIT"].ToString();
                ENDO_DATE = string.Format(dt.Rows[0]["ENDO_DATE"].ToString(), "dd/MM/yyyy");
                ENDO_TIME = (dt.Rows[0]["ENDO_TIME"]).ToString();
                ONE_CANAL = dt.Rows[0]["ONE_CANAL"].ToString();
                MB = dt.Rows[0]["MB"].ToString();
                ML = dt.Rows[0]["ML"].ToString();
                D = dt.Rows[0]["D"].ToString();
                P = dt.Rows[0]["P"].ToString();
                DB = dt.Rows[0]["DB"].ToString();
                B = dt.Rows[0]["B"].ToString();
                M = dt.Rows[0]["M"].ToString();
                COST_VISIT = dt.Rows[0]["COST_VISIT"].ToString();
                NOTE = dt.Rows[0]["NOTE"].ToString();
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                VIS_ID = Convert.ToInt64(dt.Rows[0]["VIS_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
            else
            {
                ENDO_ID = 0;
                ENDO_CODE = 0;
                TYPE_VISIT = "";
                ENDO_DATE = "";
                ENDO_TIME = "";
                ONE_CANAL = "";
                MB = "";
                ML = "";
                D = "";
                P = "";
                DB = "";
                B = "";
                M = "";
                COST_VISIT = "";
                NOTE = "";
                CUST_ID = 0;
                VIS_ID = 0;
                CLI_ID = 0;
            }
            return dt;
        }
    }
}
