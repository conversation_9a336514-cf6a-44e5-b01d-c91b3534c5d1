﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsHOLIDAY
    {
        public static long HOL_ID;
        public static long HOL_CODE;
        public static string HOL_DATE;
        public static string HOL_TIME;
        public static string HOL_NAME;
        public static string HOL_TEXT;
        public static string HOL_NOTE;
        public static long CUST_ID;
        public static long CLI_ID;
        public static long VIS_ID;

        public static HOLIDAY_TBLTableAdapter HOL_DATATABLE = new HOLIDAY_TBLTableAdapter();

        public DataTable HOL_List()
        {
            DataTable dt = new DataTable();
            dt = clsHOLIDAY.HOL_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_HOL(long S_HOL_CODE)
        {
            DataTable dt = new DataTable();
            dt = HOL_DATATABLE.HOLbyHOL_CODE(S_HOL_CODE);
            if (dt.Rows.Count == 1)
            {
                HOL_ID = Convert.ToInt64(dt.Rows[0]["HOL_ID"]);
                HOL_CODE = Convert.ToInt64(dt.Rows[0]["HOL_CODE"]);
                HOL_DATE = (dt.Rows[0]["HOL_DATE"]).ToString();
                HOL_TIME = (dt.Rows[0]["HOL_TIME"]).ToString();
                HOL_NAME = (dt.Rows[0]["HOL_NAME"]).ToString();
                HOL_TEXT = (dt.Rows[0]["HOL_TEXT"]).ToString();
                HOL_NOTE = (dt.Rows[0]["HOL_NOTE"]).ToString();
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                VIS_ID = Convert.ToInt64(dt.Rows[0]["VIS_ID"]);
            }
            else
            {
                HOL_ID = 0;
                HOL_CODE = 0;
                HOL_DATE = "";
                HOL_TIME = "";
                HOL_NAME = "";
                HOL_TEXT = "";
                HOL_NOTE = "";
                CUST_ID = 0;
                CLI_ID = 0;
                VIS_ID = 0;
            }
            return dt;
        }
    }
}
