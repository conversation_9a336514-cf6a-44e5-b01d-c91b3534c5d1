﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsMEDCHEK
    {
        public static long MEDCHEK_ID;
        public static long MEDCHEK_CODE;
        public static string MEDCHEK_NAME;
        public static string MEDCHEK_TYPE;
        public static decimal MEDCHEK_PRICE;
        public static string MEDCHEK_NOTE;
        public static long CLI_ID;

        public static MEDCHEK_TBLTableAdapter MEDCHEK_DATATABLE = new MEDCHEK_TBLTableAdapter();

        public DataTable MEDCHEK_List()
        {
            DataTable dt = new DataTable();
            dt = clsMEDCHEK.MEDCHEK_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_MEDCHEK(string S_MEDCHEK_NAME)
        {
            DataTable dt = new DataTable();
            dt = MEDCHEK_DATATABLE.MEDCHEKbyMEDCHEK_NAME(S_MEDCHEK_NAME);
            if (dt.Rows.Count == 1)
            {
                MEDCHEK_ID = Convert.ToInt64(dt.Rows[0]["MEDCHEK_ID"]);
                MEDCHEK_CODE = Convert.ToInt64(dt.Rows[0]["MEDCHEK_CODE"]);
                MEDCHEK_NAME = (dt.Rows[0]["MEDCHEK_NAME"]).ToString();
                MEDCHEK_TYPE = (dt.Rows[0]["MEDCHEK_TYPE"]).ToString();
                MEDCHEK_PRICE = Convert.ToDecimal(dt.Rows[0]["MEDCHEK_PRICE"]);
                MEDCHEK_NOTE = (dt.Rows[0]["MEDCHEK_NOTE"]).ToString();
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
            else
            {
                MEDCHEK_ID = 0;
                MEDCHEK_CODE = 0;
                MEDCHEK_NAME = "";
                MEDCHEK_TYPE = "";
                MEDCHEK_PRICE = 0;
                MEDCHEK_NOTE = "";
                CLI_ID = 0;
            }
            return dt;
        }
    }
}
