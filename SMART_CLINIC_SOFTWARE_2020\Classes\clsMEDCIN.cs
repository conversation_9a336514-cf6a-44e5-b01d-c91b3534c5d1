﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsMEDCIN
    {
        public static long MED_ID;
        public static long MED_CODE;
        public static string MED_NAME;
        public static string MED_S_NAME;
        public static string MED_SOURSE;
        public static decimal MED_PRICE;
        public static long CLI_ID;
        public static MEDCIN_TBLTableAdapter MEDCIN_DATATABLE = new MEDCIN_TBLTableAdapter();


        public DataTable MEDCIN_List()
        {
            DataTable dt = new DataTable();
            dt = clsMEDCIN.MEDCIN_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_MEDCIN(string S_MED_NAME)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsMEDCIN.MEDCIN_DATATABLE.MEDCINbyMED_NAME(S_MED_NAME);
            if (dt.Rows.Count == 1)
            {
                Classes.clsMEDCIN.MED_ID = Convert.ToInt64(dt.Rows[0]["MED_ID"]);
                Classes.clsMEDCIN.MED_CODE = Convert.ToInt64(dt.Rows[0]["MED_CODE"]);
                Classes.clsMEDCIN.MED_NAME = (dt.Rows[0]["MED_NAME"]).ToString();
                Classes.clsMEDCIN.MED_S_NAME = (dt.Rows[0]["MED_S_NAME"]).ToString();
                Classes.clsMEDCIN.MED_SOURSE = (dt.Rows[0]["MED_SOURSE"]).ToString();
                Classes.clsMEDCIN.MED_PRICE = Convert.ToDecimal(dt.Rows[0]["MED_PRICE"]);
                Classes.clsMEDCIN.CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
            else
            {
                Classes.clsMEDCIN.MED_ID = 0;
                Classes.clsMEDCIN.MED_CODE = 0;
                Classes.clsMEDCIN.MED_NAME = "";
                Classes.clsMEDCIN.MED_S_NAME = "";
                Classes.clsMEDCIN.MED_SOURSE = "";
                Classes.clsMEDCIN.MED_PRICE = 0;
                Classes.clsMEDCIN.CLI_ID = 0;
            }
            return dt;
        }
    }
}
