﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsMEDLIST
    {
        public static long MEDLIST_ID;
        public static long MEDLIST_CODE;
        public static string MEDLIST_NAME;
        public static string MEDLIST_DATE;
        public static string MEDLIST_TIME;
        public static string DOS_NAME;
        public static long MED_ID;
        public static long CUST_ID;
        public static long VIS_ID;
        public static string MED_SOURSE;

        public static MEDLIST_TBLTableAdapter MEDLIST_DATATABLE = new MEDLIST_TBLTableAdapter();

        public DataTable MEDLIST_List()
        {
            DataTable dt = new DataTable();
            dt = clsMEDLIST.MEDLIST_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_MEDLIST(long S_MEDLIST_ID)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsMEDLIST.MEDLIST_DATATABLE.MEDLISTbyMEDLIST_ID(S_MEDLIST_ID);
            if (dt.Rows.Count == 1)
            {
                MEDLIST_ID = Convert.ToInt64(dt.Rows[0]["MEDLIST_ID"]);
                MEDLIST_CODE = Convert.ToInt64(dt.Rows[0]["MEDLIST_CODE"]);
                MEDLIST_NAME = (dt.Rows[0]["MEDLIST_NAME"]).ToString();
                MEDLIST_DATE = (dt.Rows[0]["MEDLIST_DATE"]).ToString();
                MEDLIST_TIME = (dt.Rows[0]["MEDLIST_TIME"]).ToString();
                DOS_NAME = (dt.Rows[0]["DOS_NAME"]).ToString();
                MED_ID = Convert.ToInt64(dt.Rows[0]["MED_ID"]);
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                VIS_ID = Convert.ToInt64(dt.Rows[0]["VIS_ID"]);
                MED_SOURSE = (dt.Rows[0]["MED_SOURSE"]).ToString();
            }
            else
            {
                MEDLIST_ID = 0;
                MEDLIST_CODE = 0;
                MEDLIST_NAME = "";
                MEDLIST_DATE = "";
                MEDLIST_TIME = "";
                DOS_NAME = "";
                MED_ID = 0;
                CUST_ID = 0;
                VIS_ID = 0;
                MED_SOURSE = "";
            }
            return dt;
        }

    }
}
