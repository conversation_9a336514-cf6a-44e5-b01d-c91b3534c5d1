﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsMEDREP
    {
        public static long MREP_ID;
        public static long MREP_CODE;
        public static string MREP_DATE;
        public static string MREP_TIME;
        public static string MREP_NAME;
        public static string MREP_TEXT;
        public static string MREP_NOTE;
        public static long CUST_ID;
        public static long CLI_ID;
        public static long VIS_ID;

        public static MEDREP_TBLTableAdapter MEDREP_DATATABLE = new MEDREP_TBLTableAdapter();

        public DataTable MREP_List()
        {
            DataTable dt = new DataTable();
            dt = clsMEDREP.MEDREP_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_MEDREP(long S_MREP_ID)
        {
            DataTable dt = new DataTable();
            dt = MEDREP_DATATABLE.MREPbyMREP_ID(S_MREP_ID);
            if (dt.Rows.Count == 1)
            {
                MREP_ID = Convert.ToInt64(dt.Rows[0]["MREP_ID"]);
                MREP_CODE = Convert.ToInt64(dt.Rows[0]["MREP_CODE"]);
                MREP_DATE = (dt.Rows[0]["MREP_DATE"]).ToString();
                MREP_TIME = (dt.Rows[0]["MREP_TIME"]).ToString();
                MREP_NAME = (dt.Rows[0]["MREP_NAME"]).ToString();
                MREP_TEXT = (dt.Rows[0]["MREP_TEXT"]).ToString();
                MREP_NOTE = (dt.Rows[0]["MREP_NOTE"]).ToString();
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                VIS_ID = Convert.ToInt64(dt.Rows[0]["VIS_ID"]);
            }
            else
            {
                MREP_ID = 0;
                MREP_CODE = 0;
                MREP_DATE = "";
                MREP_TIME = "";
                MREP_NAME = "";
                MREP_TEXT = "";
                MREP_NOTE = "";
                CUST_ID = 0;
                CLI_ID = 0;
                VIS_ID = 0;
            }
            return dt;
        }

    }
}
