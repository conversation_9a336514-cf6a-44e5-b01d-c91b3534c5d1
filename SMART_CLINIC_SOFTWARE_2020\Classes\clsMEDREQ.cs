﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsMEDREQ
    {
        public static long MEDREQ_ID;
        public static long MEDREQ_CODE;
        public static string MEDREQ_NAME;
        public static string MEDREQ_DATE;
        public static string MEDREQ_TIME;
        public static string MEDREQ_RESULT;
        public static string MEDREQ_NOTE;
        public static long MEDCHEK_ID;
        public static long CUST_ID;
        public static long CLI_ID;
        public static long VIS_ID;
        public static string MEDREQ_STATE;

        public static MEDREQ_TBLTableAdapter MEDREQ_DATATABLE = new MEDREQ_TBLTableAdapter();

        public DataTable MEDREQ_List()
        {
            DataTable dt = new DataTable();
            dt = clsMEDREQ.MEDREQ_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_MEDREQ(long S_MEDREQ_CODE)
        {
            DataTable dt = new DataTable();
            dt = MEDREQ_DATATABLE.MEDREQbyMEDREQ_CODE(S_MEDREQ_CODE);
            if (dt.Rows.Count == 1)
            {
                MEDREQ_ID = Convert.ToInt64(dt.Rows[0]["MEDREQ_ID"]);
                MEDREQ_CODE = Convert.ToInt64(dt.Rows[0]["MEDREQ_CODE"]);
                MEDREQ_NAME = (dt.Rows[0]["MEDREQ_NAME"]).ToString();
                MEDREQ_DATE = (dt.Rows[0]["MEDREQ_DATE"]).ToString();
                MEDREQ_TIME = (dt.Rows[0]["MEDREQ_TIME"]).ToString();
                MEDREQ_RESULT = (dt.Rows[0]["MEDREQ_RESULT"]).ToString();
                MEDREQ_NOTE = (dt.Rows[0]["MEDREQ_NOTE"]).ToString();
                MEDCHEK_ID = Convert.ToInt64(dt.Rows[0]["MEDCHEK_ID"]);
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                VIS_ID = Convert.ToInt64(dt.Rows[0]["VIS_ID"]);
                MEDREQ_STATE = (dt.Rows[0]["MEDREQ_STATE"]).ToString();
            }
            else
            {
                MEDREQ_ID = 0;
                MEDREQ_CODE = 0;
                MEDREQ_NAME = "";
                MEDREQ_DATE = "";
                MEDREQ_TIME = "";
                MEDREQ_RESULT = "";
                MEDREQ_NOTE = "";
                MEDCHEK_ID = 0;
                CUST_ID = 0;
                CLI_ID = 0;
                VIS_ID = 0;
                MEDREQ_STATE = "";
            }
            return dt;
        }


        public DataTable Select_MEDREQbyMEDCHEK_ID(long S_MEDCHEK_ID, long S_VIS_ID, long S_CUST_ID)
        {
            DataTable dt = new DataTable();
            dt = MEDREQ_DATATABLE.MEDREQbyMEDCHEK_IDandVIS_IDandCUST_ID(S_MEDCHEK_ID, S_VIS_ID, S_CUST_ID);
            if (dt.Rows.Count == 1)
            {
                MEDREQ_ID = Convert.ToInt64(dt.Rows[0]["MEDREQ_ID"]);
                MEDREQ_CODE = Convert.ToInt64(dt.Rows[0]["MEDREQ_CODE"]);
                MEDREQ_NAME = (dt.Rows[0]["MEDREQ_NAME"]).ToString();
                MEDREQ_DATE = (dt.Rows[0]["MEDREQ_DATE"]).ToString();
                MEDREQ_TIME = (dt.Rows[0]["MEDREQ_TIME"]).ToString();
                MEDREQ_RESULT = (dt.Rows[0]["MEDREQ_RESULT"]).ToString();
                MEDREQ_NOTE = (dt.Rows[0]["MEDREQ_NOTE"]).ToString();
                MEDCHEK_ID = Convert.ToInt64(dt.Rows[0]["MEDCHEK_ID"]);
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                VIS_ID = Convert.ToInt64(dt.Rows[0]["VIS_ID"]);
                MEDREQ_STATE = (dt.Rows[0]["MEDREQ_STATE"]).ToString();
            }
            return dt;
        }
    }
}
