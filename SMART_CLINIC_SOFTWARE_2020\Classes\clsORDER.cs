﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsORDER
    {
        public static long ORDER_ID;
        public static long ORDER_CODE;
        public static string ORDER_DATE;
        public static string ORDER_TIME;
        public static string ORDER_NOTE;
        public static long CUST_ID;
        public static long CLI_ID;

        public static ORDER_TBLTableAdapter ORDER_DATATABLE = new ORDER_TBLTableAdapter();


        public DataTable ORDER_LIST()
        {
            DataTable dt = new DataTable();
            dt = clsORDER.ORDER_DATATABLE.ORDER_LIST();
            return dt;
        }


    }
}
