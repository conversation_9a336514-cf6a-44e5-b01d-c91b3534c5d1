﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsSERLIST
    {
        public static long SERLIST_ID;
        public static long SERLIST_CODE;
        public static string SERLIST_NAME;
        public static string SERLIST_DATE;
        public static string SERLIST_TIME;
        public static decimal SERLIST_PRICE_TOTAL;
        public static string SERLIST_NOTE;
        public static long SER_ID;
        public static long CLI_ID;
        public static long CUST_ID;
        public static long VIS_ID;

        public static SERLIST_TBLTableAdapter SERLIST_DATATABLE = new SERLIST_TBLTableAdapter();

        public DataTable SER_List()
        {
            DataTable dt = new DataTable();
            dt = clsSERLIST.SERLIST_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_SERLIST(long S_SERLIST_CODE)
        {
            DataTable dt = new DataTable();
            dt = SERLIST_DATATABLE.SERLISTbySERLIST_CODE(S_SERLIST_CODE);
            if (dt.Rows.Count == 1)
            {
                SERLIST_ID = Convert.ToInt64(dt.Rows[0]["SERLIST_ID"]);
                SERLIST_CODE = Convert.ToInt64(dt.Rows[0]["SERLIST_CODE"]);
                SERLIST_NAME = (dt.Rows[0]["SERLIST_NAME"]).ToString();
                SERLIST_DATE = (dt.Rows[0]["SERLIST_DATE"]).ToString();
                SERLIST_TIME = (dt.Rows[0]["SERLIST_TIME"]).ToString();
                SERLIST_PRICE_TOTAL = Convert.ToDecimal(dt.Rows[0]["SERLIST_PRICE_TOTAL"]);
                SERLIST_NOTE = (dt.Rows[0]["SERLIST_NOTE"]).ToString();
                SER_ID = Convert.ToInt64(dt.Rows[0]["SER_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                VIS_ID = Convert.ToInt64(dt.Rows[0]["VIS_ID"]);
            }
            else
            {
                SERLIST_ID = 0;
                SERLIST_CODE = 0;
                SERLIST_NAME = "";
                SERLIST_DATE = "";
                SERLIST_TIME = "";
                SERLIST_PRICE_TOTAL = 0;
                SERLIST_NOTE = "";
                SER_ID = 0;
                CLI_ID = 0;
                CUST_ID = 0;
                VIS_ID = 0;
            }
            return dt;
        }

    }
}
