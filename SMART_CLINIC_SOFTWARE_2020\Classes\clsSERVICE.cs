﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsSERVICE
    {
        public static long SER_ID;
        public static long SER_CODE;
        public static string SER_NAME;
        public static string SER_TYPE;
        public static string SER_PRICE;
        public static string SER_NOTE;
        public static long CLI_ID;
        public static SERVICE_TBLTableAdapter SERVICE_DATATABLE = new SERVICE_TBLTableAdapter();

        public DataTable SERVICE_List()
        {
            DataTable dt = new DataTable();
            dt = clsSERVICE.SERVICE_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_SERVICE(string S_SER_NAME)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsSERVICE.SERVICE_DATATABLE.SERVICEbySER_NAME(S_SER_NAME);
            if (dt.Rows.Count == 1)
            {
                Classes.clsSERVICE.SER_ID = Convert.ToInt64(dt.Rows[0]["SER_ID"]);
                Classes.clsSERVICE.SER_CODE = Convert.ToInt64(dt.Rows[0]["SER_CODE"]);
                Classes.clsSERVICE.SER_NAME = (dt.Rows[0]["SER_NAME"]).ToString();
                Classes.clsSERVICE.SER_TYPE = (dt.Rows[0]["SER_TYPE"]).ToString();
                Classes.clsSERVICE.SER_PRICE = (dt.Rows[0]["SER_PRICE"]).ToString();
                Classes.clsSERVICE.SER_NOTE = (dt.Rows[0]["SER_NOTE"]).ToString();
                Classes.clsSERVICE.CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
            else
            {
                Classes.clsSERVICE.SER_ID = 0;
                Classes.clsSERVICE.SER_CODE = 0;
                Classes.clsSERVICE.SER_NAME = "";
                Classes.clsSERVICE.SER_TYPE = "";
                Classes.clsSERVICE.SER_PRICE = "";
                Classes.clsSERVICE.SER_NOTE = "";
                Classes.clsSERVICE.CLI_ID = 0;
            }
            return dt;
        }

    }
}
