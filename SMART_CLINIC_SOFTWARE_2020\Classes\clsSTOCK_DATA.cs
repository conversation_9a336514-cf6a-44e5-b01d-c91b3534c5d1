﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsSTOCK_DATA
    {
        public static long Stock_ID;
        public static long Stock_Code;
        public static string Stock_Name;
        public static decimal Money;

        public static Stock_DataTableAdapter STOCK_DATATABLE = new Stock_DataTableAdapter();

        public DataTable STOCK_List()
        {
            DataTable dt = new DataTable();
            try
            {
                dt = clsSTOCK_DATA.STOCK_DATATABLE.GetData();
            }
            catch
            {
                return dt;
            }
            return dt;
        }

        public DataTable Select_STOCK(long S_Stock_Code)
        {
            DataTable dt = new DataTable();
            try
            {
                dt = STOCK_DATATABLE.STOCKbyStock_Code(S_Stock_Code);
                if (dt.Rows.Count == 1)
                {
                    Stock_ID = Convert.ToInt64(dt.Rows[0]["Stock_ID"]);
                    Stock_Code = Convert.ToInt64(dt.Rows[0]["Stock_Code"]);
                    Stock_Name = (dt.Rows[0]["Stock_Name"]).ToString();
                    Money = Convert.ToDecimal(dt.Rows[0]["Money"]);
                }
                else
                {
                    Stock_ID = 0;
                    Stock_Code = 0;
                    Stock_Name = "";
                    Money = 0;
                }
            }
            catch
            {
                return dt;
            }

            return dt;
        }

        public DataTable Select_STOCK_NAME(string S_STOCK_NAME)
        {
            DataTable dt = new DataTable();
            try
            {
                dt = STOCK_DATATABLE.Stock_DatabyStock_Name(S_STOCK_NAME);
                if (dt.Rows.Count == 1)
                {
                    Stock_ID = Convert.ToInt64(dt.Rows[0]["Stock_ID"]);
                    Stock_Code = Convert.ToInt64(dt.Rows[0]["Stock_Code"]);
                    Stock_Name = (dt.Rows[0]["Stock_Name"]).ToString();
                    Money = Convert.ToDecimal(dt.Rows[0]["Money"]);
                }
                else
                {
                    Stock_ID = 0;
                    Stock_Code = 0;
                    Stock_Name = "";
                    Money = 0;
                }
            }
            catch
            {
                return dt;
            }

            return dt;
        }
    }
}
