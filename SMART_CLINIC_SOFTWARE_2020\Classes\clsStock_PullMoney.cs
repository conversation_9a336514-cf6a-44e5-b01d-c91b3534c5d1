﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsStock_PullMoney
    {
        public static long Order_ID;
        public static long Stock_ID;
        public static decimal Money;
        public static string Date;
        public static string Name;
        public static string Type;
        public static string Reason;

        public static Stock_PullTableAdapter STOCK_PULL_DATATABLE = new Stock_PullTableAdapter();

        public DataTable STOCK_PULL_List()
        {
            DataTable dt = new DataTable();
            try
            {
                dt = clsStock_PullMoney.STOCK_PULL_DATATABLE.GetData();
            }
            catch
            {
                return dt;
            }
            return dt;
        }
    }
}
