﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsTRANSACTION
    {
        public static long T_ID;
        public static long T_CODE;
        public static string T_NAME;
        public static string T_DATE;
        public static string T_TIME;
        public static string T_TYPE;
        public static decimal T_PRICE;
        public static decimal T_DISCOUNT;
        public static decimal T_UNPAY;
        public static decimal T_TOTAL;
        public static decimal CUST_PRICE;
        public static decimal COMPANY_PRICE;
        public static string T_NOTE;
        public static string T_STATE;
        public static long VIS_ID;
        public static long CLI_ID;
        public static long COM_ID;

        public static TRANSACTION_TBLTableAdapter T_DATATABLE = new TRANSACTION_TBLTableAdapter();

        public DataTable TRANS_List()
        {
            DataTable dt = new DataTable();
            dt = clsTRANSACTION.T_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_TRANS(string S_T_NAME,long S_T_ID)
        {
            DataTable dt = new DataTable();
            dt = T_DATATABLE.TRANS_LISTbyT_NAMEandT_ID(S_T_NAME, S_T_ID);
            if (dt.Rows.Count == 1)
            {
                T_ID = Convert.ToInt64(dt.Rows[0]["T_ID"]);
                T_CODE = Convert.ToInt64(dt.Rows[0]["T_CODE"]);
                T_NAME = (dt.Rows[0]["T_NAME"]).ToString();
                T_DATE = (dt.Rows[0]["T_DATE"]).ToString();
                T_TIME = (dt.Rows[0]["T_TIME"]).ToString();
                T_TYPE = (dt.Rows[0]["T_TYPE"]).ToString();
                T_PRICE = Convert.ToDecimal(dt.Rows[0]["T_PRICE"]);
                T_DISCOUNT = Convert.ToDecimal(dt.Rows[0]["T_DISCOUNT"]);
                T_UNPAY = Convert.ToDecimal(dt.Rows[0]["T_UNPAY"]);
                T_TOTAL = Convert.ToDecimal(dt.Rows[0]["T_TOTAL"]);
                CUST_PRICE = Convert.ToDecimal(dt.Rows[0]["CUST_PRICE"]);
                COMPANY_PRICE = Convert.ToDecimal(dt.Rows[0]["COMPANY_PRICE"]);
                T_NOTE = (dt.Rows[0]["T_NOTE"]).ToString();
                T_STATE = (dt.Rows[0]["T_STATE"]).ToString();
                VIS_ID = Convert.ToInt64(dt.Rows[0]["VIS_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                COM_ID = Convert.ToInt64(dt.Rows[0]["COM_ID"]);
            }
            else
            {
                T_ID = 0;
                T_CODE = 0;
                T_NAME = "";
                T_DATE = "";
                T_TIME = "";
                T_TYPE = "";
                T_PRICE = 0;
                T_DISCOUNT = 0;
                T_UNPAY = 0;
                T_TOTAL = 0;
                CUST_PRICE = 0;
                COMPANY_PRICE = 0;
                T_NOTE = "";
                T_STATE = "";
                VIS_ID = 0;
                CLI_ID = 0;
                COM_ID = 0;
            }
            return dt;
        }
    }
}
