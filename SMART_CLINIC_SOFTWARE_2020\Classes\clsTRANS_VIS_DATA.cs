﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsTRANS_VIS_DATA
    {
        public static long VIS_ID;
        public static long VIS_CODE;
        public static string VIS_NAME;
        public static string VIS_DATE;
        public static string VIS_TIME;
        public static string VIS_TYPE;
        public static decimal VIS_PRICE;
        public static decimal VIS_DISCOUNT;
        public static decimal VIS_TOTAL;
        public static string VIS_PAY_TYPE;
        public static string CUST_NAME;
        public static long CUST_ID;
        public static long COM_ID;
        public static string COM_NAME;
        public static long CARD_ID;
        public static string CARD_NAME;
        public static long CARD_PER;
        public static decimal VIS_UNPAY;

        public static TRANS_VIS_DATATableAdapter TRANS_VIS_DATATABLE = new TRANS_VIS_DATATableAdapter();

        public DataTable TRANS_VIS_DATA(long S_VIS_ID)
        {
            DataTable dt = new DataTable();
            dt = TRANS_VIS_DATATABLE.GetData(S_VIS_ID);
            if (dt.Rows.Count > 0)
            {
                VIS_ID = Convert.ToInt64(dt.Rows[0]["VIS_ID"]);
                VIS_CODE = Convert.ToInt64(dt.Rows[0]["VIS_CODE"]);
                VIS_NAME = (dt.Rows[0]["VIS_NAME"]).ToString();
                VIS_DATE = (dt.Rows[0]["VIS_DATE"]).ToString();
                VIS_TIME = (dt.Rows[0]["VIS_TIME"]).ToString();
                VIS_TYPE = (dt.Rows[0]["VIS_TYPE"]).ToString();
                VIS_PRICE = Convert.ToDecimal(dt.Rows[0]["VIS_PRICE"]);
                VIS_DISCOUNT = Convert.ToDecimal(dt.Rows[0]["VIS_DISCOUNT"]);
                VIS_TOTAL = Convert.ToDecimal(dt.Rows[0]["VIS_TOTAL"]);
                VIS_PAY_TYPE = (dt.Rows[0]["VIS_PAY_TYPE"]).ToString();
                CUST_NAME = (dt.Rows[0]["CUST_NAME"]).ToString();
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                COM_ID = Convert.ToInt64(dt.Rows[0]["COM_ID"]);
                COM_NAME = (dt.Rows[0]["COM_NAME"]).ToString();
                CARD_ID = Convert.ToInt64(dt.Rows[0]["CARD_ID"]);
                CARD_NAME = (dt.Rows[0]["CARD_NAME"]).ToString();
                CARD_PER = Convert.ToInt64(dt.Rows[0]["CARD_PER"]);
                VIS_UNPAY = Convert.ToDecimal(dt.Rows[0]["VIS_UNPAY"]);
            }
            else
            {
                VIS_ID = 0;
                VIS_CODE = 0;
                VIS_NAME = "";
                VIS_DATE = "";
                VIS_TIME = "";
                VIS_TYPE = "";
                VIS_PRICE = 0;
                VIS_DISCOUNT = 0;
                VIS_TOTAL = 0;
                VIS_PAY_TYPE = "";
                CUST_NAME = "";
                CUST_ID = 0;
                COM_ID = 0;
                COM_NAME = "";
                CARD_ID = 0;
                CARD_NAME = "";
                CARD_PER = 0;
                VIS_UNPAY = 0;
            }
            return dt;
        }
    }
}
