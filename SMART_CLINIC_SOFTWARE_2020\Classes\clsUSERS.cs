﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsUSERS
    {
        public static long USER_ID;
        public static long USER_CODE;
        public static string USER_NAME;
        public static string USER_PASSWORD;
        public static string USER_MOBILE;
        public static string USER_ADDRESS;
        public static long USER_T_CODE;
        public static long CLI_ID;

        public static long LOG_USER_ID;
        public static long LOG_USER_CODE;
        public static string LOG_USER_NAME;
        public static long LOG_USER_T_CODE;

        public static USERS_TBLTableAdapter USER_DATATABLE = new USERS_TBLTableAdapter();
        Classes.clsDOCTORS NclsDoc = new clsDOCTORS();
        Classes.clsUSER_TYPE NclsUSER_T = new clsUSER_TYPE();
        Classes.clsUSER_PER NclsUSER_P = new clsUSER_PER();

        public DataTable User_List()
        {
            DataTable dt = new DataTable();
            dt = clsUSERS.USER_DATATABLE.GetData();
            return dt;
        }
        public DataTable Select_User(long S_USER_CODE,string S_USER_NAME)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsUSERS.USER_DATATABLE.USER_DATAbyUSER_CODEandUSER_NAME(S_USER_CODE, S_USER_NAME);
            if (dt.Rows.Count == 1)
            {
                Classes.clsUSERS.USER_ID = Convert.ToInt64(dt.Rows[0]["USER_ID"]);
                Classes.clsUSERS.USER_CODE = Convert.ToInt64(dt.Rows[0]["USER_CODE"]);
                Classes.clsUSERS.USER_NAME = (dt.Rows[0]["USER_NAME"]).ToString();
                Classes.clsUSERS.USER_PASSWORD = (dt.Rows[0]["USER_PASSWORD"]).ToString();
                Classes.clsUSERS.USER_MOBILE = (dt.Rows[0]["USER_MOBILE"]).ToString();
                Classes.clsUSERS.USER_ADDRESS = (dt.Rows[0]["USER_ADDRESS"]).ToString();
                Classes.clsUSERS.USER_T_CODE = Convert.ToInt64(dt.Rows[0]["USER_T_CODE"]);
                Classes.clsUSERS.CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
            else
            {
                Classes.clsUSERS.USER_ID = 0;
                Classes.clsUSERS.USER_CODE = 0;
                Classes.clsUSERS.USER_NAME = "";
                Classes.clsUSERS.USER_PASSWORD = "";
                Classes.clsUSERS.USER_MOBILE = "";
                Classes.clsUSERS.USER_ADDRESS = "";
                Classes.clsUSERS.USER_T_CODE = 0;
                Classes.clsUSERS.CLI_ID = 0;
            }
            return dt;
        }
        public DataTable USER_LOGIN(string LOG_USER_NAME,string LOG_USER_PASSWORD)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsUSERS.USER_DATATABLE.USER_LOGIN_DATA(LOG_USER_NAME, LOG_USER_PASSWORD);
            if (dt.Rows.Count == 1)
            {
                //USER_LOG_DATA
                Classes.clsUSERS.LOG_USER_ID = Convert.ToInt64(dt.Rows[0]["USER_ID"]);
                Classes.clsUSERS.LOG_USER_CODE = Convert.ToInt64(dt.Rows[0]["USER_CODE"]);
                Classes.clsUSERS.LOG_USER_NAME = (dt.Rows[0]["USER_NAME"]).ToString();
                Classes.clsUSERS.LOG_USER_T_CODE = Convert.ToInt64(dt.Rows[0]["USER_T_CODE"]);
            }
            else
            {
                Classes.clsUSERS.LOG_USER_ID = 0;
                Classes.clsUSERS.LOG_USER_CODE = 0;
                Classes.clsUSERS.LOG_USER_NAME = "";
                Classes.clsUSERS.LOG_USER_T_CODE = 0;
                return dt;
            }
            if (dt.Rows.Count == 1 && NclsUSER_T.SELECT_USER_T(LOG_USER_T_CODE).Rows.Count == 1)
            {
                Classes.clsUSERS.USER_ID = Convert.ToInt64(dt.Rows[0]["USER_ID"]);
                Classes.clsUSERS.USER_CODE = Convert.ToInt64(dt.Rows[0]["USER_CODE"]);
                Classes.clsUSERS.USER_NAME = (dt.Rows[0]["USER_NAME"]).ToString();
                Classes.clsUSERS.USER_PASSWORD = (dt.Rows[0]["USER_PASSWORD"]).ToString();
                Classes.clsUSERS.USER_MOBILE = (dt.Rows[0]["USER_MOBILE"]).ToString();
                Classes.clsUSERS.USER_ADDRESS = (dt.Rows[0]["USER_ADDRESS"]).ToString();
                Classes.clsUSERS.USER_T_CODE = Convert.ToInt64(dt.Rows[0]["USER_T_CODE"]);
                Classes.clsUSERS.CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                
                if (clsUSER_TYPE.USER_T_TYPE == "DOCTORS")
                {
                    NclsDoc.DOC_LIST_BY_USER_ID(LOG_USER_ID);
                }
                else
                {
                    NclsDoc.DOC_LIST_BY_USER_ID(0);
                }
                NclsUSER_P.Select_User_P(LOG_USER_T_CODE);
            }
            else
            {
                Classes.clsUSERS.USER_ID = 0;
                Classes.clsUSERS.USER_CODE = 0;
                Classes.clsUSERS.USER_NAME = "";
                Classes.clsUSERS.USER_PASSWORD = "";
                Classes.clsUSERS.USER_MOBILE = "";
                Classes.clsUSERS.USER_ADDRESS = "";
                Classes.clsUSERS.USER_T_CODE = 0;
                Classes.clsUSERS.CLI_ID = 0;
                
            }
           
            return dt;
        }

    }
}
