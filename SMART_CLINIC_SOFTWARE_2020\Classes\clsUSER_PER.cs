﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsUSER_PER
    {
        public static long USER_P_ID;
        public static long USER_P_CODE;
        public static long USER_P_M_RES;
        public static long USER_P_M_CUST;
        public static long USER_P_M_APO;
        public static long USER_P_M_DOC;
        public static long USER_P_M_MED_CAT;
        public static long USER_P_M_VIS;
        public static long USER_P_M_DIG;
        public static long USER_P_M_MED;
        public static long USER_P_M_CHECK;
        public static long USER_P_M_HOL;
        public static long USER_P_M_SAVE;
        public static long USER_P_M_ACC;
        public static long USER_P_M_REP;
        public static long USER_P_M_SET;
        public static long USER_P_C_RES;
        public static long USER_P_C_VIS;
        public static long USER_P_C_CUSTLIST;
        public static long USER_P_C_CUSTEDIT;
        public static long USER_P_C_APOLIST;
        public static long USER_P_C_APOEDIT;
        public static long USER_P_C_DOCLIST;
        public static long USER_P_C_DOCEDIT;
        public static long USER_P_C_CLILIST;
        public static long USER_P_C_CLIEDIT;
        public static long USER_P_C_VISLIST;
        public static long USER_P_C_VISEDIT;
        public static long USER_P_C_DIGLIST;
        public static long USER_P_C_DIGEDIT;
        public static long USER_P_C_MEDLIST;
        public static long USER_P_C_MEDEDIT;
        public static long USER_P_C_MEDSLIST;
        public static long USER_P_C_MEDGET;
        public static long USER_P_C_SERLIST;
        public static long USER_P_C_SEREDIT;
        public static long USER_P_C_MCHECKLIST;
        public static long USER_P_C_MCHECKEDIT;
        public static long USER_P_C_MCHECKGET;
        public static long USER_P_C_HOL;
        public static long USER_P_C_MEDREP;
        public static long USER_P_C_SAVECARD;
        public static long USER_P_C_COM;
        public static long USER_P_C_ACCLIST;
        public static long USER_P_C_ACCEDIT;
        public static long USER_P_CUST_INSERT;
        public static long USER_P_CUST_EDITE;
        public static long USER_P_CUST_DELETE;
        public static long USER_P_VIS_S_DIG;
        public static long USER_P_VIS_S_SER;
        public static long USER_P_VIS_S_MED;
        public static long USER_P_VIS_S_CHECK;
        public static long USER_P_VIS_S_APO;
        public static long USER_P_VIS_S_MREP;
        public static long USER_P_VIS_S_HOL;
        public static long USER_P_VIS_S_DES;
        public static long USER_P_VIS_S_FILE;
        public static long USER_P_VIS_A_PRICE;
        public static long USER_P_VIS_A_DIS;
        public static long USER_P_VIS_A_TOTAL;
        public static long USER_P_VIS_A_CUST;
        public static long USER_P_VIS_A_COM;
        public static long USER_P_VIS_INSERT;
        public static long USER_P_VIS_EDITE;
        public static long USER_P_VIS_DELETE;
        public static long USER_P_VIS_PRINT;
        public static long USER_P_APO_INSERT;
        public static long USER_P_APO_EDITE;
        public static long USER_P_APO_DELETE;
        public static long USER_P_DOC_INSERT;
        public static long USER_P_DOC_EDITE;
        public static long USER_P_DOC_DELETE;
        public static long USER_P_CLI_INSERT;
        public static long USER_P_CLI_EDITE;
        public static long USER_P_CLI_DELETE;
        public static long USER_P_DIG_INSERT;
        public static long USER_P_DIG_EDITE;
        public static long USER_P_DIG_DELETE;
        public static long USER_P_DIGLIST_OLD;
        public static long USER_P_MED_INSERT;
        public static long USER_P_MED_EDITE;
        public static long USER_P_MED_DELETE;
        public static long USER_P_MED_PRINT;
        public static long USER_P_MEDLIST_OLD;
        public static long USER_P_MEDLIST_EDITE;
        public static long USER_P_MEDLIST_DELETE;
        public static long USER_P_MEDLIST_DOS;
        public static long USER_P_SER_INSERT;
        public static long USER_P_SER_EDITE;
        public static long USER_P_SER_DELETE;
        public static long USER_P_SERLIST_OLD;
        public static long USER_P_CHECK_INSERT;
        public static long USER_P_CHECK_EDITE;
        public static long USER_P_CHECK_DELETE;
        public static long USER_P_CHECK_PRINT;
        public static long USER_P_CHECKLIST_OLD;
        public static long USER_P_CHECKLIST_RESULT;
        public static long USER_P_HOL_INSERT;
        public static long USER_P_HOL_EDITE;
        public static long USER_P_HOL_DELETE;
        public static long USER_P_MEDREP_INSERT;
        public static long USER_P_MEDREP_EDITE;
        public static long USER_P_MEDREP_DELETE;
        public static long USER_P_SAVE_INSERT;
        public static long USER_P_SAVE_EDITE;
        public static long USER_P_SAVE_DELETE;
        public static long USER_P_COM_INSERT;
        public static long USER_P_COM_EDITE;
        public static long USER_P_COM_DELETE;
        public static long USER_P_ACC_INSERT;
        public static long USER_P_ACC_EDITE;
        public static long USER_P_ACC_DELETE;
        public static long USER_P_ACC_PRINT;
        public static long USER_P_SET_TITLE;
        public static long USER_P_SET_PRINTER;
        public static long USER_P_SET_DB;
        public static long USER_P_USER_INSERT;
        public static long USER_P_USER_EDIT;
        public static long USER_P_USER_DELETE;
        public static long USER_P_USER_PASS;
        public static long USER_P_TYPE_INSERT;
        public static long USER_P_TYPE_EDIT;
        public static long USER_P_TYPE_DELETE;
        public static long USER_P_PER_INSERT;
        public static long USER_P_PER_EDIT;
        public static long USER_P_PER_DELETE;
        public static long USER_T_CODE;

        public static USERS_P_TBLTableAdapter USER_P_DATATABLE = new USERS_P_TBLTableAdapter();
        public static GET_USER_PERTableAdapter USER_P_NAME_DATATABLE = new GET_USER_PERTableAdapter();
        public static DataTable PER_DT = new DataTable();

        public DataTable User_P_List()
        {
            DataTable dt = new DataTable();
            dt = clsUSER_PER.USER_P_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_User_P(long S_USER_T_CODE)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsUSER_PER.USER_P_DATATABLE.USER_PERbyUSER_T_CODE(S_USER_T_CODE);
            if (dt.Rows.Count == 1)
            {
                USER_P_ID = Convert.ToInt64(dt.Rows[0]["USER_P_ID"]);
                USER_P_CODE = Convert.ToInt64(dt.Rows[0]["USER_P_CODE"]);
                USER_P_M_RES = Convert.ToInt64(dt.Rows[0]["USER_P_M_RES"]);
                USER_P_M_CUST = Convert.ToInt64(dt.Rows[0]["USER_P_M_CUST"]);
                USER_P_M_APO = Convert.ToInt64(dt.Rows[0]["USER_P_M_APO"]);
                USER_P_M_DOC = Convert.ToInt64(dt.Rows[0]["USER_P_M_DOC"]);
                USER_P_M_MED_CAT = Convert.ToInt64(dt.Rows[0]["USER_P_M_MED_CAT"]);
                USER_P_M_VIS = Convert.ToInt64(dt.Rows[0]["USER_P_M_VIS"]);
                USER_P_M_DIG = Convert.ToInt64(dt.Rows[0]["USER_P_M_DIG"]);
                USER_P_M_MED = Convert.ToInt64(dt.Rows[0]["USER_P_M_MED"]);
                USER_P_M_CHECK = Convert.ToInt64(dt.Rows[0]["USER_P_M_CHECK"]);
                USER_P_M_HOL = Convert.ToInt64(dt.Rows[0]["USER_P_M_HOL"]);
                USER_P_M_SAVE = Convert.ToInt64(dt.Rows[0]["USER_P_M_SAVE"]);
                USER_P_M_ACC = Convert.ToInt64(dt.Rows[0]["USER_P_M_ACC"]);
                USER_P_M_REP = Convert.ToInt64(dt.Rows[0]["USER_P_M_REP"]);
                USER_P_M_SET = Convert.ToInt64(dt.Rows[0]["USER_P_M_SET"]);
                USER_P_C_RES = Convert.ToInt64(dt.Rows[0]["USER_P_C_RES"]);
                USER_P_C_VIS = Convert.ToInt64(dt.Rows[0]["USER_P_C_VIS"]);
                USER_P_C_CUSTLIST = Convert.ToInt64(dt.Rows[0]["USER_P_C_CUSTLIST"]);
                USER_P_C_CUSTEDIT = Convert.ToInt64(dt.Rows[0]["USER_P_C_CUSTEDIT"]);
                USER_P_C_APOLIST = Convert.ToInt64(dt.Rows[0]["USER_P_C_APOLIST"]);
                USER_P_C_APOEDIT = Convert.ToInt64(dt.Rows[0]["USER_P_C_APOEDIT"]);
                USER_P_C_DOCLIST = Convert.ToInt64(dt.Rows[0]["USER_P_C_DOCLIST"]);
                USER_P_C_DOCEDIT = Convert.ToInt64(dt.Rows[0]["USER_P_C_DOCEDIT"]);
                USER_P_C_CLILIST = Convert.ToInt64(dt.Rows[0]["USER_P_C_CLILIST"]);
                USER_P_C_CLIEDIT = Convert.ToInt64(dt.Rows[0]["USER_P_C_CLIEDIT"]);
                USER_P_C_VISLIST = Convert.ToInt64(dt.Rows[0]["USER_P_C_VISLIST"]);
                USER_P_C_VISEDIT = Convert.ToInt64(dt.Rows[0]["USER_P_C_VISEDIT"]);
                USER_P_C_DIGLIST = Convert.ToInt64(dt.Rows[0]["USER_P_C_DIGLIST"]);
                USER_P_C_DIGEDIT = Convert.ToInt64(dt.Rows[0]["USER_P_C_DIGEDIT"]);
                USER_P_C_MEDLIST = Convert.ToInt64(dt.Rows[0]["USER_P_C_MEDLIST"]);
                USER_P_C_MEDEDIT = Convert.ToInt64(dt.Rows[0]["USER_P_C_MEDEDIT"]);
                USER_P_C_MEDSLIST = Convert.ToInt64(dt.Rows[0]["USER_P_C_MEDSLIST"]);
                USER_P_C_MEDGET = Convert.ToInt64(dt.Rows[0]["USER_P_C_MEDGET"]);
                USER_P_C_SERLIST = Convert.ToInt64(dt.Rows[0]["USER_P_C_SERLIST"]);
                USER_P_C_SEREDIT = Convert.ToInt64(dt.Rows[0]["USER_P_C_SEREDIT"]);
                USER_P_C_MCHECKLIST = Convert.ToInt64(dt.Rows[0]["USER_P_C_MCHECKLIST"]);
                USER_P_C_MCHECKEDIT = Convert.ToInt64(dt.Rows[0]["USER_P_C_MCHECKEDIT"]);
                USER_P_C_MCHECKGET = Convert.ToInt64(dt.Rows[0]["USER_P_C_MCHECKGET"]);
                USER_P_C_HOL = Convert.ToInt64(dt.Rows[0]["USER_P_C_HOL"]);
                USER_P_C_MEDREP = Convert.ToInt64(dt.Rows[0]["USER_P_C_MEDREP"]);
                USER_P_C_SAVECARD = Convert.ToInt64(dt.Rows[0]["USER_P_C_SAVECARD"]);
                USER_P_C_COM = Convert.ToInt64(dt.Rows[0]["USER_P_C_COM"]);
                USER_P_C_ACCLIST = Convert.ToInt64(dt.Rows[0]["USER_P_C_ACCLIST"]);
                USER_P_C_ACCEDIT = Convert.ToInt64(dt.Rows[0]["USER_P_C_ACCEDIT"]);
                USER_P_CUST_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_CUST_INSERT"]);
                USER_P_CUST_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_CUST_EDITE"]);
                USER_P_CUST_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_CUST_DELETE"]);
                USER_P_VIS_S_DIG = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_S_DIG"]);
                USER_P_VIS_S_SER = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_S_SER"]);
                USER_P_VIS_S_MED = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_S_MED"]);
                USER_P_VIS_S_CHECK = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_S_CHECK"]);
                USER_P_VIS_S_APO = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_S_APO"]);
                USER_P_VIS_S_MREP = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_S_MREP"]);
                USER_P_VIS_S_HOL = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_S_HOL"]);
                USER_P_VIS_S_DES = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_S_DES"]);
                USER_P_VIS_S_FILE = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_S_FILE"]);
                USER_P_VIS_A_PRICE = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_A_PRICE"]);
                USER_P_VIS_A_DIS = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_A_DIS"]);
                USER_P_VIS_A_TOTAL = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_A_TOTAL"]);
                USER_P_VIS_A_CUST = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_A_CUST"]);
                USER_P_VIS_A_COM = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_A_COM"]);
                USER_P_VIS_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_INSERT"]);
                USER_P_VIS_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_EDITE"]);
                USER_P_VIS_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_DELETE"]);
                USER_P_VIS_PRINT = Convert.ToInt64(dt.Rows[0]["USER_P_VIS_PRINT"]);
                USER_P_APO_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_APO_INSERT"]);
                USER_P_APO_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_APO_EDITE"]);
                USER_P_APO_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_APO_DELETE"]);
                USER_P_DOC_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_DOC_INSERT"]);
                USER_P_DOC_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_DOC_EDITE"]);
                USER_P_DOC_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_DOC_DELETE"]);
                USER_P_CLI_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_CLI_INSERT"]);
                USER_P_CLI_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_CLI_EDITE"]);
                USER_P_CLI_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_CLI_DELETE"]);
                USER_P_DIG_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_DIG_INSERT"]);
                USER_P_DIG_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_DIG_EDITE"]);
                USER_P_DIG_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_DIG_DELETE"]);
                USER_P_DIGLIST_OLD = Convert.ToInt64(dt.Rows[0]["USER_P_DIGLIST_OLD"]);
                USER_P_MED_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_MED_INSERT"]);
                USER_P_MED_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_MED_EDITE"]);
                USER_P_MED_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_MED_DELETE"]);
                USER_P_MED_PRINT = Convert.ToInt64(dt.Rows[0]["USER_P_MED_PRINT"]);
                USER_P_MEDLIST_OLD = Convert.ToInt64(dt.Rows[0]["USER_P_MEDLIST_OLD"]);
                USER_P_MEDLIST_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_MEDLIST_EDITE"]);
                USER_P_MEDLIST_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_MEDLIST_DELETE"]);
                USER_P_MEDLIST_DOS = Convert.ToInt64(dt.Rows[0]["USER_P_MEDLIST_DOS"]);
                USER_P_SER_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_SER_INSERT"]);
                USER_P_SER_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_SER_EDITE"]);
                USER_P_SER_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_SER_DELETE"]);
                USER_P_SERLIST_OLD = Convert.ToInt64(dt.Rows[0]["USER_P_SERLIST_OLD"]);
                USER_P_CHECK_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_CHECK_INSERT"]);
                USER_P_CHECK_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_CHECK_EDITE"]);
                USER_P_CHECK_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_CHECK_DELETE"]);
                USER_P_CHECK_PRINT = Convert.ToInt64(dt.Rows[0]["USER_P_CHECK_PRINT"]);
                USER_P_CHECKLIST_OLD = Convert.ToInt64(dt.Rows[0]["USER_P_CHECKLIST_OLD"]);
                USER_P_CHECKLIST_RESULT = Convert.ToInt64(dt.Rows[0]["USER_P_CHECKLIST_RESULT"]);
                USER_P_HOL_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_HOL_INSERT"]);
                USER_P_HOL_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_HOL_EDITE"]);
                USER_P_HOL_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_HOL_DELETE"]);
                USER_P_MEDREP_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_MEDREP_INSERT"]);
                USER_P_MEDREP_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_MEDREP_EDITE"]);
                USER_P_MEDREP_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_MEDREP_DELETE"]);
                USER_P_SAVE_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_SAVE_INSERT"]);
                USER_P_SAVE_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_SAVE_EDITE"]);
                USER_P_SAVE_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_SAVE_DELETE"]);
                USER_P_COM_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_COM_INSERT"]);
                USER_P_COM_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_COM_EDITE"]);
                USER_P_COM_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_COM_DELETE"]);
                USER_P_ACC_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_ACC_INSERT"]);
                USER_P_ACC_EDITE = Convert.ToInt64(dt.Rows[0]["USER_P_ACC_EDITE"]);
                USER_P_ACC_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_ACC_DELETE"]);
                USER_P_ACC_PRINT = Convert.ToInt64(dt.Rows[0]["USER_P_ACC_PRINT"]);
                USER_P_SET_TITLE = Convert.ToInt64(dt.Rows[0]["USER_P_SET_TITLE"]);
                USER_P_SET_PRINTER = Convert.ToInt64(dt.Rows[0]["USER_P_SET_PRINTER"]);
                USER_P_SET_DB = Convert.ToInt64(dt.Rows[0]["USER_P_SET_DB"]);
                USER_P_USER_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_USER_INSERT"]);
                USER_P_USER_EDIT = Convert.ToInt64(dt.Rows[0]["USER_P_USER_EDIT"]);
                USER_P_USER_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_USER_DELETE"]);
                USER_P_USER_PASS = Convert.ToInt64(dt.Rows[0]["USER_P_USER_PASS"]);
                USER_P_TYPE_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_TYPE_INSERT"]);
                USER_P_TYPE_EDIT = Convert.ToInt64(dt.Rows[0]["USER_P_TYPE_EDIT"]);
                USER_P_TYPE_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_TYPE_DELETE"]);
                USER_P_PER_INSERT = Convert.ToInt64(dt.Rows[0]["USER_P_PER_INSERT"]);
                USER_P_PER_EDIT = Convert.ToInt64(dt.Rows[0]["USER_P_PER_EDIT"]);
                USER_P_PER_DELETE = Convert.ToInt64(dt.Rows[0]["USER_P_PER_DELETE"]);
                USER_T_CODE = Convert.ToInt64(dt.Rows[0]["USER_T_CODE"]);
                PER_DT = USER_P_NAME_DATATABLE.SELECT_USER_PER_NAME(S_USER_T_CODE);
            }
            else
            {
                USER_P_ID = 0;
                USER_P_CODE = 0;
                USER_P_M_RES = 0;
                USER_P_M_CUST = 0;
                USER_P_M_APO = 0;
                USER_P_M_DOC = 0;
                USER_P_M_MED_CAT = 0;
                USER_P_M_VIS = 0;
                USER_P_M_DIG = 0;
                USER_P_M_MED = 0;
                USER_P_M_CHECK = 0;
                USER_P_M_HOL = 0;
                USER_P_M_SAVE = 0;
                USER_P_M_ACC = 0;
                USER_P_M_REP = 0;
                USER_P_M_SET = 0;
                USER_P_C_RES = 0;
                USER_P_C_VIS = 0;
                USER_P_C_CUSTLIST = 0;
                USER_P_C_CUSTEDIT = 0;
                USER_P_C_APOLIST = 0;
                USER_P_C_APOEDIT = 0;
                USER_P_C_DOCLIST = 0;
                USER_P_C_DOCEDIT = 0;
                USER_P_C_CLILIST = 0;
                USER_P_C_CLIEDIT = 0;
                USER_P_C_VISLIST = 0;
                USER_P_C_VISEDIT = 0;
                USER_P_C_DIGLIST = 0;
                USER_P_C_DIGEDIT = 0;
                USER_P_C_MEDLIST = 0;
                USER_P_C_MEDEDIT = 0;
                USER_P_C_MEDSLIST = 0;
                USER_P_C_MEDGET = 0;
                USER_P_C_SERLIST = 0;
                USER_P_C_SEREDIT = 0;
                USER_P_C_MCHECKLIST = 0;
                USER_P_C_MCHECKEDIT = 0;
                USER_P_C_MCHECKGET = 0;
                USER_P_C_HOL = 0;
                USER_P_C_MEDREP = 0;
                USER_P_C_SAVECARD = 0;
                USER_P_C_COM = 0;
                USER_P_C_ACCLIST = 0;
                USER_P_C_ACCEDIT = 0;
                USER_P_CUST_INSERT = 0;
                USER_P_CUST_EDITE = 0;
                USER_P_CUST_DELETE = 0;
                USER_P_VIS_S_DIG = 0;
                USER_P_VIS_S_SER = 0;
                USER_P_VIS_S_MED = 0;
                USER_P_VIS_S_CHECK = 0;
                USER_P_VIS_S_APO = 0;
                USER_P_VIS_S_MREP = 0;
                USER_P_VIS_S_HOL = 0;
                USER_P_VIS_S_DES = 0;
                USER_P_VIS_S_FILE = 0;
                USER_P_VIS_A_PRICE = 0;
                USER_P_VIS_A_DIS = 0;
                USER_P_VIS_A_TOTAL = 0;
                USER_P_VIS_A_CUST = 0;
                USER_P_VIS_A_COM = 0;
                USER_P_VIS_INSERT = 0;
                USER_P_VIS_EDITE = 0;
                USER_P_VIS_DELETE = 0;
                USER_P_VIS_PRINT = 0;
                USER_P_APO_INSERT = 0;
                USER_P_APO_EDITE = 0;
                USER_P_APO_DELETE = 0;
                USER_P_DOC_INSERT = 0;
                USER_P_DOC_EDITE = 0;
                USER_P_DOC_DELETE = 0;
                USER_P_CLI_INSERT = 0;
                USER_P_CLI_EDITE = 0;
                USER_P_CLI_DELETE = 0;
                USER_P_DIG_INSERT = 0;
                USER_P_DIG_EDITE = 0;
                USER_P_DIG_DELETE = 0;
                USER_P_DIGLIST_OLD = 0;
                USER_P_MED_INSERT = 0;
                USER_P_MED_EDITE = 0;
                USER_P_MED_DELETE = 0;
                USER_P_MED_PRINT = 0;
                USER_P_MEDLIST_OLD = 0;
                USER_P_MEDLIST_EDITE = 0;
                USER_P_MEDLIST_DELETE = 0;
                USER_P_MEDLIST_DOS = 0;
                USER_P_SER_INSERT = 0;
                USER_P_SER_EDITE = 0;
                USER_P_SER_DELETE = 0;
                USER_P_SERLIST_OLD = 0;
                USER_P_CHECK_INSERT = 0;
                USER_P_CHECK_EDITE = 0;
                USER_P_CHECK_DELETE = 0;
                USER_P_CHECK_PRINT = 0;
                USER_P_CHECKLIST_OLD = 0;
                USER_P_CHECKLIST_RESULT = 0;
                USER_P_HOL_INSERT = 0;
                USER_P_HOL_EDITE = 0;
                USER_P_HOL_DELETE = 0;
                USER_P_MEDREP_INSERT = 0;
                USER_P_MEDREP_EDITE = 0;
                USER_P_MEDREP_DELETE = 0;
                USER_P_SAVE_INSERT = 0;
                USER_P_SAVE_EDITE = 0;
                USER_P_SAVE_DELETE = 0;
                USER_P_COM_INSERT = 0;
                USER_P_COM_EDITE = 0;
                USER_P_COM_DELETE = 0;
                USER_P_ACC_INSERT = 0;
                USER_P_ACC_EDITE = 0;
                USER_P_ACC_DELETE = 0;
                USER_P_ACC_PRINT = 0;
                USER_P_SET_TITLE = 0;
                USER_P_SET_PRINTER = 0;
                USER_P_SET_DB = 0;
                USER_P_USER_INSERT = 0;
                USER_P_USER_EDIT = 0;
                USER_P_USER_DELETE = 0;
                USER_P_USER_PASS = 0;
                USER_P_TYPE_INSERT = 0;
                USER_P_TYPE_EDIT = 0;
                USER_P_TYPE_DELETE = 0;
                USER_P_PER_INSERT = 0;
                USER_P_PER_EDIT = 0;
                USER_P_PER_DELETE = 0;
                USER_T_CODE = 0;
            }
            return dt;
        }

    }
}
