﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsUSER_TYPE
    {
        public static long USER_T_ID;
        public static long USER_T_CODE;
        public static string USER_T_TYPE;
        public static string USER_T_NOTE;
        public static string USER_T_STATE;
        public static long CLI_ID;

        public static USERS_T_TBLTableAdapter USER_T_DATATABLE = new USERS_T_TBLTableAdapter();

        public DataTable User_T_List()
        {
            DataTable dt = new DataTable();
            dt = clsUSER_TYPE.USER_T_DATATABLE.GetData();
            return dt;
        }

        public DataTable SELECT_USER_T_LIST(string S_USER_T_CODE , string S_USER_T_TYPE , string S_USER_T_STATE)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsUSER_TYPE.USER_T_DATATABLE.USER_TYPEbYUSER_T_CODEorUSER_T_TPYEorUSER_T_STATE(S_USER_T_CODE , S_USER_T_TYPE , S_USER_T_STATE);
            return dt;
        }

        public DataTable SELECT_USER_T(long S_USER_T_CODE)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsUSER_TYPE.USER_T_DATATABLE.USER_TbyUSER_T_CODE(S_USER_T_CODE);
            if (dt.Rows.Count == 1)
            {
                Classes.clsUSER_TYPE.USER_T_ID = Convert.ToInt64(dt.Rows[0]["USER_T_ID"]);
                Classes.clsUSER_TYPE.USER_T_CODE = Convert.ToInt64(dt.Rows[0]["USER_T_CODE"]);
                Classes.clsUSER_TYPE.USER_T_TYPE = (dt.Rows[0]["USER_T_TYPE"]).ToString();
                Classes.clsUSER_TYPE.USER_T_NOTE = (dt.Rows[0]["USER_T_NOTE"]).ToString();
                Classes.clsUSER_TYPE.USER_T_STATE = (dt.Rows[0]["USER_T_STATE"]).ToString();
                Classes.clsUSER_TYPE.CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
            }
            else
            {
                Classes.clsUSER_TYPE.USER_T_ID = 0;
                Classes.clsUSER_TYPE.USER_T_CODE = 0;
                Classes.clsUSER_TYPE.USER_T_TYPE = "";
                Classes.clsUSER_TYPE.USER_T_NOTE = "";
                Classes.clsUSER_TYPE.USER_T_STATE = "";
                Classes.clsUSER_TYPE.CLI_ID = 0;
            }
            return dt;
        }

    }
}
