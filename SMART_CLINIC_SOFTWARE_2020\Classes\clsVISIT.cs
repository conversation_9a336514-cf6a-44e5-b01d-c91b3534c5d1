﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;

namespace SMART_CLINIC_SOFTWARE_2020.Classes
{
    class clsVISIT
    {
        public static long VIS_ID;
        public static long VIS_CODE;
        public static string VIS_NAME;
        public static string VIS_DATE;
        public static string VIS_TIME;
        public static string VIS_TYPE;
        public static long CUST_ID;
        public static long CLI_ID;
        public static long DOC_ID;

        public static VISIT_TBLTableAdapter VISIT_DATATABLE = new VISIT_TBLTableAdapter();

        public DataTable VISIT_List()
        {
            DataTable dt = new DataTable();
            dt = clsVISIT.VISIT_DATATABLE.GetData();
            return dt;
        }

        public DataTable Select_VIST_BY_CUST(long S_CUST_ID, long S_CLI_ID)
        {
            DataTable dt = new DataTable();
            dt = VISIT_DATATABLE.VISITbyCUST_IDandCLI_ID(S_CUST_ID, S_CLI_ID);
            return dt;
        }

        public DataTable Select_VIST(long S_VIS_ID)
        {
            DataTable dt = new DataTable();
            dt = VISIT_DATATABLE.VIS_LISTbyVIS_ID(S_VIS_ID);
            if (dt.Rows.Count == 1)
            {
                VIS_ID = Convert.ToInt64(dt.Rows[0]["VIS_ID"]);
                VIS_CODE = Convert.ToInt64(dt.Rows[0]["VIS_CODE"]);
                VIS_NAME = (dt.Rows[0]["VIS_NAME"]).ToString();
                VIS_DATE = (dt.Rows[0]["VIS_DATE"]).ToString();
                VIS_TIME = (dt.Rows[0]["VIS_TIME"]).ToString();
                VIS_TYPE = (dt.Rows[0]["VIS_TYPE"]).ToString();
                CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                CLI_ID = Convert.ToInt64(dt.Rows[0]["CLI_ID"]);
                DOC_ID = Convert.ToInt64(dt.Rows[0]["DOC_ID"]);
            }
            else
            {
                VIS_ID = 0;
                VIS_CODE = 0;
                VIS_NAME = "";
                VIS_DATE = "";
                VIS_TIME = "";
                VIS_TYPE = "";
                CUST_ID = 0;
                CLI_ID = 0;
                DOC_ID = 0;
            }
                return dt;
        }

    }
}
