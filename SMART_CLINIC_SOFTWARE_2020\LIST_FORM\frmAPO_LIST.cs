﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;

namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmAPO_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmAPO_LIST()
        {
            InitializeComponent();
        }

        Classes.clsAPO NclsAPO = new Classes.clsAPO();
        Classes.clsCUST NclsCUST = new Classes.clsCUST();
        Classes.clsDOCTORS NclsDOC = new Classes.clsDOCTORS();

        public void GRID_DATA(string F_DATE, string S_DATE, string DOC_NAME, string CUST_NAME)
        {
            gridControl1.DataSource = Classes.clsAPO.APO_DATATABLE.APO_LISTbyDATEorDOC_NAMEorCUST_NAME(F_DATE, S_DATE, DOC_NAME, CUST_NAME);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["APO_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["APO_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["DOC_ID"]);
            //gridView1.Columns.Remove(gridView1.Columns["CUST_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["DOC_NAME"]);
            gridView1.Columns["CUST_ID"].Visible = false;
            gridView1.Columns["APO_CODE"].Caption = "كود الموعد";
            gridView1.Columns["APO_DATE"].Caption = "تاريخ الموعد";
            gridView1.Columns["APO_TIME"].Caption = "وقت الموعد";
            gridView1.Columns["VIS_ID"].Caption = "رقم الزيارة";
            gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
            gridView1.Columns["APO_NAME"].Caption = "اسم الموعد";
            gridView1.BestFitColumns();
        }

        private void frmAPO_LIST_Load(object sender, EventArgs e)
        {
            GRID_DATA(string.Format(dtpF_DATE.Value.ToString() , "MM/dd/yyyy" ) , string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"), cmbDOC_NAME.Text , cmbCUST_NAME.Text );
            cmbDOC_ID.DataSource = NclsDOC.DOC_LIST();
            cmbDOC_ID.ValueMember = "DOC_ID";
            cmbDOC_NAME.DataSource = cmbDOC_ID.DataSource;
            cmbDOC_NAME.ValueMember = "DOC_NAME";
            if (Classes.clsCUST.CUST_ID != 0)
            {
                cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData(Classes.clsCUST.CUST_ID.ToString(), Classes.clsCUST.CUST_FULL_NAME);
                cmbCUST_ID.DisplayMember = "CUST_ID";
                cmbCUST_ID.ValueMember = "CUST_ID";
                cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
                cmbCUST_NAME.ValueMember = "CUST_NAME";
            }
            cmbCUST_ID.Text = "";
            cmbCUST_NAME.Text = "";
            cmbDOC_ID.Text = "";
            cmbDOC_NAME.Text = "";
            dtpF_DATE.Value = DateTime.Now;
            dtpS_DATE.Value = DateTime.Now;
        }

        private void dtpF_DATE_ValueChanged(object sender, EventArgs e)
        {
            GRID_DATA(string.Format(dtpF_DATE.Value.ToString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"), cmbDOC_NAME.Text, cmbCUST_NAME.Text);
        }

        private void dtpS_DATE_ValueChanged(object sender, EventArgs e)
        {
            GRID_DATA(string.Format(dtpF_DATE.Value.ToString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"), cmbDOC_NAME.Text, cmbCUST_NAME.Text);
        }

        private void cmbCUST_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA(string.Format(dtpF_DATE.Value.ToString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"), cmbDOC_NAME.Text, cmbCUST_NAME.Text);
        }

        private void cmbDOC_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA(string.Format(dtpF_DATE.Value.ToString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"), cmbDOC_NAME.Text, cmbCUST_NAME.Text);
        }

        private void btnSEARCH_Click(object sender, EventArgs e)
        {
            GRID_DATA(string.Format(dtpF_DATE.Value.ToString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"), cmbDOC_NAME.Text, cmbCUST_NAME.Text);
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            cmbCUST_ID.Text = "";
            cmbCUST_NAME.Text = "";
            cmbDOC_ID.Text = "";
            cmbDOC_NAME.Text = "";
            dtpF_DATE.Value = DateTime.Now;
            dtpS_DATE.Value = DateTime.Now;
            GRID_DATA(string.Format(dtpF_DATE.Value.ToString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"), cmbDOC_NAME.Text, cmbCUST_NAME.Text);
        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmCUST_LIST frmCUST_LIST = new LIST_FORM.frmCUST_LIST();
            frmCUST_LIST.ShowDialog();
            cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
            cmbCUST_NAME.Text = Classes.clsCUST.CUST_FULL_NAME;
        }

        private void lblDOC_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmDOC_LIST frmDOC_LIST = new LIST_FORM.frmDOC_LIST();
            frmDOC_LIST.ShowDialog();
            cmbDOC_ID.Text = Classes.clsDOCTORS.DOC_ID.ToString();
            cmbDOC_NAME.Text = Classes.clsDOCTORS.DOC_NAME;
        }

        private void btnPRINT_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                frmAPOLIST_REPORT apolistrep = new frmAPOLIST_REPORT();
                frmAPOLIST_REPORT.DOC_NAME = cmbDOC_NAME.Text;
                frmAPOLIST_REPORT.CUST_NAME = cmbCUST_NAME.Text;
                frmAPOLIST_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                DateTime F_DATE = Convert.ToDateTime(string.Format(dtpF_DATE.Value.ToString()));
                frmAPOLIST_REPORT.F_DATE = F_DATE.ToString("MM/dd/yyyy");
                DateTime S_DATE = Convert.ToDateTime(string.Format(dtpS_DATE.Value.ToString()));
                frmAPOLIST_REPORT.S_DATE = S_DATE.ToString("MM/dd/yyyy");
                apolistrep.ShowDialog();
            }     
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                frmAPO_VIS_REPORT apovisrep = new frmAPO_VIS_REPORT();
                frmAPO_VIS_REPORT.VIS_ID = Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["VIS_ID"]));
                frmAPO_VIS_REPORT.CUST_ID = Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CUST_ID"]));
                frmAPO_VIS_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                frmAPO_VIS_REPORT.APO_CODE = Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["APO_CODE"]));
                apovisrep.ShowDialog();
            }
        }

        private void cmbCUST_ID_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void cmbCUST_NAME_SelectedIndexChanged(object sender, EventArgs e)
        {

        }
    }
}