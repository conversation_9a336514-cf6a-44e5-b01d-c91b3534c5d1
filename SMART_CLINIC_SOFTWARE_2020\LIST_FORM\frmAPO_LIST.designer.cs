﻿namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    partial class frmAPO_LIST
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmAPO_LIST));
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.btnSEARCH = new DevExpress.XtraEditors.SimpleButton();
            this.btnPRINT = new DevExpress.XtraEditors.SimpleButton();
            this.btnCLEAR = new DevExpress.XtraEditors.SimpleButton();
            this.dtpS_DATE = new System.Windows.Forms.DateTimePicker();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.cmbCUST_NAME = new System.Windows.Forms.ComboBox();
            this.cmbCUST_ID = new System.Windows.Forms.ComboBox();
            this.lblCUST_LIST = new DevExpress.XtraEditors.LabelControl();
            this.cmbDOC_NAME = new System.Windows.Forms.ComboBox();
            this.cmbDOC_ID = new System.Windows.Forms.ComboBox();
            this.dtpF_DATE = new System.Windows.Forms.DateTimePicker();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.lblDOC_LIST = new DevExpress.XtraEditors.LabelControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Appearance.Font = new System.Drawing.Font("Times New Roman", 15.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupControl1.Appearance.Options.UseFont = true;
            this.groupControl1.AppearanceCaption.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.groupControl1.AppearanceCaption.Options.UseFont = true;
            this.groupControl1.Controls.Add(this.btnSEARCH);
            this.groupControl1.Controls.Add(this.btnPRINT);
            this.groupControl1.Controls.Add(this.btnCLEAR);
            this.groupControl1.Controls.Add(this.dtpS_DATE);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.cmbCUST_NAME);
            this.groupControl1.Controls.Add(this.cmbCUST_ID);
            this.groupControl1.Controls.Add(this.lblCUST_LIST);
            this.groupControl1.Controls.Add(this.cmbDOC_NAME);
            this.groupControl1.Controls.Add(this.cmbDOC_ID);
            this.groupControl1.Controls.Add(this.dtpF_DATE);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Controls.Add(this.lblDOC_LIST);
            this.groupControl1.Location = new System.Drawing.Point(3, 6);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(944, 132);
            this.groupControl1.TabIndex = 44;
            this.groupControl1.Text = "بيانات الموعد";
            // 
            // btnSEARCH
            // 
            this.btnSEARCH.Appearance.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSEARCH.Appearance.Options.UseFont = true;
            this.btnSEARCH.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnSEARCH.ImageOptions.Image")));
            this.btnSEARCH.Location = new System.Drawing.Point(174, 43);
            this.btnSEARCH.Name = "btnSEARCH";
            this.btnSEARCH.Size = new System.Drawing.Size(80, 66);
            this.btnSEARCH.TabIndex = 52;
            this.btnSEARCH.Text = "بحث";
            this.btnSEARCH.Click += new System.EventHandler(this.btnSEARCH_Click);
            // 
            // btnPRINT
            // 
            this.btnPRINT.Appearance.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPRINT.Appearance.Options.UseFont = true;
            this.btnPRINT.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnPRINT.ImageOptions.Image")));
            this.btnPRINT.Location = new System.Drawing.Point(89, 43);
            this.btnPRINT.Name = "btnPRINT";
            this.btnPRINT.Size = new System.Drawing.Size(80, 66);
            this.btnPRINT.TabIndex = 49;
            this.btnPRINT.Text = "طباعة";
            this.btnPRINT.Click += new System.EventHandler(this.btnPRINT_Click);
            // 
            // btnCLEAR
            // 
            this.btnCLEAR.Appearance.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCLEAR.Appearance.Options.UseFont = true;
            this.btnCLEAR.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnCLEAR.ImageOptions.Image")));
            this.btnCLEAR.Location = new System.Drawing.Point(7, 43);
            this.btnCLEAR.Name = "btnCLEAR";
            this.btnCLEAR.Size = new System.Drawing.Size(78, 66);
            this.btnCLEAR.TabIndex = 51;
            this.btnCLEAR.Text = "مسح";
            this.btnCLEAR.Click += new System.EventHandler(this.btnCLEAR_Click);
            // 
            // dtpS_DATE
            // 
            this.dtpS_DATE.CustomFormat = "";
            this.dtpS_DATE.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.dtpS_DATE.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.dtpS_DATE.Location = new System.Drawing.Point(260, 81);
            this.dtpS_DATE.Name = "dtpS_DATE";
            this.dtpS_DATE.Size = new System.Drawing.Size(188, 38);
            this.dtpS_DATE.TabIndex = 25;
            this.dtpS_DATE.Value = new System.DateTime(2022, 9, 28, 23, 34, 43, 0);
            this.dtpS_DATE.ValueChanged += new System.EventHandler(this.dtpS_DATE_ValueChanged);
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(454, 87);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(62, 31);
            this.labelControl2.TabIndex = 24;
            this.labelControl2.Text = "الى تاريخ";
            // 
            // cmbCUST_NAME
            // 
            this.cmbCUST_NAME.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.cmbCUST_NAME.FormattingEnabled = true;
            this.cmbCUST_NAME.Location = new System.Drawing.Point(525, 40);
            this.cmbCUST_NAME.Name = "cmbCUST_NAME";
            this.cmbCUST_NAME.Size = new System.Drawing.Size(236, 39);
            this.cmbCUST_NAME.TabIndex = 23;
            this.cmbCUST_NAME.SelectedIndexChanged += new System.EventHandler(this.cmbCUST_NAME_SelectedIndexChanged);
            this.cmbCUST_NAME.TextChanged += new System.EventHandler(this.cmbCUST_NAME_TextChanged);
            // 
            // cmbCUST_ID
            // 
            this.cmbCUST_ID.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.cmbCUST_ID.FormattingEnabled = true;
            this.cmbCUST_ID.Location = new System.Drawing.Point(763, 40);
            this.cmbCUST_ID.Name = "cmbCUST_ID";
            this.cmbCUST_ID.Size = new System.Drawing.Size(76, 39);
            this.cmbCUST_ID.TabIndex = 22;
            this.cmbCUST_ID.SelectedIndexChanged += new System.EventHandler(this.cmbCUST_ID_SelectedIndexChanged);
            // 
            // lblCUST_LIST
            // 
            this.lblCUST_LIST.Appearance.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.lblCUST_LIST.Appearance.Options.UseFont = true;
            this.lblCUST_LIST.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.lblCUST_LIST.Location = new System.Drawing.Point(845, 42);
            this.lblCUST_LIST.Name = "lblCUST_LIST";
            this.lblCUST_LIST.Size = new System.Drawing.Size(89, 33);
            this.lblCUST_LIST.TabIndex = 21;
            this.lblCUST_LIST.Text = "اسم المريض";
            this.lblCUST_LIST.Click += new System.EventHandler(this.lblCUST_LIST_Click);
            // 
            // cmbDOC_NAME
            // 
            this.cmbDOC_NAME.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.cmbDOC_NAME.FormattingEnabled = true;
            this.cmbDOC_NAME.Location = new System.Drawing.Point(525, 83);
            this.cmbDOC_NAME.Name = "cmbDOC_NAME";
            this.cmbDOC_NAME.Size = new System.Drawing.Size(236, 39);
            this.cmbDOC_NAME.TabIndex = 20;
            this.cmbDOC_NAME.TextChanged += new System.EventHandler(this.cmbDOC_NAME_TextChanged);
            // 
            // cmbDOC_ID
            // 
            this.cmbDOC_ID.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.cmbDOC_ID.FormattingEnabled = true;
            this.cmbDOC_ID.Location = new System.Drawing.Point(763, 83);
            this.cmbDOC_ID.Name = "cmbDOC_ID";
            this.cmbDOC_ID.Size = new System.Drawing.Size(76, 39);
            this.cmbDOC_ID.TabIndex = 19;
            // 
            // dtpF_DATE
            // 
            this.dtpF_DATE.CustomFormat = "";
            this.dtpF_DATE.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.dtpF_DATE.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.dtpF_DATE.Location = new System.Drawing.Point(260, 37);
            this.dtpF_DATE.Name = "dtpF_DATE";
            this.dtpF_DATE.Size = new System.Drawing.Size(188, 38);
            this.dtpF_DATE.TabIndex = 18;
            this.dtpF_DATE.Value = new System.DateTime(2022, 9, 28, 23, 34, 55, 0);
            this.dtpF_DATE.ValueChanged += new System.EventHandler(this.dtpF_DATE_ValueChanged);
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(454, 43);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(57, 31);
            this.labelControl5.TabIndex = 15;
            this.labelControl5.Text = "من تاريخ";
            // 
            // lblDOC_LIST
            // 
            this.lblDOC_LIST.Appearance.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.lblDOC_LIST.Appearance.Options.UseFont = true;
            this.lblDOC_LIST.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.lblDOC_LIST.Location = new System.Drawing.Point(845, 86);
            this.lblDOC_LIST.Name = "lblDOC_LIST";
            this.lblDOC_LIST.Size = new System.Drawing.Size(84, 33);
            this.lblDOC_LIST.TabIndex = 12;
            this.lblDOC_LIST.Text = "اسم الطبيب";
            this.lblDOC_LIST.Click += new System.EventHandler(this.lblDOC_LIST_Click);
            // 
            // gridControl1
            // 
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.gridControl1.EmbeddedNavigator.Appearance.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridControl1.EmbeddedNavigator.Appearance.Options.UseFont = true;
            this.gridControl1.EmbeddedNavigator.Appearance.Options.UseTextOptions = true;
            this.gridControl1.EmbeddedNavigator.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.gridControl1.Font = new System.Drawing.Font("Times New Roman", 18F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridControl1.Location = new System.Drawing.Point(0, 144);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(953, 383);
            this.gridControl1.TabIndex = 45;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.ColumnFilterButton.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.gridView1.Appearance.ColumnFilterButton.Options.UseFont = true;
            this.gridView1.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gridView1.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gridView1.Appearance.HeaderPanel.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F);
            this.gridView1.Appearance.HeaderPanel.Options.UseFont = true;
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CellSelect;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.DoubleClick += new System.EventHandler(this.gridView1_DoubleClick);
            // 
            // frmAPO_LIST
            // 
            this.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.Appearance.Options.UseBackColor = true;
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 19F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(953, 527);
            this.Controls.Add(this.gridControl1);
            this.Controls.Add(this.groupControl1);
            this.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.MaximizeBox = false;
            this.Name = "frmAPO_LIST";
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "قائمة المواعيد";
            this.Load += new System.EventHandler(this.frmAPO_LIST_Load);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.DateTimePicker dtpS_DATE;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private System.Windows.Forms.ComboBox cmbCUST_NAME;
        private System.Windows.Forms.ComboBox cmbCUST_ID;
        private DevExpress.XtraEditors.LabelControl lblCUST_LIST;
        private System.Windows.Forms.ComboBox cmbDOC_NAME;
        private System.Windows.Forms.ComboBox cmbDOC_ID;
        private System.Windows.Forms.DateTimePicker dtpF_DATE;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl lblDOC_LIST;
        private DevExpress.XtraEditors.SimpleButton btnSEARCH;
        private DevExpress.XtraEditors.SimpleButton btnPRINT;
        private DevExpress.XtraEditors.SimpleButton btnCLEAR;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
    }
}