﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmCARD_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmCARD_LIST()
        {
            InitializeComponent();
        }

        Classes.clsCARD NclsCARD = new Classes.clsCARD();

        public void GRID_DATA()
        {
            DataTable dt = new DataTable();
            dt = Classes.clsCARD.CARD_DATATABLE.CARDbyCARD_NAME(txtCARD_Name.Text);
            gridControl1.DataSource = dt;
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["CARD_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["COM_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CARD_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["CARD_CODE"].Caption = "كود البطاقة";
            gridView1.Columns["CARD_NAME"].Caption = "اسم البطاقة";
            gridView1.Columns["CARD_DATE"].Caption = "تاريخ الصلاحية";
            gridView1.Columns["CARD_STATE"].Caption = "حالة البطاقة";
            gridView1.Columns["CARD_PER"].Caption = "نسبة التامين";
            gridView1.BestFitColumns();
        }
        private void frmCARD_LIST_Load(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtCARD_Name_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtCARD_Name.Text = "";
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsCARD.Select_CARD(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CARD_NAME"]).ToString()).Rows.Count == 1)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}