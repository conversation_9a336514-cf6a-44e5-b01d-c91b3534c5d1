﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;

namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmCLINIC_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmCLINIC_LIST()
        {
            InitializeComponent();
        }

        Classes.clsCLINIC NclsCLINIC = new Classes.clsCLINIC();


        public void GRID_DATA()
        {
            DataTable dt = new DataTable();
            dt = Classes.clsCLINIC.CLINIC_DATATABLE.CLINICbyNAME(txtCLINICName.Text);
            gridControl1.DataSource = dt;
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_CONNECTION"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_SERVER"]);
            gridView1.Columns["CLI_CODE"].Caption = "كود العيادة";
            gridView1.Columns["CLI_NAME"].Caption = "أسم العيادة";
            gridView1.Columns["CLI_LOC"].Caption = "موقع العيادة";
        }
        private void frmCLINIC_LIST_Load(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtCLINICName_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtCLINICName.Text = "";
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsCLINIC.SELECT_CLINIC(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CLI_NAME"]).ToString()).Rows.Count == 1)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

            }
        }
    }
}