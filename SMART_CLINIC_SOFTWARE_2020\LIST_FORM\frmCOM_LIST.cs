﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmCOM_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmCOM_LIST()
        {
            InitializeComponent();
        }

        Classes.clsCOMPANY NclsCOMPANY = new Classes.clsCOMPANY();

        public void GRID_DATA()
        {
            DataTable dt = new DataTable();
            dt = Classes.clsCOMPANY.COMPANY_DATATABLE.COMPANYbyCOM_NAME(txtCOMName.Text);
            gridControl1.DataSource = dt;
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["COM_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["COM_CODE"].Caption = "كود الشركة";
            gridView1.Columns["COM_NAME"].Caption = "اسم الشركة";
            gridView1.Columns["COM_ADDRESS"].Caption = "عنوان الشركة";
            gridView1.Columns["COM_MOBILE"].Caption = "رقم الهاتف";
            gridView1.Columns["COM_STATE"].Caption = "الحالة";
        }
        private void frmCOM_LIST_Load(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtCOMName_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtCOMName.Text = "";
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsCOMPANY.SELECT_COMPANY(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["COM_NAME"]).ToString()).Rows.Count == 1)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

            }
        }
    }
}