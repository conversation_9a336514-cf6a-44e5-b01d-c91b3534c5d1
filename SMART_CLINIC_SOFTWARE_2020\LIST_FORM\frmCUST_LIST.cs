﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmCUST_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmCUST_LIST()
        {
            InitializeComponent();
        }
        Classes.clsCUST NclsCUST = new Classes.clsCUST();

        public void GRID_DATA()
        {
            gridControl1.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData(txtCUST_Name.Text, txtCUST_Name.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            //gridView1.Columns.Remove(gridView1.Columns["CUST_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CUST_GENDER"]);
            gridView1.Columns.Remove(gridView1.Columns["CUST_NATION"]);
            gridView1.Columns.Remove(gridView1.Columns["CUST_AGE_MONTH"]);
            gridView1.Columns["CUST_ID"].Visible = false;
            gridView1.Columns["CUST_CODE"].Caption = "كود المريض";
            gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
            gridView1.Columns["CUST_AGE"].Caption = "عمر المريض";
            gridView1.Columns["CUST_BD"].Caption = "تاريخ الميلاد";
            gridView1.Columns["CUST_MOBILE1"].Caption = "رقم الهاتف";
            gridView1.BestFitColumns();
        }
        private void frmCUST_LIST_Load(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtCUST_Name_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtCUST_Name.Text = "";
            txtCUST_Name.Focus();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsCUST.Select_CUST(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CUST_CODE"]).ToString(), gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CUST_NAME"]).ToString()).Rows.Count > 0)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

            }
        }
    }
}