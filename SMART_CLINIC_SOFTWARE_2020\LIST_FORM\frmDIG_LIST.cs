﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;

namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmDIG_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmDIG_LIST()
        {
            InitializeComponent();
        }
        Classes.clsDIAGNOIS NclsDIGNOSIS = new Classes.clsDIAGNOIS();
        public void GRID_DATA()
        {
            DataTable dt = new DataTable();
            dt = Classes.clsDIAGNOIS.Diagnois_DATATABLE.DIAGNOISbyDIG_NAME(txtDIGName.Text);
            gridControl1.DataSource = dt;
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["DIG_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["DIG_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["DIG_CODE"].Caption = "كود التشخيص";
            gridView1.Columns["DIG_NAME"].Caption = "اسم التشخيص";
            gridView1.Columns["DIG_TYPE"].Caption = "نوع التشخيص";
            gridView1.BestFitColumns();
        }
        private void frmDIG_LIST_Load(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtDIGName_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtDIGName.Text = "";
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsDIGNOSIS.Select_DIAGNOIS(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["DIG_NAME"]).ToString()).Rows.Count == 1)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

            }
        }
    }
}