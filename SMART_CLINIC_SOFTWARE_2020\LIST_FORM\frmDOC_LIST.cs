﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmDOC_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmDOC_LIST()
        {
            InitializeComponent();
        }

        Classes.clsDOCTORS NclsDOC = new Classes.clsDOCTORS();


        public void GRID_DATA()
        {
            gridControl1.DataSource = NclsDOC.SELECT_DOC (txtDocName.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["DOC_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["DOC_EXP"]);
            gridView1.Columns.Remove(gridView1.Columns["DOC_BD"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["USER_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["DOC_ADDRESS"]);
            gridView1.Columns["DOC_CODE"].Caption = "كود الطبيب";
            gridView1.Columns["DOC_NAME"].Caption = "أسم الطبيب";
            gridView1.Columns["DOC_MAJOR"].Caption = "التخصص";
            gridView1.Columns["DOC_MOBILE"].Caption = "رقم الهاتف";
        }

        private void frmDOC_LIST_Load(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtDocName_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtDocName.Text = "";
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsDOC.SELECT_DOC(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["DOC_NAME"]).ToString()).Rows.Count == 1)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

            }
        }
    }
}