﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmMEDCHEK_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmMEDCHEK_LIST()
        {
            InitializeComponent();
        }
        Classes.clsMEDCHEK NclsMEDCHEK = new Classes.clsMEDCHEK();

        public void GRID_DATA()
        {
            DataTable dt = new DataTable();
            dt = Classes.clsMEDCHEK.MEDCHEK_DATATABLE.MEDCHEKbyMEDCHEK_NAME(txtMEDName.Text);
            gridControl1.DataSource = dt;
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["MEDCHEK_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDCHEK_NOTE"]);
            gridView1.Columns["MEDCHEK_CODE"].Caption = "كود الفحص";
            gridView1.Columns["MEDCHEK_NAME"].Caption = "اسم الفحص";
            gridView1.Columns["MEDCHEK_TYPE"].Caption = "نوع الفحص";
            gridView1.Columns["MEDCHEK_PRICE"].Caption = "سعر الفحص";
        }
        private void frmMEDCHEK_LIST_Load(object sender, EventArgs e)
        {
            GRID_DATA();
            txtMEDName.Focus();
        }

        private void txtMEDName_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtMEDName.Text = "";
            txtMEDName.Focus();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsMEDCHEK.Select_MEDCHEK(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MEDCHEK_NAME"]).ToString()).Rows.Count == 1)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

            }
        }
    }
}