﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmMEDCIN_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmMEDCIN_LIST()
        {
            InitializeComponent();
        }
        Classes.clsMEDCIN NclsMEDCINE = new Classes.clsMEDCIN();

        public void GRID_DATA()
        {
            DataTable dt = new DataTable();
            dt = Classes.clsMEDCIN.MEDCIN_DATATABLE.MEDCINbyMED_NAME(txtMEDName.Text);
            gridControl1.DataSource = dt;
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["MED_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["MED_S_NAME"]);
            gridView1.Columns["MED_CODE"].Caption = "كود العلاج";
            gridView1.Columns["MED_NAME"].Caption = "اسم العلاج";
            //gridView1.Columns["MED_S_NAME"].Caption = "الاسم العلمي";
            gridView1.Columns["MED_SOURSE"].Caption = "مصدر العلاج";
            gridView1.Columns["MED_PRICE"].Caption = "سعر العلاج";
        }

        private void frmMEDCIN_LIST_Load(object sender, EventArgs e)
        {
            GRID_DATA();
            txtMEDName.Focus();
        }

        private void txtMEDName_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtMEDName.Text = "";
            txtMEDName.Focus();

        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsMEDCINE.Select_MEDCIN(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MED_NAME"]).ToString()).Rows.Count == 1)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

            }
        }
    }
}