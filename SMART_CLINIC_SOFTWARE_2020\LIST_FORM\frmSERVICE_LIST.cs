﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;

namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmSERVICE_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmSERVICE_LIST()
        {
            InitializeComponent();
        }
        Classes.clsSERVICE NclsSERVICE = new Classes.clsSERVICE();
        public void GRID_DATA()
        {
            DataTable dt = new DataTable();
            dt = Classes.clsSERVICE.SERVICE_DATATABLE.SERVICEbySER_NAME(txtSER_Name.Text);
            gridControl1.DataSource = dt;
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["SER_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["SER_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["SER_CODE"].Caption = "كود الاجراء";
            gridView1.Columns["SER_NAME"].Caption = "اسم الاجراء";
            gridView1.Columns["SER_TYPE"].Caption = "نوع الاجراء";
            gridView1.Columns["SER_PRICE"].Caption = "سعر الاجراء";
        }

        private void frmSERVICE_LIST_Load(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtSER_Name_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtSER_Name.Text = "";
            txtSER_Name.Focus();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsSERVICE.Select_SERVICE(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["SER_NAME"]).ToString()).Rows.Count == 1)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

            }
        }
    }
}