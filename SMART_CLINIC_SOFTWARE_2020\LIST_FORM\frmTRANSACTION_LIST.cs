﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.Project_Form;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;

namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmTRANSACTION_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmTRANSACTION_LIST()
        {
            InitializeComponent();
        }

        Classes.clsCUST NclsCUST = new Classes.clsCUST();
        Classes.clsCOMPANY NclsCOMPANY = new Classes.clsCOMPANY();
        Classes.clsCLINIC NclsCLINIC = new Classes.clsCLINIC();
        Classes.clsTRANSACTION NclsTRANSACTION = new Classes.clsTRANSACTION();

        public void Clear_Date()
        {
            cmbCUST_NAME.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
            cmbCUST_NAME.DisplayMember = "CUST_NAME";
            cmbCUST_NAME.ValueMember = "CUST_ID";
            cmbCUST_NAME.Text = "";

            cmbCLI_NAME.DataSource = NclsCLINIC.CLINIC_LIST();
            cmbCLI_NAME.DisplayMember = "CLI_NAME";
            cmbCLI_NAME.ValueMember = "CLI_ID";

            cmbCOM_NAME.DataSource = NclsCOMPANY.COMPANY_LIST();
            cmbCOM_NAME.DisplayMember = "COM_NAME";
            cmbCOM_NAME.ValueMember = "COM_ID";
            cmbCOM_NAME.Text = "";

            txtT_ID.Text = "";
            txtVIS_ID.Text = "";
            cmbT_TYPE.Text = "";
            dtpF_DATE.Value = DateTime.Now;
            dtpS_DATE.Value = DateTime.Now;
        }

        public void GRID_DATA()
        {
            gridControl1.DataSource = Classes.clsTRANSACTION.T_DATATABLE.TRANSACTION_LIST_DATA_PRO(cmbCUST_NAME.Text == "" ? DBNull.Value.ToString() : cmbCUST_NAME.Text, cmbCOM_NAME.Text == "" ? DBNull.Value.ToString() : cmbCOM_NAME.Text, cmbCLI_NAME.Text == "" ? DBNull.Value.ToString() : cmbCLI_NAME.Text, txtVIS_ID.Text == "" ? DBNull.Value.ToString() : txtVIS_ID.Text, txtT_ID.Text == "" ? DBNull.Value.ToString() : txtT_ID.Text, cmbT_TYPE.Text, Convert.ToDateTime(string.Format(dtpF_DATE.Value.ToShortDateString(), "MM/dd/yyyy")), Convert.ToDateTime(string.Format(dtpS_DATE.Value.ToShortDateString(), "MM/dd/yyyy")));
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["T_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["T_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CARD_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["COM_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["T_STATE"]);
            gridView1.Columns.Remove(gridView1.Columns["CARD_NAME"]);
            gridView1.Columns.Remove(gridView1.Columns["COM_NAME"]);
            gridView1.Columns.Remove(gridView1.Columns["CUST_PRICE"]);
            gridView1.Columns.Remove(gridView1.Columns["COMPANY_PRICE"]);
            gridView1.Columns["T_ID"].Caption = "الرقم";
            gridView1.Columns["T_ID"].VisibleIndex = 0;
            gridView1.Columns["T_NAME"].Caption = "البيان";
            gridView1.Columns["T_NAME"].VisibleIndex = 1;
            gridView1.Columns["T_DATE"].Caption = "التاريخ";
            gridView1.Columns["T_DATE"].VisibleIndex = 2;
            gridView1.Columns["T_TIME"].Caption = "الوقت";
            gridView1.Columns["T_TIME"].VisibleIndex = 3;
            gridView1.Columns["T_TYPE"].Caption = "نوع القيد";
            gridView1.Columns["T_TYPE"].VisibleIndex = 4;
            gridView1.Columns["T_PRICE"].Caption = "القيمة";
            gridView1.Columns["T_PRICE"].VisibleIndex = 5;
            gridView1.Columns["T_DISCOUNT"].Caption = "الخصم";
            gridView1.Columns["T_DISCOUNT"].VisibleIndex = 6;
            gridView1.Columns["T_TOTAL"].Caption = "الواصل";
            gridView1.Columns["T_TOTAL"].VisibleIndex = 7;
            gridView1.Columns["T_UNPAY"].Caption = "الباقي";
            gridView1.Columns["T_UNPAY"].VisibleIndex = 8;
            
            //gridView1.Columns["CUST_PRICE"].Caption = "قيمة المريض";
            //gridView1.Columns["COMPANY_PRICE"].Caption = "قيمة الشركة";
            gridView1.Columns["VIS_ID"].Caption = "رقم الزيارة";
            gridView1.Columns["VIS_ID"].VisibleIndex = 9;
            gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
            gridView1.Columns["CUST_NAME"].VisibleIndex = 10;
            //gridView1.Columns["CARD_NAME"].Caption = "بطاقة التامين";
            //gridView1.Columns["COM_NAME"].Caption = "شركة التامين";
            gridView1.Columns["CLI_NAME"].Caption = "اسم العيادة";
            gridView1.Columns["CLI_NAME"].VisibleIndex = 11;
            gridView1.BestFitColumns();
        }
        private void frmTRANSACTION_LIST_Load(object sender, EventArgs e)
        {
            foreach (var item in groupControl1.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            Clear_Date();
            GRID_DATA();
        }

        private void dtpF_DATE_ValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void cmbT_TYPE_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void cmbCUST_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void cmbCLI_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtVIS_ID_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtT_ID_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void cmbCOM_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            frmCUST_LIST frmCUST_LIST = new frmCUST_LIST();
            frmCUST_LIST.ShowDialog();
            if (Classes.clsCUST.CUST_ID != 0)
            {
                cmbCUST_NAME.Text = Classes.clsCUST.CUST_FULL_NAME;
            }
            else
            {
                cmbCUST_NAME.Text = "";
            }
        }

        private void lblCLINIC_LIST_Click(object sender, EventArgs e)
        {
            frmCLINIC_LIST frmCLI_LIST = new frmCLINIC_LIST();
            frmCLI_LIST.ShowDialog();
            if (Classes.clsCLINIC.CLI_ID != 0)
            {
                cmbCLI_NAME.Text = Classes.clsCLINIC.CLI_NAME;
            }
            else
            {
                cmbCLI_NAME.Text = "";
            }
        }

        private void lblCOM_LIST_Click(object sender, EventArgs e)
        {
            frmCOM_LIST frmCOM_LIST = new frmCOM_LIST();
            frmCOM_LIST.ShowDialog();
            if (Classes.clsCOMPANY.COM_ID != 0)
            {
                cmbCOM_NAME.Text = Classes.clsCOMPANY.COM_NAME;
            }
            else
            {
                cmbCOM_NAME.Text = "";
            }
        }

        private void lblVISIT_LIST_Click(object sender, EventArgs e)
        {
            frmVIS_LIST frmVIS_LIST = new frmVIS_LIST();
            frmVIS_LIST.ShowDialog();
            if (Classes.clsVISIT.VIS_ID != 0)
            {
                txtVIS_ID.Text = Classes.clsVISIT.VIS_ID.ToString();
            }
            else
            {
                txtVIS_ID.Text = "";
            }
        }

        private void lblT_LIST_Click(object sender, EventArgs e)
        {
            frmTRANSACTION TRANSACTION = new frmTRANSACTION();
            TRANSACTION.ShowDialog();
            txtT_ID.Text = Classes.clsTRANSACTION.T_ID.ToString();        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متاكد من تعديل البيانات", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                if (gridView1.RowCount > 0)
                {
                    NclsTRANSACTION.Select_TRANS(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["T_NAME"]).ToString(), Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["T_ID"]).ToString()));
                    frmTRANSACTION TRANSACTION = new frmTRANSACTION();
                    TRANSACTION.ShowDialog();
                }
                else
                {
                    GRID_DATA();
                }
            } 
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متاكد من حذف البيانات", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                if (gridView1.RowCount > 0)
                {
                    Classes.clsTRANSACTION.T_DATATABLE.DeleteTRANSACTION(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["T_ID"]).ToString()), Convert.ToInt64(cmbCLI_NAME.SelectedValue));
                    GRID_DATA();
                }
                else
                {
                    GRID_DATA();
                }
            }
            
        }

        private void btnPRINT_Click(object sender, EventArgs e)
        {
            frmTRANSACTION_LIST_DATA_REPORT TRAN_LIST_REP = new frmTRANSACTION_LIST_DATA_REPORT();
            frmTRANSACTION_LIST_DATA_REPORT.VIS_ID = txtVIS_ID.Text;
            frmTRANSACTION_LIST_DATA_REPORT.T_ID = txtT_ID.Text;
            frmTRANSACTION_LIST_DATA_REPORT.T_TYPE = cmbT_TYPE.Text;
            frmTRANSACTION_LIST_DATA_REPORT.COM_NAME = cmbCOM_NAME.Text;
            frmTRANSACTION_LIST_DATA_REPORT.CUST_NAME = cmbCUST_NAME.Text;
            frmTRANSACTION_LIST_DATA_REPORT.CLI_NAME = cmbCLI_NAME.Text;
            frmTRANSACTION_LIST_DATA_REPORT.F_DATE = dtpF_DATE.Value;
            frmTRANSACTION_LIST_DATA_REPORT.S_DATE = dtpS_DATE.Value;
            TRAN_LIST_REP.ShowDialog();
        }

      
    }
}