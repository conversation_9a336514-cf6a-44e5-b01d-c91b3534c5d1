﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Diagnostics;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;

namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmVIS_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmVIS_LIST()
        {
            InitializeComponent();
        }

        Classes.clsCLINIC NclsCLI = new Classes.clsCLINIC();
        Classes.clsCUST NclsCUST = new Classes.clsCUST();
        Classes.clsDOCTORS NclsDOC = new Classes.clsDOCTORS();
        Classes.clsVISIT NclsVIS = new Classes.clsVISIT();
        public void Clear_Date()
        {
            if (Classes.clsCUST.CUST_ID != 0 & cmbCUST_NAME.Text != "" )
            {
                cmbCUST_NAME.Text = Classes.clsCUST.CUST_F_NAME;
                cmbCUST_NAME.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData(Classes.clsCUST.CUST_ID.ToString(), Classes.clsCUST.CUST_FULL_NAME);
                cmbCUST_NAME.ValueMember = "CUST_NAME";
            }
            if (Classes.clsDOCTORS.DOC_ID != 0)
            {
                cmbDOC_NAME.Text = Classes.clsDOCTORS.DOC_NAME;
                cmbDOC_NAME.DataSource = NclsDOC.DOC_LIST();
                cmbDOC_NAME.ValueMember = "DOC_NAME";

            }
            else
            {
                cmbDOC_NAME.DataSource = NclsDOC.DOC_LIST();
                cmbDOC_NAME.ValueMember = "DOC_NAME";
                cmbDOC_NAME.Text = "";
            }
            if (Classes.clsCLINIC.CLI_ID != 0)
            {
                cmbCLI_NAME.Text = Classes.clsCLINIC.CLI_NAME;
                cmbCLI_NAME.DataSource = NclsCLI.CLINIC_LIST();
                cmbCLI_NAME.ValueMember = "CLI_NAME";

            }
            else
            {
                cmbCLI_NAME.DataSource = NclsCLI.CLINIC_LIST();
                cmbCLI_NAME.ValueMember = "CLI_NAME";
                cmbCLI_NAME.Text = "";
            }
            if (Classes.clsVISIT.VIS_ID != 0)
            {
                txtVIS_ID.Text = Classes.clsVISIT.VIS_ID.ToString();
            }
            else
            {
                txtVIS_ID.Text = "";
            }
            dtpF_DATE.Value = DateTime.Now;
            dtpS_DATE.Value = DateTime.Now;
            txtVIS_ID.Text = "";
            cmbCUST_NAME.Focus(); 
        }
        public void GRID_DATA()
        {
            gridControl1.DataSource = Classes.clsVISIT.VISIT_DATATABLE.VIS_LIST_DATA(cmbCUST_NAME.Text, cmbDOC_NAME.Text, cmbCLI_NAME.Text, string.Format(dtpF_DATE.Value.ToShortDateString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToShortDateString(), "MM/dd/yyyy"),txtVIS_ID.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["VIS_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["VIS_NAME"]);
            gridView1.Columns.Remove(gridView1.Columns["VIS_TYPE"]);
            gridView1.Columns.Remove(gridView1.Columns["VIS_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["DOC_ID"]);
            gridView1.Columns["VIS_ID"].Caption = "رقم الزيارة";
            gridView1.Columns["VIS_ID"].VisibleIndex = 0;
            gridView1.Columns["VIS_DATE"].Caption = "تاريخ الزيارة";
            gridView1.Columns["VIS_DATE"].VisibleIndex = 1;
            gridView1.Columns["VIS_TIME"].Caption = "وقت الزيارة";
            gridView1.Columns["VIS_TIME"].VisibleIndex = 2;
            gridView1.Columns["CUST_ID"].Caption = "رقم المريض";
            gridView1.Columns["CUST_ID"].VisibleIndex = 3;
            gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
            gridView1.Columns["CUST_NAME"].VisibleIndex = 4;
            gridView1.Columns["DOC_NAME"].Caption = "اسم الطبيب";
            gridView1.Columns["DOC_NAME"].VisibleIndex = 5;
            gridView1.Columns["CLI_NAME"].Caption = "اسم العيادة";
            gridView1.Columns["CLI_NAME"].VisibleIndex = 6;
            gridView1.Columns["VIS_PRICE"].Caption = "القيمة";
            gridView1.Columns["VIS_PRICE"].VisibleIndex = 7;
            gridView1.Columns["VIS_DISCOUNT"].Caption = "الخصم";
            gridView1.Columns["VIS_DISCOUNT"].VisibleIndex = 8;
            gridView1.Columns["VIS_UNPAY"].Caption = "الباقي";
            gridView1.Columns["VIS_UNPAY"].VisibleIndex = 9;
            gridView1.Columns["VIS_TOTAL"].Caption = "الاجمالي";
            gridView1.Columns["VIS_TOTAL"].VisibleIndex = 10;
            gridView1.Columns["VIS_PAY_TYPE"].Caption = "طريقة الدفع";
            gridView1.Columns["VIS_PAY_TYPE"].VisibleIndex = 11;
            gridView1.BestFitColumns();
        }
        private void frmVIS_LIST_Load(object sender, EventArgs e)
        {
           
            foreach (var item in groupControl1.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            Clear_Date();
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
            GRID_DATA();

        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmCUST_LIST frmCUST_LIST = new LIST_FORM.frmCUST_LIST();
            frmCUST_LIST.ShowDialog();
            cmbCUST_NAME.Text = Classes.clsCUST.CUST_FULL_NAME;
        }

        private void lblCLINIC_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmCLINIC_LIST frmCLI_LIST = new LIST_FORM.frmCLINIC_LIST();
            frmCLI_LIST.ShowDialog();
            cmbCLI_NAME.Text = Classes.clsCLINIC.CLI_NAME;
        }

        private void lblDOC_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmDOC_LIST frmDOC_LIST = new LIST_FORM.frmDOC_LIST();
            frmDOC_LIST.ShowDialog();
            cmbDOC_NAME.Text = Classes.clsDOCTORS.DOC_NAME;
        }

        private void cmbCUST_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA();

        }

        private void txtVIS_ID_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();

        }

        private void cmbDOC_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA();

        }

        private void cmbCLI_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA();

        }

        private void dtpF_DATE_ValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();

        }

        private void dtpS_DATE_ValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();

        }

       
        private void btnEXCL_Click(object sender, EventArgs e)
        {
            string path = "output.xlsx";
            gridControl1.ExportToXlsx(path);
            Process.Start(path);
        }

        private void btnPRINT_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                frmVISLIST_REPORT vislistrep = new frmVISLIST_REPORT();
                frmVISLIST_REPORT.CUST_NAME = cmbCUST_NAME.Text;
                frmVISLIST_REPORT.CLI_NAME = cmbCLI_NAME.Text;
                frmVISLIST_REPORT.VIS_ID = txtVIS_ID.Text;
                frmVISLIST_REPORT.DOC_NAME = cmbDOC_NAME.Text;
                frmVISLIST_REPORT.F_DATE = Convert.ToDateTime(dtpF_DATE.Value);
                frmVISLIST_REPORT.S_DATE = Convert.ToDateTime(dtpS_DATE.Value);
                vislistrep.ShowDialog();
            }
            else
            {
                MessageBox.Show("لا يوجد بيانات للطباعة ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Stop);

            }

        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsVIS.Select_VIST(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["VIS_ID"]).ToString())).Rows.Count == 1)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

            }
        }
    }
}