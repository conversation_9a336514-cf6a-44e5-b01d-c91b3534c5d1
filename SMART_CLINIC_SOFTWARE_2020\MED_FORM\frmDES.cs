﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;

namespace SMART_CLINIC_SOFTWARE_2020.MED_FORM
{
    public partial class frmDES : DevExpress.XtraEditors.XtraForm
    {
        public frmDES()
        {
            InitializeComponent();
        }

        Classes.clsDES NclsDES = new Classes.clsDES();
        Classes.clsCUST NclsCUST = new Classes.clsCUST();

        public void clear_data()
        {
            gridControl1.DataSource = Classes.clsDES.DES_DATATABLE.DESbyDES_NAMEorDES_TYPE(txtSEARCH.Text,txtSEARCH.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["DES_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["DES_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CUST_ID"]);
            gridView1.Columns["DES_CODE"].Caption = "الرقم";
            gridView1.Columns["DES_NAME"].Caption = "اسم المرض";
            gridView1.Columns["DES_TYPE"].Caption = "نوع المرض";
            gridView1.Columns["DES_STATE"].Caption = "حالة المرض";
            gridView1.BestFitColumns();

            txtDES_Code.Text = Classes.clsDES.DES_DATATABLE.maxDES_CODE().Rows[0]["DES_CODE"].ToString();
            txtDES_Name.Text = "";
            txtDES_TYPE.Text = "";
            txtDES_NOTE.Text = "";
            cmbDES_STATE.Text = "";

            cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
            cmbCUST_ID.ValueMember = "CUST_ID";
            cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
            cmbCUST_NAME.ValueMember = "CUST_NAME";
            if (Classes.clsCUST.CUST_ID != 0)
            {
                cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
            }
            
        }

        private void frmDES_Load(object sender, EventArgs e)
        {
            //foreach (var item in groupControl1.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            //{
            //    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
            //    {
            //        item.Enabled = false;
            //    }
            //}
            clear_data();
            txtDES_Name.Focus();
        }

        private void btnCLEAR_SEARCH_Click(object sender, EventArgs e)
        {
            txtSEARCH.Text = "";
            txtSEARCH.Focus();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            clear_data();
        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmCUST_LIST frmCUST_LIST = new LIST_FORM.frmCUST_LIST();
            frmCUST_LIST.ShowDialog();
            cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtDES_Code.Text != "" && txtDES_Name.Text != "" && cmbCUST_ID.Text != "")
                {
                    Classes.clsDES.DES_DATATABLE.InsertDES(Convert.ToInt64(txtDES_Code.Text), txtDES_Name.Text, txtDES_TYPE.Text, txtDES_NOTE.Text, cmbDES_STATE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Convert.ToInt64(cmbCUST_ID.Text));
                    clear_data();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                //if (ex.Message.Contains("HOL_CUST_ID_INDEX"))
                //{
                //    MessageBox.Show("لا يجوز تسجيل اجازة في نفس التاريخ", "!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                //}
                //else
                //{
                //    MessageBox.Show(ex.Message, "!!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                //}
            }
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0 && txtDES_Code.Text != "" && txtDES_Name.Text != "" && cmbCUST_ID.Text != "")
            {
                Classes.clsDES.DES_DATATABLE.UpdateDES(Convert.ToInt64(txtDES_Code.Text), txtDES_Name.Text, txtDES_TYPE.Text, txtDES_NOTE.Text, cmbDES_STATE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Convert.ToInt64(cmbCUST_ID.Text), Classes.clsDES.DES_ID, Classes.clsDES.DES_ID);
                clear_data();
            }
            else
            {
                clear_data();
            }
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0 && txtDES_Code.Text != "" && txtDES_Name.Text != "" && cmbCUST_ID.Text != "")
            {
                Classes.clsDES.DES_DATATABLE.DeleteDES(Classes.clsDES.DES_ID, Convert.ToInt64(cmbCUST_ID.Text));
                clear_data();
            }
            else
            {
                clear_data();
            }
        }

     
        private void txtSEARCH_EditValueChanged(object sender, EventArgs e)
        {
            clear_data();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                NclsDES.Select_DES(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["DES_CODE"]).ToString()));
                txtDES_Code.Text = Classes.clsDES.DES_CODE.ToString();
                txtDES_Name.Text = Classes.clsDES.DES_NAME;
                txtDES_TYPE.Text = Classes.clsDES.DES_TYPE;
                txtDES_NOTE.Text = Classes.clsDES.DES_NOTE;
                cmbDES_STATE.Text = Classes.clsDES.DES_STATE;
                cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
                txtDES_Name.Focus();
            }
        }
    }
}