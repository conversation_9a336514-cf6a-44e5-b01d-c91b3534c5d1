﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;

namespace SMART_CLINIC_SOFTWARE_2020.MED_FORM
{
    public partial class frmDIAG_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmDIAG_LIST()
        {
            InitializeComponent();
        }

        Classes.clsDIAGNOIS NclsDIG = new Classes.clsDIAGNOIS();
        Classes.clsDIGLIST NclsDIGLIST = new Classes.clsDIGLIST();

        public void DIG_LIST_DATA()
        {
            gridControl1.DataSource = NclsDIG.Select_DIAGNOIS(txtDIG_Name.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["DIG_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["DIG_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["DIG_ID"].Caption = "كود التشخيص";
            gridView1.Columns["DIG_NAME"].Caption = "اسم التشخيص";
            gridView1.Columns["DIG_TYPE"].Caption = "نوع التشخيص";
            gridView1.BestFitColumns();
        }

        public void DIG_LIST_RECENT()
        {
            gridControl1.DataSource = Classes.clsDIGLIST.DIGLIST_DATATABLE.RECENT_DIAG_LISTbyDIAG_NAME(txtDIG_Name.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns["DIG_ID"].Caption = "كود التشخيص";
            gridView1.Columns["DIG_NAME"].Caption = "اسم التشخيص";
            gridView1.Columns["DIG_TYPE"].Caption = "نوع التشخيص";
            gridView1.BestFitColumns();
        }

        public void DIG_VISIT_LIST()
        {
            gridControl2.DataSource = Classes.clsDIGLIST.DIGLIST_DATATABLE.DIGLISTbyVIS_IDorCUST_ID(Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID);
            gridView2.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView2.OptionsView.EnableAppearanceEvenRow = true;
            gridView2.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView2.OptionsView.EnableAppearanceOddRow = true;
            gridView2.OptionsBehavior.Editable = false;
            gridView2.Columns.Remove(gridView2.Columns["DIGLIST_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["DIGLIST_DATE"]);
            gridView2.Columns.Remove(gridView2.Columns["DIGLIST_TIME"]);
            gridView2.Columns.Remove(gridView2.Columns["DIGLIST_NAME"]);
            gridView2.Columns.Remove(gridView2.Columns["DIG_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["VIS_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["CUST_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["CLI_ID"]);
            gridView2.Columns["DIGLIST_CODE"].Caption = "الرقم";
            gridView2.Columns["DIG_NAME"].Caption = "اسم التشخيص";
            gridView2.Columns["DIG_TYPE"].Caption = "نوع التشخيص";
            gridView2.BestFitColumns();
        }

        private void frmDIAG_LIST_Load(object sender, EventArgs e)
        {
            foreach (var item in this.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            DIG_LIST_DATA();
            DIG_VISIT_LIST();
            txtAPO_TIME.Text = string.Format(DateTime.Now.ToShortTimeString(), "hh:MM");

        }

        private void btnDIAG_LIST_Click(object sender, EventArgs e)
        {
            Project_Form.frmDIAGNOIS frmDIG = new Project_Form.frmDIAGNOIS();
            frmDIG.ShowDialog();
            DIG_LIST_DATA();

        }

        private void txtDIG_Name_EditValueChanged(object sender, EventArgs e)
        {
            if(grbDIAGLIST.Text == "قائمة التشخيص")
            {
                DIG_LIST_DATA();
            }
            else
            {
                DIG_LIST_RECENT();
            }
            
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtDIG_Name.Text = "";
            txtDIG_Name.Focus();
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                Classes.clsDIGLIST.DIGLIST_DATATABLE.DeleteDIGLIST(Classes.clsVISIT.VIS_ID, Convert.ToInt64(gridView2.GetRowCellValue(gridView2.FocusedRowHandle, gridView2.Columns["DIGLIST_CODE"]).ToString()), Classes.clsCUST.CUST_ID);
                DIG_VISIT_LIST();
            }
            else
            {
                DIG_VISIT_LIST();
            }
        }

        private void btnDELETEALL_Click(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                Classes.clsDIGLIST.DIGLIST_DATATABLE.DeleteALL_DIGLIST(Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID);
                DIG_VISIT_LIST();
            }
            else
            {
                DIG_VISIT_LIST();
            }
        }

       
        private void btnALL_LIST_Click(object sender, EventArgs e)
        {
            DIG_LIST_DATA();
            grbDIAGLIST.Text = "قائمة التشخيص";

        }

        private void btnMOREUSE_Click(object sender, EventArgs e)
        {
            DIG_LIST_RECENT();
            grbDIAGLIST.Text = "قائمة الاكثر استخدام";
        }

        private void btnOLD_DIG_Click(object sender, EventArgs e)
        {
            MED_FORM.frmOLD_DIAG_LIST frmOLD_DIAG = new MED_FORM.frmOLD_DIAG_LIST();
            frmOLD_DIAG.ShowDialog();
            DIG_VISIT_LIST();

        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                try
                {
                    Classes.clsDIGLIST.DIGLIST_DATATABLE.InsertDIGLIST(Convert.ToInt64(Classes.clsDIGLIST.DIGLIST_DATATABLE.maxDIGLIST_CODE().Rows[0]["DIGLIST_CODE"].ToString()), gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["DIG_NAME"]).ToString(), Convert.ToDateTime(string.Format(DateTime.Now.ToShortDateString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtAPO_TIME.Text.ToString(), "HH:MI")), Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["DIG_ID"]).ToString()), Classes.clsCUST.CUST_ID, Classes.clsVISIT.VIS_ID, Convert.ToInt64(Classes.clsCLINIC.CLI_ID));
                    DIG_VISIT_LIST();
                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("DIGLIST_NAME_INDEX"))
                    {
                        MessageBox.Show("التشخيص موجود في هذه الزيارة", "!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    else
                    {
                        MessageBox.Show(ex.Message, "!!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                DIG_VISIT_LIST();
            }
        }
    }
}