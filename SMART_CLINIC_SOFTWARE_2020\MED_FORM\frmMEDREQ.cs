﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;
using SMART_CLINIC_SOFTWARE_2020.LIST_FORM;

namespace SMART_CLINIC_SOFTWARE_2020.MED_FORM
{
    public partial class frmMEDREQ : DevExpress.XtraEditors.XtraForm
    {
        public frmMEDREQ()
        {
            InitializeComponent();
        }

        Classes.clsMEDREQ NclsMEDREQ = new Classes.clsMEDREQ();
        Classes.clsMEDCHEK NclsMEDCHEK = new Classes.clsMEDCHEK();
        long S_MEDREQ_CODE = Convert.ToInt64(Classes.clsMEDREQ.MEDREQ_DATATABLE.maxMEDREQ_CODE().Rows[0]["MEDREQ_CODE"]);

        public void MEDCHEK_DATA_LIST()
        {
            gridControl1.DataSource = NclsMEDCHEK.Select_MEDCHEK(txtMEDCHEK.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["MEDCHEK_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDCHEK_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["MEDCHEK_ID"].Caption = "رقم الفحص";
            gridView1.Columns["MEDCHEK_NAME"].Caption = "اسم الفحص";
            gridView1.Columns["MEDCHEK_TYPE"].Caption = "نوع الفحص";
            gridView1.Columns["MEDCHEK_PRICE"].Caption = "سعر الفحص";
            gridView1.BestFitColumns();
        }

        public void MEDCHEK_RECENT_LIST()
        {
            gridControl1.DataSource = Classes.clsMEDREQ.MEDREQ_DATATABLE.RECENT_MEDREQbyMEDREQ_NAME(txtMEDCHEK.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["MEDCHEK_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDREQ_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDREQ_NAME"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDREQ_DATE"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDREQ_TIME"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDREQ_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["COUNT"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDREQ_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDREQ_RESULT"]);
            gridView1.Columns.Remove(gridView1.Columns["VIS_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CUST_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["MEDCHEK_ID"].Caption = "رقم الفحص";
            gridView1.Columns["MEDCHEK_NAME"].Caption = "اسم الفحص";
            gridView1.Columns["MEDCHEK_TYPE"].Caption = "نوع الفحص";
            gridView1.Columns["MEDCHEK_PRICE"].Caption = "سعر الفحص";
            gridView1.BestFitColumns();
        }

        public void MEDREQ_VISIT_LIST()
        {
            gridControl2.DataSource = Classes.clsMEDREQ.MEDREQ_DATATABLE.MEDREQbyCUST_IDandVIS_ID(Classes.clsCUST.CUST_ID, Classes.clsVISIT.VIS_ID);
            gridView2.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView2.OptionsView.EnableAppearanceEvenRow = true;
            gridView2.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView2.OptionsView.EnableAppearanceOddRow = true;
            gridView2.OptionsBehavior.Editable = false;
            gridView2.Columns.Remove(gridView2.Columns["MEDCHEK_CODE"]);
            gridView2.Columns["MEDREQ_CODE"].Visible = false;
            gridView2.Columns.Remove(gridView2.Columns["MEDREQ_NAME"]);
            gridView2.Columns.Remove(gridView2.Columns["MEDREQ_DATE"]);
            gridView2.Columns.Remove(gridView2.Columns["MEDREQ_TIME"]);
            gridView2.Columns.Remove(gridView2.Columns["MEDREQ_NOTE"]);
            gridView2.Columns.Remove(gridView2.Columns["COUNT"]);
            gridView2.Columns.Remove(gridView2.Columns["MEDREQ_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["MEDREQ_STATE"]);
            gridView2.Columns.Remove(gridView2.Columns["VIS_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["CUST_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["CLI_ID"]);
            gridView2.Columns["MEDCHEK_ID"].Caption = "رقم الفحص";
            gridView2.Columns["MEDCHEK_ID"].VisibleIndex = 0;
            gridView2.Columns["MEDCHEK_NAME"].Caption = "اسم الفحص";
            gridView2.Columns["MEDCHEK_NAME"].VisibleIndex = 1;
            gridView2.Columns["MEDCHEK_TYPE"].Caption = "نوع الفحص";
            gridView2.Columns["MEDCHEK_TYPE"].VisibleIndex = 2;
            gridView2.Columns["MEDCHEK_PRICE"].Caption = "سعر الفحص";
            gridView2.Columns["MEDCHEK_PRICE"].VisibleIndex = 3;
            gridView2.Columns["MEDREQ_RESULT"].Caption = "نتيجة الفحص";
            gridView2.Columns["MEDREQ_RESULT"].VisibleIndex = 4;
            gridView2.BestFitColumns();
        }

        private void frmMEDREQ_Load(object sender, EventArgs e)
        {
            foreach (var item in this.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            MEDCHEK_DATA_LIST();
            MEDREQ_VISIT_LIST();
            txtAPO_TIME.Text = string.Format(DateTime.Now.ToShortTimeString(), "hh:MM");

            if (Classes.clsCUST.CUST_ID == 0 || (Classes.clsCUST.CUST_FULL_NAME == null || Classes.clsCUST.CUST_FULL_NAME == ""))
            {
                cmbCUST_ID.Text = "";
                cmbCUST_NAME.Text = "";
                lblCUST_LIST.Enabled = true;
            }
            else
            {
                cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
                cmbCUST_NAME.Text = Classes.clsCUST.CUST_FULL_NAME;
                lblCUST_LIST.Enabled = false;
            }
        }

        private void btnALL_LIST_Click(object sender, EventArgs e)
        {
            grbCHEKLIST.Text = "قائمة الفحوصات";
            MEDCHEK_DATA_LIST();

        }

        private void btnMOREUSE_Click(object sender, EventArgs e)
        {
            grbCHEKLIST.Text = "قائمة الاكثر استخدام";
            MEDCHEK_RECENT_LIST();
        }

        private void txtMEDCHEK_EditValueChanged(object sender, EventArgs e)
        {
            if (grbCHEKLIST.Text == "قائمة الفحوصات")
            {
                MEDCHEK_DATA_LIST();
            }
            else
            {
                MEDCHEK_RECENT_LIST();
            }
        }

        private void btnDIAG_LIST_Click(object sender, EventArgs e)
        {
            Project_Form.frmMEDCHEK frmMEDCHEK = new Project_Form.frmMEDCHEK();
            frmMEDCHEK.ShowDialog();
            MEDCHEK_DATA_LIST();
        }

      
        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                Classes.clsMEDREQ.MEDREQ_DATATABLE.DeleteMEDREQbyMEDCHEK_ID(Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID, Convert.ToInt64(gridView2.GetRowCellValue(gridView2.FocusedRowHandle, gridView2.Columns["MEDCHEK_ID"]).ToString()));
                MEDREQ_VISIT_LIST();
            }
            else
            {
                MEDREQ_VISIT_LIST();
            }
        }

        private void btnDELETEALL_Click(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                Classes.clsMEDREQ.MEDREQ_DATATABLE.DeleteALLMEDREQ(Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID);
                MEDREQ_VISIT_LIST();
            }
            else
            {
                MEDREQ_VISIT_LIST();
            }
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtMEDCHEK.Text = "";
            txtMEDCHEK.Focus();
        }


        private void btnNEW_Click(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                S_MEDREQ_CODE = Convert.ToInt64(Classes.clsMEDREQ.MEDREQ_DATATABLE.maxMEDREQ_CODE().Rows[0]["MEDREQ_CODE"]);
                S_MEDREQ_CODE = Convert.ToInt64(Classes.clsMEDREQ.MEDREQ_DATATABLE.maxMEDREQ_CODE().Rows[0]["MEDREQ_CODE"]) + 1;
                for (int i = 0; i < gridView2.RowCount; i++)
                {
                    Classes.clsMEDREQ.MEDREQ_DATATABLE.UpdateMEDREQ_STATE("CLOSE", Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID, Convert.ToInt64(gridView2.GetRowCellValue(i, "MEDCHEK_ID").ToString()));
                    MEDREQ_VISIT_LIST();

                }
            }
            MEDREQ_VISIT_LIST();
        }

        private void btnOLDMEDREQ_Click(object sender, EventArgs e)
        {
            frmOLD_MEDREQ_LIST frmOLDMEDREQ = new frmOLD_MEDREQ_LIST();
            frmOLDMEDREQ.ShowDialog();
            MEDREQ_VISIT_LIST();
        }

        private void btnPRINT_Click(object sender, EventArgs e)
        {
            try
            {
                frmMEDREQ_VIS_REPORT medreqreport = new frmMEDREQ_VIS_REPORT();
                frmMEDREQ_VIS_REPORT.VIS_ID = Classes.clsVISIT.VIS_ID;
                frmMEDREQ_VIS_REPORT.CUST_ID = Classes.clsCUST.CUST_ID;
                frmMEDREQ_VIS_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                frmMEDREQ_VIS_REPORT.MEDREQ_CODE = Convert.ToInt64(gridView2.GetRowCellValue(gridView2.FocusedRowHandle, gridView2.Columns["MEDREQ_CODE"]));
                medreqreport.ShowDialog();
                MEDREQ_VISIT_LIST();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);

            }
            
            //if (gridView2.RowCount > 0)
            //{
            //    S_MEDREQ_CODE = Convert.ToInt64(Classes.clsMEDREQ.MEDREQ_DATATABLE.maxMEDREQ_CODE().Rows[0]["MEDREQ_CODE"]);
            //    S_MEDREQ_CODE = Convert.ToInt64(Classes.clsMEDREQ.MEDREQ_DATATABLE.maxMEDREQ_CODE().Rows[0]["MEDREQ_CODE"]) + 1;
            //    for (int i = 0; i < gridView2.RowCount; i++)
            //    {
            //        Classes.clsMEDREQ.MEDREQ_DATATABLE.UpdateMEDREQ_STATE("CLOSE", Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID, Convert.ToInt64(gridView2.GetRowCellValue(i, "MEDCHEK_ID").ToString()));
            //        

            //    }
            //}
            //MEDREQ_VISIT_LIST();
        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            frmCUST_LIST custlist = new frmCUST_LIST();
            custlist.ShowDialog();
            cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
            cmbCUST_NAME.Text = Classes.clsCUST.CUST_FULL_NAME;
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (cmbCUST_ID.Text == "" && cmbCUST_NAME.Text == "")
            {
                MessageBox.Show("يرجى ادخال بيانات المريض", "!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblCUST_LIST.Focus();
                return;
            }
            if (gridView1.RowCount > 0)
            {
                try
                {
                    Classes.clsMEDREQ.MEDREQ_DATATABLE.InsertMEDREQ(S_MEDREQ_CODE, gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MEDCHEK_NAME"]).ToString(), Convert.ToDateTime(string.Format(DateTime.Now.ToShortDateString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtAPO_TIME.Text.ToString(), "HH:MI")), Classes.clsMEDREQ.MEDREQ_RESULT, "", Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MEDCHEK_ID"]).ToString()), Classes.clsCUST.CUST_ID, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsVISIT.VIS_ID, "OPEN");
                    MEDREQ_VISIT_LIST();
                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("MEDCHEK_ID_VIS_ID"))
                    {
                        MessageBox.Show("الفحص موجود في هذه الزيارة", "!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    else
                    {
                        MessageBox.Show(ex.Message, "!!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MEDREQ_VISIT_LIST();
            }
        }

        private void gridView2_DoubleClick(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                NclsMEDREQ.Select_MEDREQbyMEDCHEK_ID(Convert.ToInt64(gridView2.GetRowCellValue(gridView2.FocusedRowHandle, gridView2.Columns["MEDCHEK_ID"]).ToString()), Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID);
                frmMEDREQ_RESULT frmRESULT = new frmMEDREQ_RESULT();
                frmRESULT.ShowDialog();
                MEDREQ_VISIT_LIST();
            }
        }
    }
}