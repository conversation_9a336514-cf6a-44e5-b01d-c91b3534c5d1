﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.MED_FORM
{
    public partial class frmMEDREQ_RESULT : DevExpress.XtraEditors.XtraForm
    {
        public frmMEDREQ_RESULT()
        {
            InitializeComponent();
        }

        private void frmMEDREQ_RESULT_Load(object sender, EventArgs e)
        {
            foreach (var item in this.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            txtMEDCHEK_ID.Text = Classes.clsMEDREQ.MEDCHEK_ID.ToString();
            txtMEDCHEK_NAME.Text = Classes.clsMEDREQ.MEDREQ_NAME.ToString();
            txtMEDREQ_RESULT.Text = Classes.clsMEDREQ.MEDREQ_RESULT.ToString();
            txtMEDREQ_NOTE.Text = Classes.clsMEDREQ.MEDREQ_NOTE.ToString();

        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            if (txtMEDCHEK_ID.Text != "" && txtMEDCHEK_NAME.Text != "")
            {
                Classes.clsMEDREQ.MEDREQ_DATATABLE.UpdateMEDREQ_RESULT(txtMEDREQ_RESULT.Text, txtMEDREQ_NOTE.Text,Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID,Convert.ToInt64(txtMEDCHEK_ID.Text));
                this.Close();
            }
            else
            {
                MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                
            }
        }

        private void btnCLOSE_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}