﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.Project_Form;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using System.Data;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;
using SMART_CLINIC_SOFTWARE_2020.LIST_FORM;

namespace SMART_CLINIC_SOFTWARE_2020.MED_FORM
{
    public partial class frmMED_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmMED_LIST()
        {
            InitializeComponent();
        }
        Classes.clsMEDCIN NclsMED = new Classes.clsMEDCIN();
        Classes.clsMEDLIST NclsMEDLIST = new Classes.clsMEDLIST();
        Classes.clsDOS NclsDOS = new Classes.clsDOS();
        string MED_SOURSE = "خارجي";
        long MEDLIST_CODE = Convert.ToInt64(Classes.clsMEDLIST.MEDLIST_DATATABLE.maxMEDLIST_CODE().Rows[0]["MEDLIST_CODE"]) + 1;
        public void MEDLIST_GRIDVIEW()
        {
            gridControl1.DataSource = NclsMED.Select_MEDCIN(txtMED_Name.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["MED_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["MED_SOURSE"]);
            gridView1.Columns.Remove(gridView1.Columns["MED_PRICE"]);
            gridView1.Columns.Remove(gridView1.Columns["MED_S_NAME"]);
            gridView1.Columns["MED_ID"].Caption = "الرقم";
            gridView1.Columns["MED_NAME"].Caption = "اسم العلاج";
            //gridView1.Columns["MED_S_NAME"].Caption = "الاسم العلمي";
            //gridView1.Columns["MED_SOURSE"].Caption = "مصدر العلاج";
            //gridView1.Columns["MED_PRICE"].Caption = "سعر العلاج";
            gridView1.BestFitColumns();

            cmbCount.DataSource = Classes.clsDOS.DOS_DATATABLE.DOSbyDOS_TYPE(cmbCount.Tag.ToString());
            cmbCount.ValueMember = "DOS_NAME";
            cmbSize.DataSource = Classes.clsDOS.DOS_DATATABLE.DOSbyDOS_TYPE(cmbSize.Tag.ToString());
            cmbSize.ValueMember = "DOS_NAME";
            cmbType.DataSource = Classes.clsDOS.DOS_DATATABLE.DOSbyDOS_TYPE(cmbType.Tag.ToString());
            cmbType.ValueMember = "DOS_NAME";
            cmbTime.DataSource = Classes.clsDOS.DOS_DATATABLE.DOSbyDOS_TYPE(cmbTime.Tag.ToString());
            cmbTime.ValueMember = "DOS_NAME";
        }

        public void RECENT_MEDLIST()
        {
            gridControl1.DataSource = null;
            gridControl1.Refresh();
            gridControl1.DataSource = Classes.clsMEDLIST.MEDLIST_DATATABLE.RECENT_MEDLIST(txtMED_Name.Text, txtMED_Name.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["MED_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDLIST_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDLIST_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDLIST_NAME"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDLIST_DATE"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDLIST_TIME"]);
            gridView1.Columns.Remove(gridView1.Columns["DOS_NAME"]);
            gridView1.Columns.Remove(gridView1.Columns["VIS_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CUST_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["COUNT"]);
            gridView1.Columns.Remove(gridView1.Columns["MEDLIST_SOURSE"]);
            gridView1.Columns.Remove(gridView1.Columns["MED_SOURSE"]);
            gridView1.Columns.Remove(gridView1.Columns["MED_PRICE"]);
            gridView1.Columns.Remove(gridView1.Columns["MED_S_NAME"]);
            gridView1.Columns["MED_ID"].Caption = "الرقم";
            gridView1.Columns["MED_NAME"].Caption = "اسم الدواء";
            //gridView1.Columns["MED_S_NAME"].Caption = "الاسم العلمي";
            gridView1.BestFitColumns();
        }

        public void MEDLISTbyVIS()
        {
            gridControl2.DataSource = Classes.clsMEDLIST.MEDLIST_DATATABLE.MEDLISTbyCUST_IDorVIS_ID(Classes.clsVISIT.VIS_ID,Classes.clsCUST.CUST_ID);
            gridView2.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView2.OptionsView.EnableAppearanceEvenRow = true;
            gridView2.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView2.OptionsView.EnableAppearanceOddRow = true;
            gridView2.OptionsBehavior.Editable = false;
            gridView2.Columns.Remove(gridView2.Columns["MED_CODE"]);
            gridView2.Columns.Remove(gridView2.Columns["MEDLIST_ID"]);
            //gridView2.Columns.Remove(gridView2.Columns["MEDLIST_CODE"]);
            gridView2.Columns.Remove(gridView2.Columns["MEDLIST_NAME"]);
            gridView2.Columns.Remove(gridView2.Columns["MEDLIST_DATE"]);
            gridView2.Columns.Remove(gridView2.Columns["MEDLIST_TIME"]);
            gridView2.Columns.Remove(gridView2.Columns["VIS_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["CUST_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["CLI_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["COUNT"]);
            gridView2.Columns.Remove(gridView2.Columns["MEDLIST_SOURSE"]);
            gridView2.Columns.Remove(gridView2.Columns["MED_S_NAME"]);
            gridView2.Columns.Remove(gridView2.Columns["MED_PRICE"]);
            gridView2.Columns["MEDLIST_CODE"].Visible = false;
            gridView2.Columns["MED_ID"].Caption = "الرقم";
            gridView2.Columns["MED_ID"].VisibleIndex = 0;
            gridView2.Columns["MED_NAME"].Caption = "اسم الدواء";
            gridView2.Columns["MED_NAME"].VisibleIndex = 1;
            gridView2.Columns["DOS_NAME"].Caption = "الجرعة";
            gridView2.Columns["DOS_NAME"].VisibleIndex = 2;
            gridView2.Columns["MED_SOURSE"].Caption = "مصدر العلاج";
            gridView2.Columns["MED_SOURSE"].VisibleIndex = 3;
            gridView2.BestFitColumns();
        }

        private void btnMED_LIST_Click(object sender, EventArgs e)
        {
            frmMEDCIN frmMEDCINE = new frmMEDCIN();
            frmMEDCINE.ShowDialog();
            MEDLIST_GRIDVIEW();
        }

        private void frmMED_LIST_Load(object sender, EventArgs e)
        {
            foreach (var item in this.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            MEDLIST_GRIDVIEW();
            MEDLISTbyVIS();
            txtAPO_TIME.Text = string.Format(DateTime.Now.ToShortTimeString(), "hh:MM");

            if (Classes.clsCUST.CUST_ID == 0 || (Classes.clsCUST.CUST_FULL_NAME == null || Classes.clsCUST.CUST_FULL_NAME == ""))
            {
                cmbCUST_ID.Text = "";
                cmbCUST_NAME.Text = "";
                lblCUST_LIST.Enabled = true;
            }
            else
            {
                cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
                cmbCUST_NAME.Text = Classes.clsCUST.CUST_FULL_NAME;
                lblCUST_LIST.Enabled = false;
            }
        }

        private void btnALL_LIST_Click(object sender, EventArgs e)
        {
            grbDIAGLIST.Text = "قائمة الادوية";
            MEDLIST_GRIDVIEW();
        }

        private void btnMOREUSE_Click(object sender, EventArgs e)
        {
            grbDIAGLIST.Text = "قائمة الاكثر استخداما";
            RECENT_MEDLIST();
        }

        private void txtMED_Name_EditValueChanged(object sender, EventArgs e)
        {
            if (grbDIAGLIST.Text == "قائمة الادوية")
            {
                MEDLIST_GRIDVIEW();
            }
            else
            {
                RECENT_MEDLIST();
            }
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtMED_Name.Text = "";
            txtMED_Name.Focus();
        }

       
        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                Classes.clsMEDLIST.MEDLIST_DATATABLE.DeleteMEDLISTbyMEDLIST_ID(Convert.ToInt64(gridView2.GetRowCellValue(gridView2.FocusedRowHandle, gridView2.Columns["MED_ID"]).ToString()), Classes.clsCUST.CUST_ID, Classes.clsVISIT.VIS_ID);
                MEDLISTbyVIS();
            }
            else
            {
                MEDLISTbyVIS();
            }
        }

        private void btnDELETEALL_Click(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                Classes.clsMEDLIST.MEDLIST_DATATABLE.DeleteALLMEDLIST(Classes.clsCUST.CUST_ID, Classes.clsVISIT.VIS_ID);
                MEDLISTbyVIS();
            }
            else
            {
                MEDLISTbyVIS();
            }
        }

        private void chkMED_SOURCE_CheckedChanged(object sender, EventArgs e)
        {
            if (chkMED_SOURCE.Checked == true)
            {
                MED_SOURSE = "داخلي";
            }
            else
            {
                MED_SOURSE = "خارجي";
            }
        }

        private void btnSAVE_DOS_Click(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                Classes.clsMEDLIST.MEDLIST_DATATABLE.UpdateMEDLIST_DOS(cmbType.Text + " / " + cmbSize.Text + " / " + cmbCount.Text + " / " + cmbTime.Text, MED_SOURSE, Convert.ToInt64(gridView2.GetRowCellValue(gridView2.FocusedRowHandle, gridView2.Columns["MED_ID"]).ToString()), Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID);
                MEDLISTbyVIS();
            }
            else
            {
                MEDLISTbyVIS();
            }
        }

        private void btnDELETE_DOS_Click(object sender, EventArgs e)
        {
            if (chkMED_SOURCE.Checked == true)
            {
                MED_SOURSE = "داخلي";
            }
            else
            {
                MED_SOURSE = "خارجي";
            }
            if (gridView2.RowCount > 0)
            {
                Classes.clsMEDLIST.MEDLIST_DATATABLE.UpdateMEDLIST_DOS("-", MED_SOURSE, Convert.ToInt64(gridView2.GetRowCellValue(gridView2.FocusedRowHandle, gridView2.Columns["MED_ID"]).ToString()), Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID);
                MEDLISTbyVIS();
            }
            else
            {
                MEDLISTbyVIS();
            }
        }

        private void btnOLD_MED_Click(object sender, EventArgs e)
        {
            frmOLD_MED_LIST oldmed = new frmOLD_MED_LIST();
            oldmed.ShowDialog();
            MEDLISTbyVIS();

        }

        private void btnPRINT_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView2.RowCount > 0)
                {
                    frmMEDLIST_VIS_REPORT medlistrep = new frmMEDLIST_VIS_REPORT();
                    frmMEDLIST_VIS_REPORT.VIS_ID = Convert.ToInt64(Classes.clsVISIT.VIS_ID);
                    frmMEDLIST_VIS_REPORT.CUST_ID = Convert.ToInt64(Classes.clsCUST.CUST_ID);
                    frmMEDLIST_VIS_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                    frmMEDLIST_VIS_REPORT.MEDLIST_CODE = Convert.ToInt64(gridView2.GetRowCellValue(gridView2.FocusedRowHandle, gridView2.Columns["MEDLIST_CODE"]));
                    medlistrep.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);

            }         
        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            frmCUST_LIST custlist = new frmCUST_LIST();
            custlist.ShowDialog();
            cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
            cmbCUST_NAME.Text = Classes.clsCUST.CUST_FULL_NAME;
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (cmbCUST_ID.Text == "" && cmbCUST_NAME.Text == "")
            {
                MessageBox.Show("يرجى ادخال بيانات المريض", "!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblCUST_LIST.Focus();
                return;
            }
            if (gridView1.RowCount > 0)
            {
                try
                {
                    Classes.clsMEDLIST.MEDLIST_DATATABLE.InsertMEDLIST(MEDLIST_CODE, gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MED_NAME"]).ToString(), Convert.ToDateTime(string.Format(DateTime.Now.ToShortDateString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtAPO_TIME.Text.ToString(), "HH:MI")), cmbType.Text + " / " + cmbSize.Text + " / " + cmbCount.Text + " / " + cmbTime.Text, Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MED_ID"]).ToString()), Convert.ToInt64(cmbCUST_ID.Text), Classes.clsVISIT.VIS_ID, MED_SOURSE, Convert.ToInt64(Classes.clsCLINIC.CLI_ID));
                    MEDLISTbyVIS();
                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("MED_VIS_ID_INDEX"))
                    {
                        MessageBox.Show("العلاج موجود في هذه الزيارة", "!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    else
                    {
                        MessageBox.Show(ex.Message, "!!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MEDLISTbyVIS();
            }
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            frmDOS frmdos = new frmDOS();
            frmdos.ShowDialog();
            MEDLIST_GRIDVIEW();

        }
    }
}