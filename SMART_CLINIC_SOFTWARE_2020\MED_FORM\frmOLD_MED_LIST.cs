﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.LIST_FORM;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;

namespace SMART_CLINIC_SOFTWARE_2020.MED_FORM
{
    public partial class frmOLD_MED_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmOLD_MED_LIST()
        {
            InitializeComponent();
        }
        Classes.clsCLINIC NclsCLINIC = new Classes.clsCLINIC();
        Classes.clsCUST NclsCUST = new Classes.clsCUST();
        string MED_SOURCE;

        public void clear_data()
        {
            cmbCLI_ID.DataSource = NclsCLINIC.CLINIC_LIST();
            cmbCLI_ID.ValueMember = "CLI_ID";
            cmbCLI_NAME.DataSource = cmbCLI_ID.DataSource;
            cmbCLI_NAME.ValueMember = "CLI_NAME";
            cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
            cmbCUST_ID.ValueMember = "CUST_ID";
            cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
            cmbCUST_NAME.ValueMember = "CUST_NAME";
            txtVIS_ID.Text = "";
            txtMED_NAME.Text = "";
            dtpF_DATE.Value = DateTime.Now;
            dtpS_DATE.Value = DateTime.Now;
        }

        public void GRID_DATA()
        {
            if (chkMED_IN.Checked == true && chkMED_OUT.Checked == false)
            {
                MED_SOURCE = "داخلي";
            }
            else if (chkMED_IN.Checked == false && chkMED_OUT.Checked == true)
            {
                MED_SOURCE = "خارجي";
            }
            else if ((chkMED_IN.Checked == false && chkMED_OUT.Checked == false) || (chkMED_IN.Checked == true && chkMED_OUT.Checked == true))
            {
                MED_SOURCE = "";
            }
            gridControl1.DataSource = Classes.clsMEDLIST.MEDLIST_DATATABLE.OLD_MEDLISTbyCUST_NAMEandCLI_NAMEandVIS_IDandDATE(cmbCUST_NAME.Text, txtVIS_ID.Text, string.Format(dtpF_DATE.Value.ToShortDateString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToShortDateString(), "MM/dd/yyyy"), cmbCLI_ID.Text, txtMED_NAME.Text,MED_SOURCE);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["MED_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["DOS_NAME"]);
            gridView1.Columns["MEDLIST_CODE"].Visible = false;
            gridView1.Columns["MEDLIST_ID"].Caption = "رقم العلاج";
            gridView1.Columns["MEDLIST_ID"].VisibleIndex = 0;
            gridView1.Columns["MEDLIST_NAME"].Caption = "اسم العلاج";
            gridView1.Columns["MEDLIST_NAME"].VisibleIndex = 1;
            gridView1.Columns["MEDLIST_DATE"].Caption = "التاريخ";
            gridView1.Columns["MEDLIST_DATE"].VisibleIndex = 2;
            gridView1.Columns["MEDLIST_TIME"].Caption = "الوقت";
            gridView1.Columns["MEDLIST_TIME"].VisibleIndex = 3;
            gridView1.Columns["MED_SOURSE"].Caption = "المصدر";
            gridView1.Columns["MED_SOURSE"].VisibleIndex = 4;
            gridView1.Columns["CUST_ID"].Caption = "رقم المريض";
            gridView1.Columns["CUST_ID"].VisibleIndex = 5;
            gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
            gridView1.Columns["CUST_NAME"].VisibleIndex = 6;
            gridView1.Columns["CLI_ID"].Caption = "رقم العيادة";
            gridView1.Columns["CLI_ID"].VisibleIndex = 7;
            gridView1.Columns["CLI_NAME"].Caption = "اسم العيادة";
            gridView1.Columns["CLI_NAME"].VisibleIndex = 8;
            gridView1.Columns["VIS_ID"].Caption = "رقم الزيارة";
            gridView1.Columns["VIS_ID"].VisibleIndex = 9;
            gridView1.BestFitColumns();
        }
        private void frmOLD_MED_LIST_Load(object sender, EventArgs e)
        {
            foreach (var item in groupControl1.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            if (Classes.clsCUST.CUST_ID != 0)
            {
                cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
                cmbCUST_ID.ValueMember = "CUST_ID";
                cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
                cmbCUST_NAME.ValueMember = "CUST_NAME";
                cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
            }
            else
            {
                cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
                cmbCUST_ID.ValueMember = "CUST_ID";
                cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
                cmbCUST_NAME.ValueMember = "CUST_NAME";
            }
            if (Classes.clsCLINIC.CLI_ID != 0)
            {
                cmbCLI_ID.DataSource = NclsCLINIC.CLINIC_LIST();
                cmbCLI_ID.ValueMember = "CLI_ID";
                cmbCLI_NAME.DataSource = cmbCLI_ID.DataSource;
                cmbCLI_NAME.ValueMember = "CLI_NAME";
                cmbCLI_ID.Text = Classes.clsCLINIC.CLI_ID.ToString();
            }
            else
            {
                cmbCLI_ID.DataSource = NclsCLINIC.CLINIC_LIST();
                cmbCLI_ID.ValueMember = "CLI_ID";
                cmbCLI_NAME.DataSource = cmbCLI_ID.DataSource;
                cmbCLI_NAME.ValueMember = "CLI_NAME";
            }

            GRID_DATA();
           
        }

        private void chkMED_IN_CheckedChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void chkMED_OUT_CheckedChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void cmbCUST_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void cmbCLI_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void dtpF_DATE_ValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

 
        private void txtVIS_ID_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtMED_NAME_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            clear_data();
        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmCUST_LIST frmCUST_LIST = new LIST_FORM.frmCUST_LIST();
            frmCUST_LIST.ShowDialog();
            if (Classes.clsCUST.CUST_ID != 0)
            {
                cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
            }
            else
            {
                cmbCUST_ID.Text = "";
                cmbCUST_NAME.Text = "";
            }
        }

        private void lblCLINIC_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmCLINIC_LIST frmCLI_LIST = new LIST_FORM.frmCLINIC_LIST();
            frmCLI_LIST.ShowDialog();
            if (Classes.clsCLINIC.CLI_ID != 0)
            {
                cmbCLI_ID.Text = Classes.clsCLINIC.CLI_ID.ToString();
                cmbCLI_NAME.Text = Classes.clsCLINIC.CLI_NAME;
            }
            else
            {
                cmbCLI_ID.Text = "";
                cmbCLI_NAME.Text = "";
            }
        }

        private void lblVIS_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmVIS_LIST frmVIS_LIST = new LIST_FORM.frmVIS_LIST();
            frmVIS_LIST.ShowDialog();
            if (Classes.clsVISIT.VIS_ID != 0)
            {
                txtVIS_ID.Text = Classes.clsVISIT.VIS_ID.ToString();
            }
            else
            {
                txtVIS_ID.Text = "";
            }
        }

        private void lblMED_LIST_Click(object sender, EventArgs e)
        {
            frmMEDCIN_LIST medlist = new frmMEDCIN_LIST();
            medlist.ShowDialog();
            txtMED_NAME.Text = Classes.clsMEDCIN.MED_NAME;

        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                Classes.clsMEDLIST.MEDLIST_DATATABLE.DeleteOLD_MED_LIST(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MEDLIST_ID"]).ToString()), Convert.ToInt64(cmbCUST_ID.Text), Convert.ToInt64(cmbCLI_ID.Text));
                GRID_DATA();
            }
            else
            {
                GRID_DATA();
            }
        }

        private void btnPRINT_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && cmbCLI_ID.Text != "" && cmbCUST_ID.Text != "")
                {
                    frmMEDLIST_REPORT medlistrep = new frmMEDLIST_REPORT();
                    frmMEDLIST_REPORT.CUST_ID = Convert.ToInt64(cmbCUST_ID.Text);
                    frmMEDLIST_REPORT.CLI_ID = Convert.ToInt64(cmbCLI_ID.Text);
                    medlistrep.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);

            }  
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && cmbCLI_ID.Text != "" && cmbCUST_ID.Text != "")
                {
                    frmMEDLIST_VIS_REPORT medlistvisrep = new frmMEDLIST_VIS_REPORT();
                    frmMEDLIST_VIS_REPORT.CUST_ID = Convert.ToInt64(cmbCUST_ID.Text);
                    frmMEDLIST_VIS_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                    frmMEDLIST_VIS_REPORT.VIS_ID = Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["VIS_ID"]));
                    frmMEDLIST_VIS_REPORT.MEDLIST_CODE = Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MEDLIST_CODE"]));
                    medlistvisrep.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);

            }
        }
    }
}