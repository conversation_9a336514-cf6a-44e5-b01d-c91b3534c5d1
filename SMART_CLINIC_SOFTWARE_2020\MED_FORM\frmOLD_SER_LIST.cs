﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;

namespace SMART_CLINIC_SOFTWARE_2020.MED_FORM
{
    public partial class frmOLD_SER_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmOLD_SER_LIST()
        {
            InitializeComponent();
        }

        Classes.clsCLINIC NclsCLINIC = new Classes.clsCLINIC();
        Classes.clsCUST NclsCUST = new Classes.clsCUST();

        public void clear_data()
        {
            cmbCLI_ID.DataSource = NclsCLINIC.CLINIC_LIST();
            cmbCLI_ID.ValueMember = "CLI_ID";
            cmbCLI_NAME.DataSource = cmbCLI_ID.DataSource;
            cmbCLI_NAME.ValueMember = "CLI_NAME";
            cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
            cmbCUST_ID.ValueMember = "CUST_ID";
            cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
            cmbCUST_NAME.ValueMember = "CUST_NAME";
            txtVIS_ID.Text = "";
            txtSER_NAME.Text = "";
            dtpF_DATE.Value = DateTime.Now;
            dtpS_DATE.Value = DateTime.Now;
        }
        public void GRID_DATA()
        {
            gridControl1.DataSource = Classes.clsSERLIST.SERLIST_DATATABLE.OLD_SERLISTbyCUST_IDandVIS_IDandCLI_IDandSERLIST_DATE(cmbCUST_NAME.Text, txtVIS_ID.Text, string.Format(dtpF_DATE.Value.ToShortDateString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToShortDateString(), "MM/dd/yyyy"), cmbCLI_ID.Text, txtSER_NAME.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["SERLIST_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["SERLIST_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["SER_PRICE_TOTAL"]);
            gridView1.Columns.Remove(gridView1.Columns["SER_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["SERLIST_ID"].Caption = "رقم الاجراء";
            gridView1.Columns["SERLIST_ID"].VisibleIndex = 0;
            gridView1.Columns["SERLIST_NAME"].Caption = "اسم الاجراء";
            gridView1.Columns["SERLIST_NAME"].VisibleIndex = 1;
            gridView1.Columns["SERLIST_DATE"].Caption = "التاريخ";
            gridView1.Columns["SERLIST_DATE"].VisibleIndex = 2;
            gridView1.Columns["SERLIST_TIME"].Caption = "الوقت";
            gridView1.Columns["SERLIST_TIME"].VisibleIndex = 3;
            gridView1.Columns["CUST_ID"].Caption = "رقم المريض";
            gridView1.Columns["CUST_ID"].VisibleIndex = 4;
            gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
            gridView1.Columns["CUST_NAME"].VisibleIndex = 5;
            //gridView1.Columns["CLI_ID"].Caption = "رقم العيادة";
            //gridView1.Columns["CLI_ID"].VisibleIndex = 6;
            gridView1.Columns["CLI_NAME"].Caption = "اسم العيادة";
            gridView1.Columns["CLI_NAME"].VisibleIndex = 6;
            gridView1.Columns["VIS_ID"].Caption = "رقم الزيارة";
            gridView1.Columns["VIS_ID"].VisibleIndex = 7;
            gridView1.BestFitColumns();
        }
        private void frmOLD_SER_LIST_Load(object sender, EventArgs e)
        {
            foreach (var item in groupControl1.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            if (Classes.clsCUST.CUST_ID != 0)
            {
                cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
                cmbCUST_ID.ValueMember = "CUST_ID";
                cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
                cmbCUST_NAME.ValueMember = "CUST_NAME";
                cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
            }
            else
            {
                cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
                cmbCUST_ID.ValueMember = "CUST_ID";
                cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
                cmbCUST_NAME.ValueMember = "CUST_NAME";
            }
            if (Classes.clsCLINIC.CLI_ID != 0)
            {
                cmbCLI_ID.DataSource = NclsCLINIC.CLINIC_LIST();
                cmbCLI_ID.ValueMember = "CLI_ID";
                cmbCLI_NAME.DataSource = cmbCLI_ID.DataSource;
                cmbCLI_NAME.ValueMember = "CLI_NAME";
                cmbCLI_ID.Text = Classes.clsCLINIC.CLI_ID.ToString();
            }
            else
            {
                cmbCLI_ID.DataSource = NclsCLINIC.CLINIC_LIST();
                cmbCLI_ID.ValueMember = "CLI_ID";
                cmbCLI_NAME.DataSource = cmbCLI_ID.DataSource;
                cmbCLI_NAME.ValueMember = "CLI_NAME";
            }

            GRID_DATA();
        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmCUST_LIST frmCUST_LIST = new LIST_FORM.frmCUST_LIST();
            frmCUST_LIST.ShowDialog();
            cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
        }

        private void lblCLINIC_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmCLINIC_LIST frmCLI_LIST = new LIST_FORM.frmCLINIC_LIST();
            frmCLI_LIST.ShowDialog();
            cmbCLI_ID.Text = Classes.clsCLINIC.CLI_ID.ToString();
            cmbCLI_NAME.Text = Classes.clsCLINIC.CLI_NAME;
        }

        private void lblVIS_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmVIS_LIST frmVIS_LIST = new LIST_FORM.frmVIS_LIST();
            frmVIS_LIST.ShowDialog();
            txtVIS_ID.Text = Classes.clsVISIT.VIS_ID.ToString();
        }

        private void lblSER_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmSERVICE_LIST frmService_LIST = new LIST_FORM.frmSERVICE_LIST();
            frmService_LIST.ShowDialog();
            txtSER_NAME.Text = Classes.clsSERVICE.SER_NAME;
        }

        private void cmbCUST_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void dtpF_DATE_ValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void dtpS_DATE_ValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void cmbCLI_NAME_TextChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtVIS_ID_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtSER_NAME_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            clear_data();
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                Classes.clsSERLIST.SERLIST_DATATABLE.DeleteOLD_SERLIST(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["SERLIST_ID"]).ToString()), Convert.ToInt64(cmbCUST_ID.Text), Convert.ToInt64(cmbCLI_ID.Text));
                GRID_DATA();
            }
            else
            {
                GRID_DATA();
            }
        }

        private void btnPRINT_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbCUST_ID.Text != "" && cmbCLI_ID.Text != "")
                {
                    frmSERLIST_REP frmserrep = new frmSERLIST_REP();
                    frmSERLIST_REP.CUST_ID = Convert.ToInt64(cmbCUST_ID.Text);
                    frmSERLIST_REP.CLI_ID = Convert.ToInt64(cmbCLI_ID.Text);
                    frmserrep.ShowDialog();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار بيانات المريض و بيانات العيادة", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Question);
                    cmbCUST_ID.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);

            }
           

        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    frmSERLIST_VIS_REPORT serlistrep = new frmSERLIST_VIS_REPORT();
                    frmSERLIST_VIS_REPORT.VIS_ID = Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["VIS_ID"]).ToString());
                    frmSERLIST_VIS_REPORT.CUST_ID = Convert.ToInt64(cmbCUST_ID.Text);
                    frmSERLIST_VIS_REPORT.CLI_ID = Convert.ToInt64(cmbCLI_ID.Text);
                    serlistrep.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);

            }
           
        }
    }
}