﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;

namespace SMART_CLINIC_SOFTWARE_2020.MED_FORM
{
    public partial class frmSER_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmSER_LIST()
        {
            InitializeComponent();
        }

        Classes.clsSERVICE NclsSER = new Classes.clsSERVICE();
        Classes.clsSERLIST NclsSERLIST = new Classes.clsSERLIST();

        public void SER_LIST_DATA()
        {
            gridControl1.DataSource = NclsSER.Select_SERVICE(txtSER_Name.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["SER_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["SER_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["SER_ID"].Caption = "الرقم";
            gridView1.Columns["SER_NAME"].Caption = "اسم الاجراء";
            gridView1.Columns["SER_TYPE"].Caption = "نوع الاجراء";
            gridView1.Columns["SER_PRICE"].Caption = "سعر الاجراء";
            gridView1.BestFitColumns();
        }

        public void SER_LIST_RECENT()
        {
            gridControl1.DataSource = Classes.clsSERLIST.SERLIST_DATATABLE.RECENT_SERLISTbySER_NAME(txtSER_Name.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["SER_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["SER_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["SERLIST_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["SERLIST_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["SERLIST_DATE"]);
            gridView1.Columns.Remove(gridView1.Columns["SERLIST_NAME"]);
            gridView1.Columns.Remove(gridView1.Columns["SERLIST_TYPE"]);
            gridView1.Columns.Remove(gridView1.Columns["SERLIST_TIME"]);
            gridView1.Columns.Remove(gridView1.Columns["SERLIST_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["SER_PRICE_TOTAL"]);
            gridView1.Columns.Remove(gridView1.Columns["COUNT"]);
            gridView1.Columns.Remove(gridView1.Columns["CUST_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["VIS_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["SER_ID"].Caption = "الرقم";
            gridView1.Columns["SER_NAME"].Caption = "اسم الاجراء";
            gridView1.Columns["SER_TYPE"].Caption = "نوع الاجراء";
            gridView1.Columns["SER_PRICE"].Caption = "سعر الاجراء";
            gridView1.BestFitColumns();
        }

        public void SER_VISIT_LIST()
        {
            gridControl2.DataSource = Classes.clsSERLIST.SERLIST_DATATABLE.SERLISTbyCUST_IDorVIS_ID(Classes.clsCUST.CUST_ID, Classes.clsVISIT.VIS_ID);
            gridView2.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView2.OptionsView.EnableAppearanceEvenRow = true;
            gridView2.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView2.OptionsView.EnableAppearanceOddRow = true;
            gridView2.OptionsBehavior.Editable = false;
            gridView2.Columns.Remove(gridView2.Columns["SER_CODE"]);
            gridView2.Columns.Remove(gridView2.Columns["SER_NOTE"]);
            gridView2.Columns.Remove(gridView2.Columns["SERLIST_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["SERLIST_CODE"]);
            gridView2.Columns.Remove(gridView2.Columns["SERLIST_DATE"]);
            gridView2.Columns.Remove(gridView2.Columns["SERLIST_NAME"]);
            gridView2.Columns.Remove(gridView2.Columns["SERLIST_TYPE"]);
            gridView2.Columns.Remove(gridView2.Columns["SERLIST_TIME"]);
            gridView2.Columns.Remove(gridView2.Columns["SERLIST_NOTE"]);
            gridView2.Columns.Remove(gridView2.Columns["SER_PRICE_TOTAL"]);
            gridView2.Columns.Remove(gridView2.Columns["CUST_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["VIS_ID"]);
            gridView2.Columns.Remove(gridView2.Columns["CLI_ID"]);
            gridView2.Columns["SER_ID"].Caption = "الرقم";
            gridView2.Columns["SER_NAME"].Caption = "اسم الاجراء";
            gridView2.Columns["SER_TYPE"].Caption = "نوع الاجراء";
            gridView2.Columns["SER_PRICE"].Caption = "سعر الاجراء";
            gridView2.BestFitColumns();
        }

        private void frmSER_LIST_Load(object sender, EventArgs e)
        {
            foreach (var item in this.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            SER_LIST_DATA();
            SER_VISIT_LIST();
            txtAPO_TIME.Text = string.Format(DateTime.Now.ToShortTimeString(), "hh:MM");

        }

        private void txtSER_Name_EditValueChanged(object sender, EventArgs e)
        {
            if (grbSERLIST.Text == "قائمة الاجراءات")
            {
                SER_LIST_DATA();
            }
            else
            {
                SER_LIST_RECENT();
            }
        }

        private void btnSERVICELIST_Click(object sender, EventArgs e)
        {
            grbSERLIST.Text = "قائمة الاجراءات";
            SER_LIST_DATA();
        }

        private void btnMOREUSE_Click(object sender, EventArgs e)
        {
            grbSERLIST.Text = "قائمة الاكثر استخدام";
            SER_LIST_RECENT();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtSER_Name.Text = "";
            txtSER_Name.Focus();
        }

        private void btnNEW_SER_Click(object sender, EventArgs e)
        {
            Project_Form.frmSERVICE frmSER = new Project_Form.frmSERVICE();
            frmSER.ShowDialog();
            SER_LIST_DATA();
        }

     
        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                Classes.clsSERLIST.SERLIST_DATATABLE.DeleteSERLISTbySER_ID(Convert.ToInt64(gridView2.GetRowCellValue(gridView2.FocusedRowHandle, gridView2.Columns["SER_ID"]).ToString()),Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID);
                SER_VISIT_LIST();
            }
            else
            {
                SER_VISIT_LIST();
            }
        }

        private void btnDELETEALL_Click(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                Classes.clsSERLIST.SERLIST_DATATABLE.DeleteSERLIST(Classes.clsVISIT.VIS_ID, Classes.clsCUST.CUST_ID);
                SER_VISIT_LIST();
            }
            else
            {
                SER_VISIT_LIST();
            }
        }

        private void frmSER_LIST_FormClosing(object sender, FormClosingEventArgs e)
        {
            Classes.clsSERLIST.SERLIST_PRICE_TOTAL = 0;
            for (int i = 0; i <= gridView2.RowCount - 1; i++)
            {
                Classes.clsSERLIST.SERLIST_PRICE_TOTAL = Classes.clsSERLIST.SERLIST_PRICE_TOTAL + Convert.ToDecimal(gridView2.GetRowCellValue(i, "SER_PRICE"));

            }
        }

        private void btnOLD_SER_Click(object sender, EventArgs e)
        {
            frmOLD_SER_LIST oldser = new frmOLD_SER_LIST();
            oldser.ShowDialog();
        }

        private void btnPRINT_SERLIST_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView2.RowCount > 0)
                {
                    frmSERLIST_VIS_REPORT serlistrep = new frmSERLIST_VIS_REPORT();
                    frmSERLIST_VIS_REPORT.VIS_ID = Classes.clsVISIT.VIS_ID;
                    frmSERLIST_VIS_REPORT.CUST_ID = Classes.clsCUST.CUST_ID;
                    frmSERLIST_VIS_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                    frmSERLIST_VIS_REPORT.DOC_NAME = Classes.clsDOCTORS.DOC_LOG_NAME;
                    serlistrep.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
               
            }          
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                try
                {
                    Classes.clsSERLIST.SERLIST_DATATABLE.InsertSERLIST(Convert.ToInt64(Classes.clsSERLIST.SERLIST_DATATABLE.maxSERLIST_CODE().Rows[0]["SERLIST_CODE"].ToString()), gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["SER_NAME"]).ToString(), Convert.ToDateTime(string.Format(DateTime.Now.ToShortDateString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtAPO_TIME.Text.ToString(), "HH:MI")), 1, "", Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["SER_ID"]).ToString()), Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsCUST.CUST_ID, Classes.clsVISIT.VIS_ID);
                    SER_VISIT_LIST();

                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("SERLIST_VISIT_NAME"))
                    {
                        MessageBox.Show("الاجراء موجود في هذه الزيارة", "!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    else
                    {
                        MessageBox.Show(ex.Message, "!!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                SER_VISIT_LIST();
            }
        }
    }
}