﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;

namespace SMART_CLINIC_SOFTWARE_2020.MED_FORM
{
    public partial class frm_ENDO : DevExpress.XtraEditors.XtraForm
    {
        public frm_ENDO()
        {
            InitializeComponent();
        }
        Classes.clsENDO NclsENDO = new Classes.clsENDO();
        Classes.clsCUST NclsCUST = new Classes.clsCUST();

        public void clear_data()
        {
            gridControl1.DataSource = Classes.clsENDO.ENDO_DATATABLE.ENDObyTYPE_VISIT(txtSEARCH.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["ENDO_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CUST_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["VIS_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["ONE_CANAL"]);
            gridView1.Columns.Remove(gridView1.Columns["MB"]);
            gridView1.Columns.Remove(gridView1.Columns["ML"]);
            gridView1.Columns.Remove(gridView1.Columns["D"]);
            gridView1.Columns.Remove(gridView1.Columns["P"]);
            gridView1.Columns.Remove(gridView1.Columns["DB"]);
            gridView1.Columns.Remove(gridView1.Columns["B"]);
            gridView1.Columns.Remove(gridView1.Columns["M"]);
            gridView1.Columns.Remove(gridView1.Columns["COST_VISIT"]);
            
            gridView1.Columns["ENDO_CODE"].Caption = "الرقم";
            gridView1.Columns["TYPE_VISIT"].Caption = "نوع الجلسة";
            gridView1.Columns["ENDO_DATE"].Caption = "تاريخ الجلسة";
            gridView1.Columns["ENDO_TIME"].Caption = "وقت الجلسة";
            gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
            gridView1.BestFitColumns();

            txtENDO_Code.Text = Classes.clsENDO.ENDO_DATATABLE.maxENDO_CODE().Rows[0]["ENDO_CODE"].ToString();
            txtVIS_TYPE.Text = "";
            dtpENDO_DATE.Value = DateTime.Now;
            txtENDO_TIME.Text = string.Format(DateTime.Now.ToShortTimeString(), "hh:MM");
            cmbCOST.Text = "";
            txtONE_CANAL.Text = "";
            txtMB.Text = "";
            txtML.Text = "";
            txtD.Text = "";
            txtP.Text = "";
            txtDB.Text = "";
            txtB.Text = "";
            txtM.Text = "";
            txtNOTE.Text = "";

            cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
            cmbCUST_ID.ValueMember = "CUST_ID";
            cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
            cmbCUST_NAME.ValueMember = "CUST_NAME";
            if (Classes.clsCUST.CUST_ID != 0)
            {
                cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
            }
        }
        private void frm_ENDO_Load(object sender, EventArgs e)
        {
            foreach (var item in groupControl1.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            clear_data();
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtENDO_Code.Text != "" && txtVIS_TYPE.Text != "" && cmbCUST_ID.Text != "")
                {
                    Classes.clsENDO.ENDO_DATATABLE.InsertENDO(Convert.ToInt64(txtENDO_Code.Text), txtVIS_TYPE.Text, Convert.ToDateTime(string.Format(dtpENDO_DATE.Value.ToString(), "yyyy/dd/MM")), TimeSpan.Parse(string.Format(txtENDO_TIME.Text.ToString(), "HH:MI")), txtONE_CANAL.Text, txtMB.Text, txtML.Text, txtD.Text, txtP.Text, txtDB.Text, txtB.Text, txtM.Text, cmbCOST.Text, txtNOTE.Text, Convert.ToInt64(cmbCUST_ID.Text),1,1);
                    clear_data();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("TYPE_VISIT_INDEX"))
                {
                    MessageBox.Show("لا يجوز تسجيل جلسة في نفس النوع و اسم المريض", "!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    MessageBox.Show(ex.Message, "!!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmCUST_LIST frmCUST_LIST = new LIST_FORM.frmCUST_LIST();
            frmCUST_LIST.ShowDialog();
            cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsENDO.SELECT_ENDO(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["ENDO_CODE"]).ToString()));
                    txtENDO_Code.Text = Classes.clsENDO.ENDO_CODE.ToString();
                    txtVIS_TYPE.Text = Classes.clsENDO.TYPE_VISIT;
                    dtpENDO_DATE.Value = Convert.ToDateTime(string.Format(Classes.clsENDO.ENDO_DATE, "yyyy/MM/dd"));
                    txtENDO_TIME.Text = string.Format(Classes.clsENDO.ENDO_TIME, "hh:MI");
                    txtONE_CANAL.Text = Classes.clsENDO.ONE_CANAL;
                    txtMB.Text = Classes.clsENDO.MB;
                    txtML.Text = Classes.clsENDO.ML;
                    txtD.Text = Classes.clsENDO.D;
                    txtP.Text = Classes.clsENDO.P;
                    txtDB.Text = Classes.clsENDO.DB;
                    txtB.Text = Classes.clsENDO.B;
                    txtM.Text = Classes.clsENDO.M;
                    cmbCOST.Text = Classes.clsENDO.COST_VISIT;
                    txtNOTE.Text = Classes.clsENDO.NOTE;
                    cmbCUST_ID.Text = Classes.clsENDO.CUST_ID.ToString();

                    txtVIS_TYPE.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "!!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
           
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            clear_data();
            lblONE_CANAL.Visible = false;
            txtONE_CANAL.Visible = false;
            lblMB.Visible = false;
            txtMB.Visible = false;
            lblML.Visible = false;
            txtML.Visible = false;
            lblD.Visible = false;
            txtD.Visible = false;
            lblP.Visible = false;
            txtP.Visible = false;
            lblDB.Visible = false;
            txtDB.Visible = false;
            lblB.Visible = false;
            txtB.Visible = false;
            lblM.Visible = false;
            txtM.Visible = false;
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0 && txtENDO_Code.Text != "" && txtVIS_TYPE.Text != "" && cmbCUST_ID.Text != "")
            {
                Classes.clsENDO.ENDO_DATATABLE.UpdateENDO(Convert.ToInt64(txtENDO_Code.Text), txtVIS_TYPE.Text, Convert.ToDateTime(string.Format(dtpENDO_DATE.Value.ToString(), "yyyy/dd/MM")), TimeSpan.Parse(string.Format(txtENDO_TIME.Text.ToString(), "HH:MI")), txtONE_CANAL.Text, txtMB.Text, txtML.Text, txtD.Text, txtP.Text, txtDB.Text, txtB.Text, txtM.Text, cmbCOST.Text, txtNOTE.Text, Convert.ToInt64(cmbCUST_ID.Text), 1, 1,Classes.clsENDO.ENDO_ID,Classes.clsENDO.ENDO_ID);
                clear_data();
            }
            else
            {
                clear_data();
            }
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0 && txtENDO_Code.Text != "" && txtVIS_TYPE.Text != "" && cmbCUST_ID.Text != "")
            {
                Classes.clsENDO.ENDO_DATATABLE.DeleteENDO(Classes.clsENDO.ENDO_ID);
                clear_data();
            }
            else
            {
                clear_data();
            }
        }

        private void txtSEARCH_EditValueChanged(object sender, EventArgs e)
        {
            clear_data();
        }

        private void btnCLEAN_Click(object sender, EventArgs e)
        {
            txtSEARCH.Text = "";
            txtSEARCH.Focus();
        }

        private void cmbCOST_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbCOST.Text == "ANTERIOR")
            {
                lblONE_CANAL.Visible = true;
                txtONE_CANAL.Visible = true;
                lblMB.Visible = false;
                txtMB.Visible = false;
                lblML.Visible = false;
                txtML.Visible = false;
                lblD.Visible = false;
                txtD.Visible = false;
                lblP.Visible = false;
                txtP.Visible = false;
                lblDB.Visible = false;
                txtDB.Visible = false;
                lblB.Visible = false;
                txtB.Visible = false;
                lblM.Visible = false;
                txtM.Visible = false;
            }
            else if(cmbCOST.Text == "PREMOLAR_UPPER")
            {
                lblONE_CANAL.Visible = false;
                txtONE_CANAL.Visible = false;
                lblMB.Visible = false;
                txtMB.Visible = false;
                lblML.Visible = false;
                txtML.Visible = false;
                lblD.Visible = false;
                txtD.Visible = false;
                lblP.Visible = true;
                txtP.Visible = true;
                lblDB.Visible = false;
                txtDB.Visible = false;
                lblB.Visible = true;
                txtB.Visible = true;
                lblM.Visible = false;
                txtM.Visible = false;
            }
            else if (cmbCOST.Text == "PREMOLAR_LOWER")
            {
                lblONE_CANAL.Visible = true;
                txtONE_CANAL.Visible = true;
                lblMB.Visible = false;
                txtMB.Visible = false;
                lblML.Visible = false;
                txtML.Visible = false;
                lblD.Visible = false;
                txtD.Visible = false;
                lblP.Visible = false;
                txtP.Visible = false;
                lblDB.Visible = false;
                txtDB.Visible = false;
                lblB.Visible = false;
                txtB.Visible = false;
                lblM.Visible = false;
                txtM.Visible = false;
            }
            else if (cmbCOST.Text == "MOLAR_UPPER")
            {
                lblONE_CANAL.Visible = false;
                txtONE_CANAL.Visible = false;
                lblMB.Visible = true;
                txtMB.Visible = true;
                lblML.Visible = false;
                txtML.Visible = false;
                lblD.Visible = false;
                txtD.Visible = false;
                lblP.Visible = true;
                txtP.Visible = true;
                lblDB.Visible = true;
                txtDB.Visible = true;
                lblB.Visible = false;
                txtB.Visible = false;
                lblM.Visible = false;
                txtM.Visible = false;
            }
            else if (cmbCOST.Text == "MOLAR_LOWER")
            {
                lblONE_CANAL.Visible = false;
                txtONE_CANAL.Visible = false;
                lblMB.Visible = true;
                txtMB.Visible = true;
                lblML.Visible = true;
                txtML.Visible = true;
                lblD.Visible = true;
                txtD.Visible = true;
                lblP.Visible = false;
                txtP.Visible = false;
                lblDB.Visible = false;
                txtDB.Visible = false;
                lblB.Visible = false;
                txtB.Visible = false;
                lblM.Visible = false;
                txtM.Visible = false;
            }
            else if (cmbCOST.Text == "MOLAR_LOWER_2_CANAL")
            {
                lblONE_CANAL.Visible = false;
                txtONE_CANAL.Visible = false;
                lblMB.Visible = false;
                txtMB.Visible = false;
                lblML.Visible = false;
                txtML.Visible = false;
                lblD.Visible = true;
                txtD.Visible = true;
                lblP.Visible = false;
                txtP.Visible = false;
                lblDB.Visible = false;
                txtDB.Visible = false;
                lblB.Visible = false;
                txtB.Visible = false;
                lblM.Visible = true;
                txtM.Visible = true;
            }
           
        }
    }
}