﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="resource.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image1" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image2" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image3" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAAES9JREFUeF7d
        mwlYl1W+x88fBAF3QHFBBQPXxIXwZsW96ijXJ5ekxWn1NpWjTY6DNVZ2tXIEJRcEE1NRcUEdFBcUTdw1
        c8zJNcfKfc20TSvb6ze/7+H/g5fXH+g1fB6753k+D6/nPcv7+Z3lXUDzm0kZpoZ53TxiJpuZ5g2zy0wz
        nzE/MD8yn3PebjPFzLJlXje1vLX+H6SJpoWZYLKZbzkIxJJkpjMzmVlecIw8nEMZlE3jOhNNc28rv8H0
        mqlixpl0M5ZHOJ2lphqqMq8KdVvfjYbtG0bTj02nvDN5FhwjD+dQBmVtnXFc9zUOwwRT1dvqbySN5ZEb
        Z97nUSSe2hSYE0j9dvSj3DO5VHC+gAouFNDKCytLgTycQxmURR0bCLQxltuawDPpN5HGmQ48ap/ZqcxT
        O7ogmiYenUg5H+VQzrki5p6dQ3NOZzOzvGTbPDmPsqiDunZ5oC20ibZv6jTGtLQXOokveLahFoUtaOLx
        iZR5OpOmnMmkqWcn0xunMmjsgVGUvGs4Je/2wsfIwzmUQVnUQV20gbZsm2gbfdyU6QXe5VN5qmK0+ILr
        rapHI4+MpJTjKZR6MoXGn0qh9NPJNO7ICBq6bSC5E/JwDmVQFnVQF22gLRuEopnwgXneVPf2epOkPxlf
        k8RjhPXKU7bS0kr05HtP0tDDQ+mlY0Pp1eNDKeXkUHrt1FBK/nAQDSy836tdkpCHcyiDsqjz0tGhtg20
        hTbtckAfz/KNcjD3eVOkh43H3Gti7I7NG575u6GYrTHU//3+9Myh/vTskf407Fh/evVEfxZ7il4++Cj1
        W97Fq12SkIdzKIOyqIO6z3zY37YV81aMbdv2gTtLb9PG/IH7vlq6u+fdVe65554qiYmJpejTp0+VHr16
        VPEWu/7UyfiZp0yOvW3NMeTJ91Dvvb3p4X/1pSc/6EuDDvelvx7tS8OP96VRJx+g4Qd60QMLY73aJQl5
        OGfLcFnUQV20gbbQpmeFx/Zh+0Kfvbjvq6UhQ4ZQUlJSmXiLXV/qaHxMhAk34/nBBbesRYaC1wVT970J
        lHgggR49mEB//DCBko4k0LDjCfS3k93opf3x1GtWtFe7JCEP51AGZVEHddFG4nsJts3g9cFkcrkf9DXe
        fGdamQbGNLYXUZl/VjVRvDncUoR/c//qIe1Cqi9dupTKo05sneqBLQOL610zTZh6JsR0MX+xI5LNLOcA
        bAqmu3bFUfd9cXT/gTh6/IM4Hsk4euFYHI08GUfD9sZQQmY9r3ZJQh7OoQzKog7qog20hTbRNvqwfaHP
        eDPA8FNzNXMrr8G7zH3mTvOYucP0c3LixAkqD3f5a6aj+R9zG0/EIWa7fYSdzxTwElhrqM7aQGq/Lpw6
        b4ygnlsi6MFtEfTkjggavDOCBq5vQHek1vBqlyTk4RzKoCzqoC7aQFt1CgNt2+jD9oU+B5iFhsegCV/I
        czwS25kPmUNOvv32WyoPd/lrpjMTb46YFJ7+eI7H1FxtyHeloaAJhqq+aCj0xUoU/lJlajI8gJq9HEC3
        vhJArYdXpqgkP692SUIezqEMyqIO6qINtBU0vqht9IGlZvscbQ4Yvvu25RkwucakGqRx5syZchmx+1lK
        eW8YTXj/Vco8PIZmnkijnNOTafG5LMq/MJtWfzaf1n6RSxsuLqYNlxbTuou59CbnLTuXTTP2T6DAGQFF
        LzR5zJt8C1xsyD/JeLUqLqFNtG3WePtCn2+Yi4YXQAd+8s7iBwQ8JJBnHBdM8yG/ET7kecLQ9u3by8W/
        vy81zQilTisiKWF1FPUobEaJ61tS382t6dG32tIf/hFLA3Z2oGd2dbTg+AnOe3hTG+qZ25zMDO4XO/My
        hi/Oj2eC38CKDwDaRNum0NsX+pzBt0NvAGaUCsAEH+IdgUxXQwsWLCgX041H7Pe+9F/5kdR1VRTdvaYp
        3bOuBd2/8VZ6aEsb6retnRV+6p04C46R9/v1MdQ9pyl5sCHlMNic+OJsAJ6+AQHgNosDkO/tM9v8VDoA
        qYwEgOVNrKHBgweXCW6RvH9QpQQfumt5Y+qysgn99+po6smzoA/PAgThwS0x9MjWtjwb2llw/ODmGLq3
        sBV1nR1FvnO4r4XcFy6KL+6GLwEEYAWDPmebL0sCAHkwljeL8R7y9OWHhraG4uPjy6RLly5k2huq9Xgg
        dVzakP4zP4K6FNxig4ClgJmQuKGVDcQDm1pbcJy4vhX1XNWcOs2MpKDZfkVPaN4A2E2QN6yqL3C7vHmF
        DTMU/r+Gmoww1PxlQy1xrAQIeTiHMiiLOqiLNtBW8SYoAcBsmGgOlw7AGAbLgAv7jfch/xd8qfbTwRT2
        pxCqPyiUwv9chxr/JYwih9SlqOfqU/PnwynitZrUdmE9ilvcgIPQiOIRBJ4J3Xg5dH+zKfVY04x6rm1O
        vbzgGHkJK6LpzhmNKXgav7cjAFiXq7hv3gir8a2qLi+JpvwzlveFzusN9d5i6JFthh7i87eNvjIAyMM5
        lEFZ1EFdtIG20CbaRh+2L/SZxLtOcQAgz43IMvDlZVB5oi8FZfhRtdcrU63MQKo9tQrVm16NGs6sSZHZ
        wRQ9N4Ra5NSm1gvCqF1uPbqNg3D7koZ057LGdjZ05kD8jmcEgpHgBcfI67Qskv5jRjiFp1Uvui0tZTAy
        fIGBfOFhG/jiWSTuH4a6vmvo3n2GHv+XoX7bDd3Ob3XuhDycQxmURR3URRtoC21aefSBvtBnF7YuFYAU
        Bj95GXh4Gfjx3SAgvRJVmeRPNSYHUPCUIKoztSrVz6pOjTgITWaXBOFWDkKbv9el9ovqU1xeUSDu4BmB
        vSF+eYQNCMAx8jrmNaLYmfUpemQIeRbxcsOoYBnwBWKq1uTRasij2IoFOrJIwj9ZbA/zlqEO/BDjTsjD
        OZRBWdRBXbRRk+Xt9Ic8+kBfi8z3/AjQuyQAGP1kRmaBdy/wT/OlwHQ/quoNQgiCMK0oCDITouaEULN5
        odRyfh1qvbAoEJgRsQgGz4oOeeGlQF5sbn2KmVWXopNDKCwrpOguIPAFVuYLDeWHlkh+emu9iYVYritL
        ddtoqP20KwOAPJxDGZRFHdRFG2jLSkuQ0cdfzWZ+G+kgAciy4n9jZBbgruBdCv68FJxBkJmA5RA+owY1
        nlWreDYgEJgRrRYUBSNmYV0bECfIw7JpkV2bbhkbTA9Ovpc8K3kWYJRwcUt4BvKOHcRTtTbnRfJsaLWO
        pzQL3s6j2TrrygAgD+dQBmVRB3XRhgcPPgBto4+V5mcTaf7MAWheFIBofj+C+EhmFOMIgjwXXBmEQArl
        PSFsWrXi2SCBwIyQYDQHHBAEBeAYeTgXnR1CERm1qNWISKr6Om+GWKd4VveuUQ/v1EEciFAeuYZ84U15
        NFvyNI7ihyd3Qh7OoQzKog7qoo3iPQZto4/BZi3L/46pZ/g9MNaE8dhj+r/ClBEEOxPscuA9IcPfbow1
        vUsCm6MEAjMC+wOCEZFdiyI5IAiKE+ThXOOZtSg8swaFvVqVag4MIJ8lPAt49OyF8izATu1ZwFOY79k1
        WSKM8xqwVAMlAMhr4C2DsqjjwUsPdnu0hTbRdo45Z4L4RcyY1kw1hu/2lcwgNHLx4sViLl26VCZffvll
        mXz11Vdl8vXXX5cCeV988QUdOXKEVq9eTSOmj6DADTwTeNe2OzYuHA8s83gA5vJOzj+rMzVn8mszv8yE
        eMEx8nAOZVDWAA6ebQNtoc3V5mt+9R/Ozp0YHv2iDyJtGfuV8WrymrDglnXiFneC8xcuXKB9+/bRokWL
        aMjUIRS4hYOwlS8Y0xUbF0YRQvzY7OGXmEo82v5MZZYGOK7EP3HOvuujLAKHunjwQVtrzWV+6Utl115M
        FBPE2E9ixQHQpAVNWnALC5qwcPnyZQuO0cbZs2dpx44dlJOTQ89Pfp5CN4WS2cEXzru5fYXFGvbOBvuF
        V34lBnCMPJxDGZRFnc0M2lhmzvOmN4o972Mw9WsyxR9Fyw2AW9aJJi24hZ2IvDMI6Ov06dP2DRNBGJ02
        mp8VOpLnXd4XIAEZzAjcxrCjY3ODLMAMQR7OoYyIv8u7/TDzFo/1s+wIebiGMJWY4qQGQBMWNGHBLevE
        Le4Gyw9BeOedd+xymDRpEiWlJ1Hb5W3Jd6cvGb7HG364MfxwY0UxygDHyMM5lNnBr7kpZjdP9LHs9kcG
        0x4jD/krPoSWCoAmLGjCgiYsaLLCN998Uwz+jWs4d+4c7d+/n9asWUPZ2dmUlpZGw1OH033T7uP3jrYU
        lh9GAW8GkKfQQz6F/LTKx3VX4Cm0PZnu5p+muslkpyHMQww2vGgG077UyEsqDoAmLWjSQBMW3LJOnOJu
        UPfTTz+lY8eO0c6dO2nVqlU0d+5cyszMpPHjx9OYMWMoOTmZRo0aZUlJSaHU1FTKyMjAV+p5zFCmBxPD
        YLfHhlfmL0LKDYAmLbiFnWjSQBMW5DsjjlEWs+GTTz6h48eP27vE1q1bbTAWL15M8+fPp3nz5lnwYSYv
        L4/Wrl2LAGCn78PcwuA+jylf7i9A1ABowoImLLiFnbiFBedHVjc4jz4///xz+vjjj+nkyZN0+PBhOnjw
        IB04cMCCYzxLYOmwC6b+7UwNxoe5aioVALesE01Y0IQFt7ATTVr47rvvLPJvlEdfuBZcq3PDRt6PP/6I
        ADzNtGOu+bdWxQFwyjpxyzrRhAW3rBOnqBsR1/j+++/VPIAEF69TxQRAkxY0aaAJC5qw4BZzIpIaP/zw
        g6VCA6AJC5q0oEkLmjTQhAVNWBDxCg2AJixowoImLGjSgiYNNGHBLS786gBo0kATFjRhQRMWNGlBkxY0
        cYAN8IYEQJMWNGmgCQuasKAJC5q0APkKD4AmLGjSgiYNNGFBExY0YUHEKzQAmrCgCQuatKBJC5o00IQF
        t7jwqwOgSQNNWNCEBU1Y0KQFTVrQxMFPP/10YwKgSQNNWNCEBU1Y0IQFTVqAfIUHQJMWNGlBkwaasKAJ
        C5qwIOIVFgBN1okmLGjSgiYtaNJAExbc4sINC4AmLGjCgiYsaNKCJg00aeHnn3++MQHQpIEmLGjCgiYs
        aNKCJi1AvsIDoEkLmjTQhAVNWNCEBU1YEHFXAAYwbZjrC4AmLGjSgiYtaNJAExbcsshDHbSHa8FGjXcX
        +YCDZxiUYZcnmFYMPoNdUyoOgFtY0IQFt6wTt7ATp6wbTR6SR48epb1799pP5xs2bLAfTfEbJXwqwxeh
        rKysPR6PJ5F9Ihh/yF1LKjMAmrCgCQuasOCWdeIWF1APn8Hw/W/69On2A2lBQQHt2bPHfgbDd8P8/PxT
        AQEBWP+xDP7T1DX/NbgaAE0aaMKCW9aJJixo0gLWNvrdtWsXpaen2zykX375xV4nfq22efPmT0JDQ/HL
        D3wLrMNc/Y+gHalUANzCTjRpQZMGmrDglnXi3NywN23bto2mTJlSLI+88+fP45colxo1avQiO3RkwhhM
        /av/GbwjFQdAkwaasKBJC5q0oEkDp7iADQ5rPjc318pjA8QXYt4PvmnRosUrfP13MNclj1RmADRhQRMW
        NGFBkxY0eYAAFBYW0ttvv22Pse537959OS4ubjRfO+Tr8uZXmX/+n+WR1ABo0kATFjRhQRMWNGkBI47b
        HXZ7bIQfffQRAnGxWbNmL/N138nU9fHxuW55pFIB0KQFTVrQpIEmLGjCAsQFBGDjxo32j7PXrVt3ITw8
        HGv+Lqberxl5SXhqGoAAaNJAExY0aUGTBpqw4BQXsOEdOnSIlixZcio4OPg5vl7I168IeST8P7rHEICb
        NSFoeXl5HwQFBQ3ia41n6jMBzK+WRwpnOjOPMXiOxsPEzcZTzAMM7vMVKo+ElwYEATMBywF7ws0G/rgB
        v+0NZSpk2hclY/4NEXp5mvIfMe0AAAAASUVORK5CYII=
</value>
  </data>
  <data name="resource.Image4" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAAES9JREFUeF7d
        mwlYl1W+x88fBAF3QHFBBQPXxIXwZsW96ijXJ5ekxWn1NpWjTY6DNVZ2tXIEJRcEE1NRcUEdFBcUTdw1
        c8zJNcfKfc20TSvb6ze/7+H/g5fXH+g1fB6753k+D6/nPcv7+Z3lXUDzm0kZpoZ53TxiJpuZ5g2zy0wz
        nzE/MD8yn3PebjPFzLJlXje1vLX+H6SJpoWZYLKZbzkIxJJkpjMzmVlecIw8nEMZlE3jOhNNc28rv8H0
        mqlixpl0M5ZHOJ2lphqqMq8KdVvfjYbtG0bTj02nvDN5FhwjD+dQBmVtnXFc9zUOwwRT1dvqbySN5ZEb
        Z97nUSSe2hSYE0j9dvSj3DO5VHC+gAouFNDKCytLgTycQxmURR0bCLQxltuawDPpN5HGmQ48ap/ZqcxT
        O7ogmiYenUg5H+VQzrki5p6dQ3NOZzOzvGTbPDmPsqiDunZ5oC20ibZv6jTGtLQXOokveLahFoUtaOLx
        iZR5OpOmnMmkqWcn0xunMmjsgVGUvGs4Je/2wsfIwzmUQVnUQV20gbZsm2gbfdyU6QXe5VN5qmK0+ILr
        rapHI4+MpJTjKZR6MoXGn0qh9NPJNO7ICBq6bSC5E/JwDmVQFnVQF22gLRuEopnwgXneVPf2epOkPxlf
        k8RjhPXKU7bS0kr05HtP0tDDQ+mlY0Pp1eNDKeXkUHrt1FBK/nAQDSy836tdkpCHcyiDsqjz0tGhtg20
        hTbtckAfz/KNcjD3eVOkh43H3Gti7I7NG575u6GYrTHU//3+9Myh/vTskf407Fh/evVEfxZ7il4++Cj1
        W97Fq12SkIdzKIOyqIO6z3zY37YV81aMbdv2gTtLb9PG/IH7vlq6u+fdVe65554qiYmJpejTp0+VHr16
        VPEWu/7UyfiZp0yOvW3NMeTJ91Dvvb3p4X/1pSc/6EuDDvelvx7tS8OP96VRJx+g4Qd60QMLY73aJQl5
        OGfLcFnUQV20gbbQpmeFx/Zh+0Kfvbjvq6UhQ4ZQUlJSmXiLXV/qaHxMhAk34/nBBbesRYaC1wVT970J
        lHgggR49mEB//DCBko4k0LDjCfS3k93opf3x1GtWtFe7JCEP51AGZVEHddFG4nsJts3g9cFkcrkf9DXe
        fGdamQbGNLYXUZl/VjVRvDncUoR/c//qIe1Cqi9dupTKo05sneqBLQOL610zTZh6JsR0MX+xI5LNLOcA
        bAqmu3bFUfd9cXT/gTh6/IM4Hsk4euFYHI08GUfD9sZQQmY9r3ZJQh7OoQzKog7qog20hTbRNvqwfaHP
        eDPA8FNzNXMrr8G7zH3mTvOYucP0c3LixAkqD3f5a6aj+R9zG0/EIWa7fYSdzxTwElhrqM7aQGq/Lpw6
        b4ygnlsi6MFtEfTkjggavDOCBq5vQHek1vBqlyTk4RzKoCzqoC7aQFt1CgNt2+jD9oU+B5iFhsegCV/I
        czwS25kPmUNOvv32WyoPd/lrpjMTb46YFJ7+eI7H1FxtyHeloaAJhqq+aCj0xUoU/lJlajI8gJq9HEC3
        vhJArYdXpqgkP692SUIezqEMyqIO6qINtBU0vqht9IGlZvscbQ4Yvvu25RkwucakGqRx5syZchmx+1lK
        eW8YTXj/Vco8PIZmnkijnNOTafG5LMq/MJtWfzaf1n6RSxsuLqYNlxbTuou59CbnLTuXTTP2T6DAGQFF
        LzR5zJt8C1xsyD/JeLUqLqFNtG3WePtCn2+Yi4YXQAd+8s7iBwQ8JJBnHBdM8yG/ET7kecLQ9u3by8W/
        vy81zQilTisiKWF1FPUobEaJ61tS382t6dG32tIf/hFLA3Z2oGd2dbTg+AnOe3hTG+qZ25zMDO4XO/My
        hi/Oj2eC38CKDwDaRNum0NsX+pzBt0NvAGaUCsAEH+IdgUxXQwsWLCgX041H7Pe+9F/5kdR1VRTdvaYp
        3bOuBd2/8VZ6aEsb6retnRV+6p04C46R9/v1MdQ9pyl5sCHlMNic+OJsAJ6+AQHgNosDkO/tM9v8VDoA
        qYwEgOVNrKHBgweXCW6RvH9QpQQfumt5Y+qysgn99+po6smzoA/PAgThwS0x9MjWtjwb2llw/ODmGLq3
        sBV1nR1FvnO4r4XcFy6KL+6GLwEEYAWDPmebL0sCAHkwljeL8R7y9OWHhraG4uPjy6RLly5k2huq9Xgg
        dVzakP4zP4K6FNxig4ClgJmQuKGVDcQDm1pbcJy4vhX1XNWcOs2MpKDZfkVPaN4A2E2QN6yqL3C7vHmF
        DTMU/r+Gmoww1PxlQy1xrAQIeTiHMiiLOqiLNtBW8SYoAcBsmGgOlw7AGAbLgAv7jfch/xd8qfbTwRT2
        pxCqPyiUwv9chxr/JYwih9SlqOfqU/PnwynitZrUdmE9ilvcgIPQiOIRBJ4J3Xg5dH+zKfVY04x6rm1O
        vbzgGHkJK6LpzhmNKXgav7cjAFiXq7hv3gir8a2qLi+JpvwzlveFzusN9d5i6JFthh7i87eNvjIAyMM5
        lEFZ1EFdtIG20CbaRh+2L/SZxLtOcQAgz43IMvDlZVB5oi8FZfhRtdcrU63MQKo9tQrVm16NGs6sSZHZ
        wRQ9N4Ra5NSm1gvCqF1uPbqNg3D7koZ057LGdjZ05kD8jmcEgpHgBcfI67Qskv5jRjiFp1Uvui0tZTAy
        fIGBfOFhG/jiWSTuH4a6vmvo3n2GHv+XoX7bDd3Ob3XuhDycQxmURR3URRtoC21aefSBvtBnF7YuFYAU
        Bj95GXh4Gfjx3SAgvRJVmeRPNSYHUPCUIKoztSrVz6pOjTgITWaXBOFWDkKbv9el9ovqU1xeUSDu4BmB
        vSF+eYQNCMAx8jrmNaLYmfUpemQIeRbxcsOoYBnwBWKq1uTRasij2IoFOrJIwj9ZbA/zlqEO/BDjTsjD
        OZRBWdRBXbRRk+Xt9Ic8+kBfi8z3/AjQuyQAGP1kRmaBdy/wT/OlwHQ/quoNQgiCMK0oCDITouaEULN5
        odRyfh1qvbAoEJgRsQgGz4oOeeGlQF5sbn2KmVWXopNDKCwrpOguIPAFVuYLDeWHlkh+emu9iYVYritL
        ddtoqP20KwOAPJxDGZRFHdRFG2jLSkuQ0cdfzWZ+G+kgAciy4n9jZBbgruBdCv68FJxBkJmA5RA+owY1
        nlWreDYgEJgRrRYUBSNmYV0bECfIw7JpkV2bbhkbTA9Ovpc8K3kWYJRwcUt4BvKOHcRTtTbnRfJsaLWO
        pzQL3s6j2TrrygAgD+dQBmVRB3XRhgcPPgBto4+V5mcTaf7MAWheFIBofj+C+EhmFOMIgjwXXBmEQArl
        PSFsWrXi2SCBwIyQYDQHHBAEBeAYeTgXnR1CERm1qNWISKr6Om+GWKd4VveuUQ/v1EEciFAeuYZ84U15
        NFvyNI7ihyd3Qh7OoQzKog7qoo3iPQZto4/BZi3L/46pZ/g9MNaE8dhj+r/ClBEEOxPscuA9IcPfbow1
        vUsCm6MEAjMC+wOCEZFdiyI5IAiKE+ThXOOZtSg8swaFvVqVag4MIJ8lPAt49OyF8izATu1ZwFOY79k1
        WSKM8xqwVAMlAMhr4C2DsqjjwUsPdnu0hTbRdo45Z4L4RcyY1kw1hu/2lcwgNHLx4sViLl26VCZffvll
        mXz11Vdl8vXXX5cCeV988QUdOXKEVq9eTSOmj6DADTwTeNe2OzYuHA8s83gA5vJOzj+rMzVn8mszv8yE
        eMEx8nAOZVDWAA6ebQNtoc3V5mt+9R/Ozp0YHv2iDyJtGfuV8WrymrDglnXiFneC8xcuXKB9+/bRokWL
        aMjUIRS4hYOwlS8Y0xUbF0YRQvzY7OGXmEo82v5MZZYGOK7EP3HOvuujLAKHunjwQVtrzWV+6Utl115M
        FBPE2E9ixQHQpAVNWnALC5qwcPnyZQuO0cbZs2dpx44dlJOTQ89Pfp5CN4WS2cEXzru5fYXFGvbOBvuF
        V34lBnCMPJxDGZRFnc0M2lhmzvOmN4o972Mw9WsyxR9Fyw2AW9aJJi24hZ2IvDMI6Ov06dP2DRNBGJ02
        mp8VOpLnXd4XIAEZzAjcxrCjY3ODLMAMQR7OoYyIv8u7/TDzFo/1s+wIebiGMJWY4qQGQBMWNGHBLevE
        Le4Gyw9BeOedd+xymDRpEiWlJ1Hb5W3Jd6cvGb7HG364MfxwY0UxygDHyMM5lNnBr7kpZjdP9LHs9kcG
        0x4jD/krPoSWCoAmLGjCgiYsaLLCN998Uwz+jWs4d+4c7d+/n9asWUPZ2dmUlpZGw1OH033T7uP3jrYU
        lh9GAW8GkKfQQz6F/LTKx3VX4Cm0PZnu5p+muslkpyHMQww2vGgG077UyEsqDoAmLWjSQBMW3LJOnOJu
        UPfTTz+lY8eO0c6dO2nVqlU0d+5cyszMpPHjx9OYMWMoOTmZRo0aZUlJSaHU1FTKyMjAV+p5zFCmBxPD
        YLfHhlfmL0LKDYAmLbiFnWjSQBMW5DsjjlEWs+GTTz6h48eP27vE1q1bbTAWL15M8+fPp3nz5lnwYSYv
        L4/Wrl2LAGCn78PcwuA+jylf7i9A1ABowoImLLiFnbiFBedHVjc4jz4///xz+vjjj+nkyZN0+PBhOnjw
        IB04cMCCYzxLYOmwC6b+7UwNxoe5aioVALesE01Y0IQFt7ATTVr47rvvLPJvlEdfuBZcq3PDRt6PP/6I
        ADzNtGOu+bdWxQFwyjpxyzrRhAW3rBOnqBsR1/j+++/VPIAEF69TxQRAkxY0aaAJC5qw4BZzIpIaP/zw
        g6VCA6AJC5q0oEkLmjTQhAVNWBDxCg2AJixowoImLGjSgiYNNGHBLS786gBo0kATFjRhQRMWNGlBkxY0
        cYAN8IYEQJMWNGmgCQuasKAJC5q0APkKD4AmLGjSgiYNNGFBExY0YUHEKzQAmrCgCQuatKBJC5o00IQF
        t7jwqwOgSQNNWNCEBU1Y0KQFTVrQxMFPP/10YwKgSQNNWNCEBU1Y0IQFTVqAfIUHQJMWNGlBkwaasKAJ
        C5qwIOIVFgBN1okmLGjSgiYtaNJAExbc4sINC4AmLGjCgiYsaNKCJg00aeHnn3++MQHQpIEmLGjCgiYs
        aNKCJi1AvsIDoEkLmjTQhAVNWNCEBU1YEHFXAAYwbZjrC4AmLGjSgiYtaNJAExbcsshDHbSHa8FGjXcX
        +YCDZxiUYZcnmFYMPoNdUyoOgFtY0IQFt6wTt7ATp6wbTR6SR48epb1799pP5xs2bLAfTfEbJXwqwxeh
        rKysPR6PJ5F9Ihh/yF1LKjMAmrCgCQuasOCWdeIWF1APn8Hw/W/69On2A2lBQQHt2bPHfgbDd8P8/PxT
        AQEBWP+xDP7T1DX/NbgaAE0aaMKCW9aJJixo0gLWNvrdtWsXpaen2zykX375xV4nfq22efPmT0JDQ/HL
        D3wLrMNc/Y+gHalUANzCTjRpQZMGmrDglnXi3NywN23bto2mTJlSLI+88+fP45colxo1avQiO3RkwhhM
        /av/GbwjFQdAkwaasKBJC5q0oEkDp7iADQ5rPjc318pjA8QXYt4PvmnRosUrfP13MNclj1RmADRhQRMW
        NGFBkxY0eYAAFBYW0ttvv22Pse537959OS4ubjRfO+Tr8uZXmX/+n+WR1ABo0kATFjRhQRMWNGkBI47b
        HXZ7bIQfffQRAnGxWbNmL/N138nU9fHxuW55pFIB0KQFTVrQpIEmLGjCAsQFBGDjxo32j7PXrVt3ITw8
        HGv+Lqberxl5SXhqGoAAaNJAExY0aUGTBpqw4BQXsOEdOnSIlixZcio4OPg5vl7I168IeST8P7rHEICb
        NSFoeXl5HwQFBQ3ia41n6jMBzK+WRwpnOjOPMXiOxsPEzcZTzAMM7vMVKo+ElwYEATMBywF7ws0G/rgB
        v+0NZSpk2hclY/4NEXp5mvIfMe0AAAAASUVORK5CYII=
</value>
  </data>
  <data name="resource.Image5" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAAES9JREFUeF7d
        mwlYl1W+x88fBAF3QHFBBQPXxIXwZsW96ijXJ5ekxWn1NpWjTY6DNVZ2tXIEJRcEE1NRcUEdFBcUTdw1
        c8zJNcfKfc20TSvb6ze/7+H/g5fXH+g1fB6753k+D6/nPcv7+Z3lXUDzm0kZpoZ53TxiJpuZ5g2zy0wz
        nzE/MD8yn3PebjPFzLJlXje1vLX+H6SJpoWZYLKZbzkIxJJkpjMzmVlecIw8nEMZlE3jOhNNc28rv8H0
        mqlixpl0M5ZHOJ2lphqqMq8KdVvfjYbtG0bTj02nvDN5FhwjD+dQBmVtnXFc9zUOwwRT1dvqbySN5ZEb
        Z97nUSSe2hSYE0j9dvSj3DO5VHC+gAouFNDKCytLgTycQxmURR0bCLQxltuawDPpN5HGmQ48ap/ZqcxT
        O7ogmiYenUg5H+VQzrki5p6dQ3NOZzOzvGTbPDmPsqiDunZ5oC20ibZv6jTGtLQXOokveLahFoUtaOLx
        iZR5OpOmnMmkqWcn0xunMmjsgVGUvGs4Je/2wsfIwzmUQVnUQV20gbZsm2gbfdyU6QXe5VN5qmK0+ILr
        rapHI4+MpJTjKZR6MoXGn0qh9NPJNO7ICBq6bSC5E/JwDmVQFnVQF22gLRuEopnwgXneVPf2epOkPxlf
        k8RjhPXKU7bS0kr05HtP0tDDQ+mlY0Pp1eNDKeXkUHrt1FBK/nAQDSy836tdkpCHcyiDsqjz0tGhtg20
        hTbtckAfz/KNcjD3eVOkh43H3Gti7I7NG575u6GYrTHU//3+9Myh/vTskf407Fh/evVEfxZ7il4++Cj1
        W97Fq12SkIdzKIOyqIO6z3zY37YV81aMbdv2gTtLb9PG/IH7vlq6u+fdVe65554qiYmJpejTp0+VHr16
        VPEWu/7UyfiZp0yOvW3NMeTJ91Dvvb3p4X/1pSc/6EuDDvelvx7tS8OP96VRJx+g4Qd60QMLY73aJQl5
        OGfLcFnUQV20gbbQpmeFx/Zh+0Kfvbjvq6UhQ4ZQUlJSmXiLXV/qaHxMhAk34/nBBbesRYaC1wVT970J
        lHgggR49mEB//DCBko4k0LDjCfS3k93opf3x1GtWtFe7JCEP51AGZVEHddFG4nsJts3g9cFkcrkf9DXe
        fGdamQbGNLYXUZl/VjVRvDncUoR/c//qIe1Cqi9dupTKo05sneqBLQOL610zTZh6JsR0MX+xI5LNLOcA
        bAqmu3bFUfd9cXT/gTh6/IM4Hsk4euFYHI08GUfD9sZQQmY9r3ZJQh7OoQzKog7qog20hTbRNvqwfaHP
        eDPA8FNzNXMrr8G7zH3mTvOYucP0c3LixAkqD3f5a6aj+R9zG0/EIWa7fYSdzxTwElhrqM7aQGq/Lpw6
        b4ygnlsi6MFtEfTkjggavDOCBq5vQHek1vBqlyTk4RzKoCzqoC7aQFt1CgNt2+jD9oU+B5iFhsegCV/I
        czwS25kPmUNOvv32WyoPd/lrpjMTb46YFJ7+eI7H1FxtyHeloaAJhqq+aCj0xUoU/lJlajI8gJq9HEC3
        vhJArYdXpqgkP692SUIezqEMyqIO6qINtBU0vqht9IGlZvscbQ4Yvvu25RkwucakGqRx5syZchmx+1lK
        eW8YTXj/Vco8PIZmnkijnNOTafG5LMq/MJtWfzaf1n6RSxsuLqYNlxbTuou59CbnLTuXTTP2T6DAGQFF
        LzR5zJt8C1xsyD/JeLUqLqFNtG3WePtCn2+Yi4YXQAd+8s7iBwQ8JJBnHBdM8yG/ET7kecLQ9u3by8W/
        vy81zQilTisiKWF1FPUobEaJ61tS382t6dG32tIf/hFLA3Z2oGd2dbTg+AnOe3hTG+qZ25zMDO4XO/My
        hi/Oj2eC38CKDwDaRNum0NsX+pzBt0NvAGaUCsAEH+IdgUxXQwsWLCgX041H7Pe+9F/5kdR1VRTdvaYp
        3bOuBd2/8VZ6aEsb6retnRV+6p04C46R9/v1MdQ9pyl5sCHlMNic+OJsAJ6+AQHgNosDkO/tM9v8VDoA
        qYwEgOVNrKHBgweXCW6RvH9QpQQfumt5Y+qysgn99+po6smzoA/PAgThwS0x9MjWtjwb2llw/ODmGLq3
        sBV1nR1FvnO4r4XcFy6KL+6GLwEEYAWDPmebL0sCAHkwljeL8R7y9OWHhraG4uPjy6RLly5k2huq9Xgg
        dVzakP4zP4K6FNxig4ClgJmQuKGVDcQDm1pbcJy4vhX1XNWcOs2MpKDZfkVPaN4A2E2QN6yqL3C7vHmF
        DTMU/r+Gmoww1PxlQy1xrAQIeTiHMiiLOqiLNtBW8SYoAcBsmGgOlw7AGAbLgAv7jfch/xd8qfbTwRT2
        pxCqPyiUwv9chxr/JYwih9SlqOfqU/PnwynitZrUdmE9ilvcgIPQiOIRBJ4J3Xg5dH+zKfVY04x6rm1O
        vbzgGHkJK6LpzhmNKXgav7cjAFiXq7hv3gir8a2qLi+JpvwzlveFzusN9d5i6JFthh7i87eNvjIAyMM5
        lEFZ1EFdtIG20CbaRh+2L/SZxLtOcQAgz43IMvDlZVB5oi8FZfhRtdcrU63MQKo9tQrVm16NGs6sSZHZ
        wRQ9N4Ra5NSm1gvCqF1uPbqNg3D7koZ057LGdjZ05kD8jmcEgpHgBcfI67Qskv5jRjiFp1Uvui0tZTAy
        fIGBfOFhG/jiWSTuH4a6vmvo3n2GHv+XoX7bDd3Ob3XuhDycQxmURR3URRtoC21aefSBvtBnF7YuFYAU
        Bj95GXh4Gfjx3SAgvRJVmeRPNSYHUPCUIKoztSrVz6pOjTgITWaXBOFWDkKbv9el9ovqU1xeUSDu4BmB
        vSF+eYQNCMAx8jrmNaLYmfUpemQIeRbxcsOoYBnwBWKq1uTRasij2IoFOrJIwj9ZbA/zlqEO/BDjTsjD
        OZRBWdRBXbRRk+Xt9Ic8+kBfi8z3/AjQuyQAGP1kRmaBdy/wT/OlwHQ/quoNQgiCMK0oCDITouaEULN5
        odRyfh1qvbAoEJgRsQgGz4oOeeGlQF5sbn2KmVWXopNDKCwrpOguIPAFVuYLDeWHlkh+emu9iYVYritL
        ddtoqP20KwOAPJxDGZRFHdRFG2jLSkuQ0cdfzWZ+G+kgAciy4n9jZBbgruBdCv68FJxBkJmA5RA+owY1
        nlWreDYgEJgRrRYUBSNmYV0bECfIw7JpkV2bbhkbTA9Ovpc8K3kWYJRwcUt4BvKOHcRTtTbnRfJsaLWO
        pzQL3s6j2TrrygAgD+dQBmVRB3XRhgcPPgBto4+V5mcTaf7MAWheFIBofj+C+EhmFOMIgjwXXBmEQArl
        PSFsWrXi2SCBwIyQYDQHHBAEBeAYeTgXnR1CERm1qNWISKr6Om+GWKd4VveuUQ/v1EEciFAeuYZ84U15
        NFvyNI7ihyd3Qh7OoQzKog7qoo3iPQZto4/BZi3L/46pZ/g9MNaE8dhj+r/ClBEEOxPscuA9IcPfbow1
        vUsCm6MEAjMC+wOCEZFdiyI5IAiKE+ThXOOZtSg8swaFvVqVag4MIJ8lPAt49OyF8izATu1ZwFOY79k1
        WSKM8xqwVAMlAMhr4C2DsqjjwUsPdnu0hTbRdo45Z4L4RcyY1kw1hu/2lcwgNHLx4sViLl26VCZffvll
        mXz11Vdl8vXXX5cCeV988QUdOXKEVq9eTSOmj6DADTwTeNe2OzYuHA8s83gA5vJOzj+rMzVn8mszv8yE
        eMEx8nAOZVDWAA6ebQNtoc3V5mt+9R/Ozp0YHv2iDyJtGfuV8WrymrDglnXiFneC8xcuXKB9+/bRokWL
        aMjUIRS4hYOwlS8Y0xUbF0YRQvzY7OGXmEo82v5MZZYGOK7EP3HOvuujLAKHunjwQVtrzWV+6Utl115M
        FBPE2E9ixQHQpAVNWnALC5qwcPnyZQuO0cbZs2dpx44dlJOTQ89Pfp5CN4WS2cEXzru5fYXFGvbOBvuF
        V34lBnCMPJxDGZRFnc0M2lhmzvOmN4o972Mw9WsyxR9Fyw2AW9aJJi24hZ2IvDMI6Ov06dP2DRNBGJ02
        mp8VOpLnXd4XIAEZzAjcxrCjY3ODLMAMQR7OoYyIv8u7/TDzFo/1s+wIebiGMJWY4qQGQBMWNGHBLevE
        Le4Gyw9BeOedd+xymDRpEiWlJ1Hb5W3Jd6cvGb7HG364MfxwY0UxygDHyMM5lNnBr7kpZjdP9LHs9kcG
        0x4jD/krPoSWCoAmLGjCgiYsaLLCN998Uwz+jWs4d+4c7d+/n9asWUPZ2dmUlpZGw1OH033T7uP3jrYU
        lh9GAW8GkKfQQz6F/LTKx3VX4Cm0PZnu5p+muslkpyHMQww2vGgG077UyEsqDoAmLWjSQBMW3LJOnOJu
        UPfTTz+lY8eO0c6dO2nVqlU0d+5cyszMpPHjx9OYMWMoOTmZRo0aZUlJSaHU1FTKyMjAV+p5zFCmBxPD
        YLfHhlfmL0LKDYAmLbiFnWjSQBMW5DsjjlEWs+GTTz6h48eP27vE1q1bbTAWL15M8+fPp3nz5lnwYSYv
        L4/Wrl2LAGCn78PcwuA+jylf7i9A1ABowoImLLiFnbiFBedHVjc4jz4///xz+vjjj+nkyZN0+PBhOnjw
        IB04cMCCYzxLYOmwC6b+7UwNxoe5aioVALesE01Y0IQFt7ATTVr47rvvLPJvlEdfuBZcq3PDRt6PP/6I
        ADzNtGOu+bdWxQFwyjpxyzrRhAW3rBOnqBsR1/j+++/VPIAEF69TxQRAkxY0aaAJC5qw4BZzIpIaP/zw
        g6VCA6AJC5q0oEkLmjTQhAVNWBDxCg2AJixowoImLGjSgiYNNGHBLS786gBo0kATFjRhQRMWNGlBkxY0
        cYAN8IYEQJMWNGmgCQuasKAJC5q0APkKD4AmLGjSgiYNNGFBExY0YUHEKzQAmrCgCQuatKBJC5o00IQF
        t7jwqwOgSQNNWNCEBU1Y0KQFTVrQxMFPP/10YwKgSQNNWNCEBU1Y0IQFTVqAfIUHQJMWNGlBkwaasKAJ
        C5qwIOIVFgBN1okmLGjSgiYtaNJAExbc4sINC4AmLGjCgiYsaNKCJg00aeHnn3++MQHQpIEmLGjCgiYs
        aNKCJi1AvsIDoEkLmjTQhAVNWNCEBU1YEHFXAAYwbZjrC4AmLGjSgiYtaNJAExbcsshDHbSHa8FGjXcX
        +YCDZxiUYZcnmFYMPoNdUyoOgFtY0IQFt6wTt7ATp6wbTR6SR48epb1799pP5xs2bLAfTfEbJXwqwxeh
        rKysPR6PJ5F9Ihh/yF1LKjMAmrCgCQuasOCWdeIWF1APn8Hw/W/69On2A2lBQQHt2bPHfgbDd8P8/PxT
        AQEBWP+xDP7T1DX/NbgaAE0aaMKCW9aJJixo0gLWNvrdtWsXpaen2zykX375xV4nfq22efPmT0JDQ/HL
        D3wLrMNc/Y+gHalUANzCTjRpQZMGmrDglnXi3NywN23bto2mTJlSLI+88+fP45colxo1avQiO3RkwhhM
        /av/GbwjFQdAkwaasKBJC5q0oEkDp7iADQ5rPjc318pjA8QXYt4PvmnRosUrfP13MNclj1RmADRhQRMW
        NGFBkxY0eYAAFBYW0ttvv22Pse537959OS4ubjRfO+Tr8uZXmX/+n+WR1ABo0kATFjRhQRMWNGkBI47b
        HXZ7bIQfffQRAnGxWbNmL/N138nU9fHxuW55pFIB0KQFTVrQpIEmLGjCAsQFBGDjxo32j7PXrVt3ITw8
        HGv+Lqberxl5SXhqGoAAaNJAExY0aUGTBpqw4BQXsOEdOnSIlixZcio4OPg5vl7I168IeST8P7rHEICb
        NSFoeXl5HwQFBQ3ia41n6jMBzK+WRwpnOjOPMXiOxsPEzcZTzAMM7vMVKo+ElwYEATMBywF7ws0G/rgB
        v+0NZSpk2hclY/4NEXp5mvIfMe0AAAAASUVORK5CYII=
</value>
  </data>
</root>