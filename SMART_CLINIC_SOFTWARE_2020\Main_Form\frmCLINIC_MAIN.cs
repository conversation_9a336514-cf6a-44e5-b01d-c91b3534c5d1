﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.Main_Form
{
    public partial class frmCLINIC_MAIN : DevExpress.XtraEditors.XtraForm
    {
        public frmCLINIC_MAIN()
        {
            InitializeComponent();
        }

        private void btnEDIT_CLINIC_ItemClick(object sender, TileItemEventArgs e)
        {
            frmMAIN.GETNEWUSER.OPEN_FORM(((DevExpress.XtraEditors.TileItem)sender).Tag.ToString());
            this.Close();
        }

        private void btnCLINIC_LIST_ItemClick(object sender, TileItemEventArgs e)
        {
            LIST_FORM.frmCLINIC_LIST frmCLI_LIST = new LIST_FORM.frmCLINIC_LIST();
            frmCLI_LIST.Show();
        }

        private void frmCLINIC_MAIN_Load(object sender, EventArgs e)
        {
            if (Classes.clsUSER_PER.PER_DT.Columns.Contains(tileLIST.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][tileLIST.Tag.ToString()]) == 0)
            {
                tileLIST.Visible = false;
            }
            else
            {
                tileLIST.Visible = true;
            }

            if (Classes.clsUSER_PER.PER_DT.Columns.Contains(tileDATA.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][tileDATA.Tag.ToString()]) == 0)
            {
                tileDATA.Visible = false;
            }
            else
            {
                tileDATA.Visible = true;
            }
        }

        private void tileItem1_ItemClick(object sender, TileItemEventArgs e)
        {

        }
    }
}