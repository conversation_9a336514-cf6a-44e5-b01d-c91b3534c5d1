﻿namespace SMART_CLINIC_SOFTWARE_2020.Main_Form
{
    partial class frmCLINIC_MAIN
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraEditors.TileItemElement tileItemElement1 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement2 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement3 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame1 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement4 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement5 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement6 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame2 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement7 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement8 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement9 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement10 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement11 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement12 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame3 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement13 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement14 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement15 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame4 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement16 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement17 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement18 = new DevExpress.XtraEditors.TileItemElement();
            this.tileControl1 = new DevExpress.XtraEditors.TileControl();
            this.tileLIST = new DevExpress.XtraEditors.TileGroup();
            this.btnCLINIC_LIST = new DevExpress.XtraEditors.TileItem();
            this.tileDATA = new DevExpress.XtraEditors.TileGroup();
            this.btnEDIT_CLINIC = new DevExpress.XtraEditors.TileItem();
            this.SuspendLayout();
            // 
            // tileControl1
            // 
            this.tileControl1.AllowItemHover = true;
            this.tileControl1.AllowSelectedItem = true;
            this.tileControl1.AppearanceItem.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 16F);
            this.tileControl1.AppearanceItem.Hovered.Options.UseFont = true;
            this.tileControl1.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.tileControl1.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileControl1.AppearanceItem.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 16F);
            this.tileControl1.AppearanceItem.Normal.Options.UseFont = true;
            this.tileControl1.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileControl1.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileControl1.AppearanceItem.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 16F);
            this.tileControl1.AppearanceItem.Pressed.Options.UseFont = true;
            this.tileControl1.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.tileControl1.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileControl1.AppearanceItem.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 16F);
            this.tileControl1.AppearanceItem.Selected.Options.UseFont = true;
            this.tileControl1.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.tileControl1.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileControl1.AppearanceText.Font = new System.Drawing.Font("Droid Arabic Kufi", 16F);
            this.tileControl1.AppearanceText.Options.UseFont = true;
            this.tileControl1.AppearanceText.Options.UseTextOptions = true;
            this.tileControl1.AppearanceText.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Style3D;
            this.tileControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tileControl1.Groups.Add(this.tileLIST);
            this.tileControl1.Groups.Add(this.tileDATA);
            this.tileControl1.IndentBetweenGroups = 10;
            this.tileControl1.Location = new System.Drawing.Point(0, 0);
            this.tileControl1.MaxId = 4;
            this.tileControl1.Name = "tileControl1";
            this.tileControl1.Size = new System.Drawing.Size(822, 380);
            this.tileControl1.TabIndex = 1;
            this.tileControl1.Text = "tileControl1";
            // 
            // tileLIST
            // 
            this.tileLIST.Items.Add(this.btnCLINIC_LIST);
            this.tileLIST.Name = "tileLIST";
            this.tileLIST.Tag = "قائمة_العيادات";
            // 
            // btnCLINIC_LIST
            // 
            this.btnCLINIC_LIST.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.btnCLINIC_LIST.AppearanceItem.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 16F);
            this.btnCLINIC_LIST.AppearanceItem.Normal.Options.UseBackColor = true;
            this.btnCLINIC_LIST.AppearanceItem.Normal.Options.UseFont = true;
            this.btnCLINIC_LIST.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.btnCLINIC_LIST.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Hovered.Options.UseFont = true;
            tileItemElement1.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Normal.Options.UseFont = true;
            tileItemElement1.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Pressed.Options.UseFont = true;
            tileItemElement1.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Selected.Options.UseFont = true;
            tileItemElement1.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.MaxWidth = 160;
            tileItemElement1.Text = "قائمة العيادات";
            tileItemElement1.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement1.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement2.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Hovered.Options.UseFont = true;
            tileItemElement2.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Normal.Options.UseFont = true;
            tileItemElement2.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Selected.Options.UseFont = true;
            tileItemElement2.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.MaxWidth = 160;
            tileItemElement2.Text = "";
            tileItemElement2.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement2.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement3.ImageOptions.Image = global::SMART_CLINIC_SOFTWARE_2020.Properties.Resources.Inventory_icon;
            tileItemElement3.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement3.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement3.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement3.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.btnCLINIC_LIST.Elements.Add(tileItemElement1);
            this.btnCLINIC_LIST.Elements.Add(tileItemElement2);
            this.btnCLINIC_LIST.Elements.Add(tileItemElement3);
            tileItemFrame1.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame1.Appearance.BackColor = System.Drawing.Color.Teal;
            tileItemFrame1.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame1.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame1.Appearance.Options.UseBackColor = true;
            tileItemFrame1.Appearance.Options.UseBorderColor = true;
            tileItemElement4.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Hovered.Options.UseFont = true;
            tileItemElement4.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Normal.Options.UseFont = true;
            tileItemElement4.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Pressed.Options.UseFont = true;
            tileItemElement4.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Selected.Options.UseFont = true;
            tileItemElement4.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.MaxWidth = 160;
            tileItemElement4.Text = "قائمة العيادات";
            tileItemElement4.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement4.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement5.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Hovered.Options.UseFont = true;
            tileItemElement5.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Normal.Options.UseFont = true;
            tileItemElement5.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Selected.Options.UseFont = true;
            tileItemElement5.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.MaxWidth = 160;
            tileItemElement5.Text = "";
            tileItemElement5.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement5.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement6.ImageOptions.Image = global::SMART_CLINIC_SOFTWARE_2020.Properties.Resources.Inventory_icon;
            tileItemElement6.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement6.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement6.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement6.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame1.Elements.Add(tileItemElement4);
            tileItemFrame1.Elements.Add(tileItemElement5);
            tileItemFrame1.Elements.Add(tileItemElement6);
            tileItemFrame2.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame2.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame2.Appearance.BackColor2 = System.Drawing.Color.Teal;
            tileItemFrame2.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame2.Appearance.Options.UseBackColor = true;
            tileItemFrame2.Appearance.Options.UseBorderColor = true;
            tileItemElement7.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Hovered.Options.UseFont = true;
            tileItemElement7.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Normal.Options.UseFont = true;
            tileItemElement7.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Pressed.Options.UseFont = true;
            tileItemElement7.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Selected.Options.UseFont = true;
            tileItemElement7.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.MaxWidth = 160;
            tileItemElement7.Text = "قائمة العيادات";
            tileItemElement7.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement7.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement8.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Hovered.Options.UseFont = true;
            tileItemElement8.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Normal.Options.UseFont = true;
            tileItemElement8.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Selected.Options.UseFont = true;
            tileItemElement8.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.MaxWidth = 160;
            tileItemElement8.Text = "";
            tileItemElement8.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement8.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement9.ImageOptions.Image = global::SMART_CLINIC_SOFTWARE_2020.Properties.Resources.Inventory_icon;
            tileItemElement9.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement9.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement9.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement9.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame2.Elements.Add(tileItemElement7);
            tileItemFrame2.Elements.Add(tileItemElement8);
            tileItemFrame2.Elements.Add(tileItemElement9);
            this.btnCLINIC_LIST.Frames.Add(tileItemFrame1);
            this.btnCLINIC_LIST.Frames.Add(tileItemFrame2);
            this.btnCLINIC_LIST.Id = 0;
            this.btnCLINIC_LIST.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnCLINIC_LIST.Name = "btnCLINIC_LIST";
            this.btnCLINIC_LIST.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnCLINIC_LIST_ItemClick);
            // 
            // tileDATA
            // 
            this.tileDATA.Items.Add(this.btnEDIT_CLINIC);
            this.tileDATA.Name = "tileDATA";
            this.tileDATA.Tag = "بيانات_العيادات";
            // 
            // btnEDIT_CLINIC
            // 
            this.btnEDIT_CLINIC.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.btnEDIT_CLINIC.AppearanceItem.Normal.Options.UseBackColor = true;
            this.btnEDIT_CLINIC.CurrentFrameIndex = 1;
            tileItemElement10.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Hovered.Options.UseFont = true;
            tileItemElement10.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement10.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Normal.Options.UseFont = true;
            tileItemElement10.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            tileItemElement10.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement10.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Pressed.Options.UseFont = true;
            tileItemElement10.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Selected.Options.UseFont = true;
            tileItemElement10.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement10.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.MaxWidth = 160;
            tileItemElement10.Text = "أضافة و تعديل";
            tileItemElement10.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement10.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement11.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement11.Appearance.Hovered.Options.UseFont = true;
            tileItemElement11.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement11.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement11.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement11.Appearance.Normal.Options.UseFont = true;
            tileItemElement11.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement11.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement11.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement11.Appearance.Pressed.Options.UseFont = true;
            tileItemElement11.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement11.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement11.Appearance.Selected.Options.UseFont = true;
            tileItemElement11.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement11.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement11.MaxWidth = 160;
            tileItemElement11.Text = "العيادات";
            tileItemElement11.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement11.TextLocation = new System.Drawing.Point(75, 37);
            tileItemElement12.ImageOptions.Image = global::SMART_CLINIC_SOFTWARE_2020.Properties.Resources.العيادات_ImageOptions_Image;
            tileItemElement12.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement12.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement12.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement12.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.btnEDIT_CLINIC.Elements.Add(tileItemElement10);
            this.btnEDIT_CLINIC.Elements.Add(tileItemElement11);
            this.btnEDIT_CLINIC.Elements.Add(tileItemElement12);
            tileItemFrame3.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame3.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(150)))), ((int)(((byte)(136)))), ((int)(((byte)(179)))));
            tileItemFrame3.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame3.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame3.Appearance.Options.UseBackColor = true;
            tileItemFrame3.Appearance.Options.UseBorderColor = true;
            tileItemElement13.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Hovered.Options.UseFont = true;
            tileItemElement13.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement13.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Normal.Options.UseFont = true;
            tileItemElement13.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            tileItemElement13.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement13.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Pressed.Options.UseFont = true;
            tileItemElement13.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Selected.Options.UseFont = true;
            tileItemElement13.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement13.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.MaxWidth = 160;
            tileItemElement13.Text = "أضافة و تعديل";
            tileItemElement13.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement13.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement14.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement14.Appearance.Hovered.Options.UseFont = true;
            tileItemElement14.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement14.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement14.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement14.Appearance.Normal.Options.UseFont = true;
            tileItemElement14.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement14.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement14.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement14.Appearance.Pressed.Options.UseFont = true;
            tileItemElement14.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement14.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement14.Appearance.Selected.Options.UseFont = true;
            tileItemElement14.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement14.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement14.MaxWidth = 160;
            tileItemElement14.Text = "العيادات";
            tileItemElement14.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement14.TextLocation = new System.Drawing.Point(75, 37);
            tileItemElement15.ImageOptions.Image = global::SMART_CLINIC_SOFTWARE_2020.Properties.Resources.العيادات_ImageOptions_Image;
            tileItemElement15.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement15.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement15.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement15.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame3.Elements.Add(tileItemElement13);
            tileItemFrame3.Elements.Add(tileItemElement14);
            tileItemFrame3.Elements.Add(tileItemElement15);
            tileItemFrame4.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame4.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame4.Appearance.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(150)))), ((int)(((byte)(136)))), ((int)(((byte)(179)))));
            tileItemFrame4.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame4.Appearance.Options.UseBackColor = true;
            tileItemFrame4.Appearance.Options.UseBorderColor = true;
            tileItemElement16.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Hovered.Options.UseFont = true;
            tileItemElement16.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement16.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Normal.Options.UseFont = true;
            tileItemElement16.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            tileItemElement16.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement16.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Pressed.Options.UseFont = true;
            tileItemElement16.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Selected.Options.UseFont = true;
            tileItemElement16.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement16.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.MaxWidth = 160;
            tileItemElement16.Text = "أضافة و تعديل";
            tileItemElement16.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement16.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement17.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement17.Appearance.Hovered.Options.UseFont = true;
            tileItemElement17.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement17.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement17.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement17.Appearance.Normal.Options.UseFont = true;
            tileItemElement17.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement17.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement17.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement17.Appearance.Pressed.Options.UseFont = true;
            tileItemElement17.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement17.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement17.Appearance.Selected.Options.UseFont = true;
            tileItemElement17.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement17.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement17.MaxWidth = 160;
            tileItemElement17.Text = "العيادات";
            tileItemElement17.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement17.TextLocation = new System.Drawing.Point(75, 37);
            tileItemElement18.ImageOptions.Image = global::SMART_CLINIC_SOFTWARE_2020.Properties.Resources.العيادات_ImageOptions_Image;
            tileItemElement18.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement18.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement18.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement18.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame4.Elements.Add(tileItemElement16);
            tileItemFrame4.Elements.Add(tileItemElement17);
            tileItemFrame4.Elements.Add(tileItemElement18);
            this.btnEDIT_CLINIC.Frames.Add(tileItemFrame3);
            this.btnEDIT_CLINIC.Frames.Add(tileItemFrame4);
            this.btnEDIT_CLINIC.Id = 2;
            this.btnEDIT_CLINIC.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnEDIT_CLINIC.Name = "btnEDIT_CLINIC";
            this.btnEDIT_CLINIC.Tag = "frmCLINIC";
            this.btnEDIT_CLINIC.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnEDIT_CLINIC_ItemClick);
            // 
            // frmCLINIC_MAIN
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(822, 380);
            this.Controls.Add(this.tileControl1);
            this.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.MaximizeBox = false;
            this.Name = "frmCLINIC_MAIN";
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "frmCLINIC_MAIN";
            this.Load += new System.EventHandler(this.frmCLINIC_MAIN_Load);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.TileControl tileControl1;
        private DevExpress.XtraEditors.TileGroup tileLIST;
        private DevExpress.XtraEditors.TileItem btnCLINIC_LIST;
        private DevExpress.XtraEditors.TileGroup tileDATA;
        private DevExpress.XtraEditors.TileItem btnEDIT_CLINIC;
    }
}