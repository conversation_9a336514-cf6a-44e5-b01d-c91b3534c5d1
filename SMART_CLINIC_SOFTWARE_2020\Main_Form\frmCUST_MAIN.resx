﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="resource.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image1" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image2" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image3" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABzhJREFUWEet
        VwlMlOkZtqykxWpBU4+13ZhNW2qbzaoQJVsVShtNRLHrsYgSYF0W3K2ICOEsaMGDsgbremBXBIQp14BQ
        zrk6wBwwYYZxmCGcw1FugRSZBTwQefp+Pz/tNutqt86TPPn+//vnfZ/3+v7Mv+RVcHFxedfT0/Ocj49P
        mb+/v87b27th7969wm3btn2ycePG5fzPrA9HR8d3duzYoSBRhISEIDIyEtHR0QgNDUVAQAC8vLywc+fO
        f27YsGEPb2I9kFNvNze32YMHD8LX1xeBgYEIDg7mGBQUBD8/Pxw4cAC7du2Cs7Pz3Lp1697lTV8fDg4O
        blu3boWrqyt2796NPR4eoJLDg+i5fz8+oMwPHz4MD9qnILFlyxasX78+jzd/faxbZf/GoR2br0cGHEHs
        p/64GHkSaZfiUJV+GXW5V5F9/gz27XaHu7s7nJycsHr1asZJ3tw6GClJuQizAujWAP16YMAA9NJ1Zy1g
        LMW4NBVv/ehNULWwbNky2NvbY+nSpW/x5q8Pi/z239BRQwHUk3Aj0EdB9FAAbTJAXwjocuC3Zzu+Z2cH
        Gxsb2NrasvU93vz1YZGn6dEuB8zKhSowtlcDhhKgsQBoyMapQ+6gn36V7kTrYFqR2Ys2yYIoC8RUAdy/
        R9kLAa0A0GTiyK+dsMflF3h7rf1iAC6csTVgKb/8ALpcEsuhcuf9hw0kXp8BKG8hI+IoIEuBNOX0YgAO
        nLE1MFmUMATlF0BdOpctNHdJmFb1HUCRinn553guTsZc+XkUJwYycfmCpZUwkR9nnJdfBWpuEFM5UdTe
        xHz1NTyXXMGcKBnPyhPxrCwBm95e+4hMfrlgaSWMZYUJ56qS8Ez8GQkypuC56PKCcOVFzJLwbHE8Ery2
        j69ZbuvKm1kPAzcDT86WnMMTEuIypVI/KUvEbGkCnhSfxWNhDKYLYqGI9jjEm1gXXZd9f2gRhD5lQo8K
        Y/GI1se0PhZGYyYvnNsbux2E3qQDF3kT66Pvis9nM4LTmMkJ/Tens09hJj8SYxmnMHbdD8NXPvhH6Vm/
        FbyJdWFK+WjleKr/9FRGIBYYRGWPQf+14+hO2IepzE/x8MYxfPyh/2RBgTBGLJHY8abWgVYhea+3/Fbf
        ZNpxfJkdjIFbQTD8cT+6L/wOE7cDuWoYkn1xIjgE7R0dMJqax6trak+LxJKlvIv/DxqNZnmT0ZhhmZrC
        k9lnaJfkoDTEHXXxnuhJPoKJOycWZoLaczXMH4KcPIyOjmFoeAQDg0NQqev6iktK3+fdfTuo1Oo3O81m
        s8XyJaYogOnpR+iol0J59iC6/+yPyexQOoJ0IuiEtFw/iYjIGAyPPMAwiXf39KK5pQ29ff1QquogLCz6
        C+/2f0eDVvf30bExTEw8xMOHE5ictKBTXYWOzwMwmRuDOelVzEuvYCwnFnHhoajXaLjMe3r70NrWDp3+
        PmpqlVwwYqkcAkHux7zrV0MqlTmau7o5ZyyIMcbxcehqRBgtSMC8ir2WM9B29yziI86gskr0X+KGJiMq
        qsS4b2hCg1bPBZdXIBwU5OTa8BIvh1Kp+j0LoKW1DYNDw8QRjDx4AFNLK+IiwpCaEIXEqBDExp1jfcYg
        9buLMl0YwBbUKlTQaLRcBaSyagpCh+LScpqR3H28xHf49cVQq+tS29o7oKcMevv60E+97B8Y5KoxMDC0
        INCgJeFBrs9d3T0k3glTcyv09w3IzRdy16z0ZRVVUNdpUF4pQlp6xg1eguGbg1AoVRVNpmZoG/Uwm7u5
        PrLyMjFWERYMu2b7rFIsWFNzC4k3ccGVVVSiVqni2sAyZ5WoFIlx4VJSPbn/LnGxFS8OIitboGukTFgA
        zdQGJtDRaUanuYsTpNPB3XPC9LzJ2IzGRgNVRQdhUTFXdpFIgpKyCtwrKYNUXoMqsRR30jMHyf0a4g+I
        tkyL8PUgjh71qc/NL4BW1wg9TbPBaILR2AKTqZWOVyu3snu2z55rdWzQGqCgI5d+NxsiiQyl5ZWcOCNr
        hUgiRUFh0Qy5/ynxx8TVRPY19fXB9PLyXh8REdXKescca2iIWDXY0WokspXds332XKWqRw2VXiyVITNL
        wPWdCbPys5UFxFh4r/gpuXcmvkP8CXEt8cVvy8ioqO/fTL3112rqn0KphkJdD6VaA3W9hlvZvUKhhrxG
        CVl1NSdeVFzKvQ2Z8FcpYcNIQ3j8w48s5Jr9YWX/GTcQF9vwzUg8f+G3WQJBo4z6KKtmrOUEWV8lXGYS
        mnAxV/K8gkLkC4u43jOyvbQ7GQgLC4fTps1PVyxfcZtcbiWu5Jx/G0RFR7teSvpTVl6+cKKSpptNOCs1
        E1rMtPBeCZKSkumjNQzHjvlg+6+24+c/cxxdtXJVNrn4DXEV8eXvgFeBvgttvL2POgcGnfjkTFj4tbDw
        iJI/xMUr4uLP1dFau3nT5vK1a9akrXRwCLezs3MjEyb6Bmf8UixZ8i8hC7uie4//DAAAAABJRU5ErkJg
        gg==
</value>
  </data>
  <data name="resource.Image4" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABzhJREFUWEet
        VwlMlOkZtqykxWpBU4+13ZhNW2qbzaoQJVsVShtNRLHrsYgSYF0W3K2ICOEsaMGDsgbremBXBIQp14BQ
        zrk6wBwwYYZxmCGcw1FugRSZBTwQefp+Pz/tNutqt86TPPn+//vnfZ/3+v7Mv+RVcHFxedfT0/Ocj49P
        mb+/v87b27th7969wm3btn2ycePG5fzPrA9HR8d3duzYoSBRhISEIDIyEtHR0QgNDUVAQAC8vLywc+fO
        f27YsGEPb2I9kFNvNze32YMHD8LX1xeBgYEIDg7mGBQUBD8/Pxw4cAC7du2Cs7Pz3Lp1697lTV8fDg4O
        blu3boWrqyt2796NPR4eoJLDg+i5fz8+oMwPHz4MD9qnILFlyxasX78+jzd/faxbZf/GoR2br0cGHEHs
        p/64GHkSaZfiUJV+GXW5V5F9/gz27XaHu7s7nJycsHr1asZJ3tw6GClJuQizAujWAP16YMAA9NJ1Zy1g
        LMW4NBVv/ehNULWwbNky2NvbY+nSpW/x5q8Pi/z239BRQwHUk3Aj0EdB9FAAbTJAXwjocuC3Zzu+Z2cH
        Gxsb2NrasvU93vz1YZGn6dEuB8zKhSowtlcDhhKgsQBoyMapQ+6gn36V7kTrYFqR2Ys2yYIoC8RUAdy/
        R9kLAa0A0GTiyK+dsMflF3h7rf1iAC6csTVgKb/8ALpcEsuhcuf9hw0kXp8BKG8hI+IoIEuBNOX0YgAO
        nLE1MFmUMATlF0BdOpctNHdJmFb1HUCRinn553guTsZc+XkUJwYycfmCpZUwkR9nnJdfBWpuEFM5UdTe
        xHz1NTyXXMGcKBnPyhPxrCwBm95e+4hMfrlgaSWMZYUJ56qS8Ez8GQkypuC56PKCcOVFzJLwbHE8Ery2
        j69ZbuvKm1kPAzcDT86WnMMTEuIypVI/KUvEbGkCnhSfxWNhDKYLYqGI9jjEm1gXXZd9f2gRhD5lQo8K
        Y/GI1se0PhZGYyYvnNsbux2E3qQDF3kT66Pvis9nM4LTmMkJ/Tens09hJj8SYxmnMHbdD8NXPvhH6Vm/
        FbyJdWFK+WjleKr/9FRGIBYYRGWPQf+14+hO2IepzE/x8MYxfPyh/2RBgTBGLJHY8abWgVYhea+3/Fbf
        ZNpxfJkdjIFbQTD8cT+6L/wOE7cDuWoYkn1xIjgE7R0dMJqax6trak+LxJKlvIv/DxqNZnmT0ZhhmZrC
        k9lnaJfkoDTEHXXxnuhJPoKJOycWZoLaczXMH4KcPIyOjmFoeAQDg0NQqev6iktK3+fdfTuo1Oo3O81m
        s8XyJaYogOnpR+iol0J59iC6/+yPyexQOoJ0IuiEtFw/iYjIGAyPPMAwiXf39KK5pQ29ff1QquogLCz6
        C+/2f0eDVvf30bExTEw8xMOHE5ictKBTXYWOzwMwmRuDOelVzEuvYCwnFnHhoajXaLjMe3r70NrWDp3+
        PmpqlVwwYqkcAkHux7zrV0MqlTmau7o5ZyyIMcbxcehqRBgtSMC8ir2WM9B29yziI86gskr0X+KGJiMq
        qsS4b2hCg1bPBZdXIBwU5OTa8BIvh1Kp+j0LoKW1DYNDw8QRjDx4AFNLK+IiwpCaEIXEqBDExp1jfcYg
        9buLMl0YwBbUKlTQaLRcBaSyagpCh+LScpqR3H28xHf49cVQq+tS29o7oKcMevv60E+97B8Y5KoxMDC0
        INCgJeFBrs9d3T0k3glTcyv09w3IzRdy16z0ZRVVUNdpUF4pQlp6xg1eguGbg1AoVRVNpmZoG/Uwm7u5
        PrLyMjFWERYMu2b7rFIsWFNzC4k3ccGVVVSiVqni2sAyZ5WoFIlx4VJSPbn/LnGxFS8OIitboGukTFgA
        zdQGJtDRaUanuYsTpNPB3XPC9LzJ2IzGRgNVRQdhUTFXdpFIgpKyCtwrKYNUXoMqsRR30jMHyf0a4g+I
        tkyL8PUgjh71qc/NL4BW1wg9TbPBaILR2AKTqZWOVyu3snu2z55rdWzQGqCgI5d+NxsiiQyl5ZWcOCNr
        hUgiRUFh0Qy5/ynxx8TVRPY19fXB9PLyXh8REdXKescca2iIWDXY0WokspXds332XKWqRw2VXiyVITNL
        wPWdCbPys5UFxFh4r/gpuXcmvkP8CXEt8cVvy8ioqO/fTL3112rqn0KphkJdD6VaA3W9hlvZvUKhhrxG
        CVl1NSdeVFzKvQ2Z8FcpYcNIQ3j8w48s5Jr9YWX/GTcQF9vwzUg8f+G3WQJBo4z6KKtmrOUEWV8lXGYS
        mnAxV/K8gkLkC4u43jOyvbQ7GQgLC4fTps1PVyxfcZtcbiWu5Jx/G0RFR7teSvpTVl6+cKKSpptNOCs1
        E1rMtPBeCZKSkumjNQzHjvlg+6+24+c/cxxdtXJVNrn4DXEV8eXvgFeBvgttvL2POgcGnfjkTFj4tbDw
        iJI/xMUr4uLP1dFau3nT5vK1a9akrXRwCLezs3MjEyb6Bmf8UixZ8i8hC7uie4//DAAAAABJRU5ErkJg
        gg==
</value>
  </data>
  <data name="resource.Image5" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABzhJREFUWEet
        VwlMlOkZtqykxWpBU4+13ZhNW2qbzaoQJVsVShtNRLHrsYgSYF0W3K2ICOEsaMGDsgbremBXBIQp14BQ
        zrk6wBwwYYZxmCGcw1FugRSZBTwQefp+Pz/tNutqt86TPPn+//vnfZ/3+v7Mv+RVcHFxedfT0/Ocj49P
        mb+/v87b27th7969wm3btn2ycePG5fzPrA9HR8d3duzYoSBRhISEIDIyEtHR0QgNDUVAQAC8vLywc+fO
        f27YsGEPb2I9kFNvNze32YMHD8LX1xeBgYEIDg7mGBQUBD8/Pxw4cAC7du2Cs7Pz3Lp1697lTV8fDg4O
        blu3boWrqyt2796NPR4eoJLDg+i5fz8+oMwPHz4MD9qnILFlyxasX78+jzd/faxbZf/GoR2br0cGHEHs
        p/64GHkSaZfiUJV+GXW5V5F9/gz27XaHu7s7nJycsHr1asZJ3tw6GClJuQizAujWAP16YMAA9NJ1Zy1g
        LMW4NBVv/ehNULWwbNky2NvbY+nSpW/x5q8Pi/z239BRQwHUk3Aj0EdB9FAAbTJAXwjocuC3Zzu+Z2cH
        Gxsb2NrasvU93vz1YZGn6dEuB8zKhSowtlcDhhKgsQBoyMapQ+6gn36V7kTrYFqR2Ys2yYIoC8RUAdy/
        R9kLAa0A0GTiyK+dsMflF3h7rf1iAC6csTVgKb/8ALpcEsuhcuf9hw0kXp8BKG8hI+IoIEuBNOX0YgAO
        nLE1MFmUMATlF0BdOpctNHdJmFb1HUCRinn553guTsZc+XkUJwYycfmCpZUwkR9nnJdfBWpuEFM5UdTe
        xHz1NTyXXMGcKBnPyhPxrCwBm95e+4hMfrlgaSWMZYUJ56qS8Ez8GQkypuC56PKCcOVFzJLwbHE8Ery2
        j69ZbuvKm1kPAzcDT86WnMMTEuIypVI/KUvEbGkCnhSfxWNhDKYLYqGI9jjEm1gXXZd9f2gRhD5lQo8K
        Y/GI1se0PhZGYyYvnNsbux2E3qQDF3kT66Pvis9nM4LTmMkJ/Tens09hJj8SYxmnMHbdD8NXPvhH6Vm/
        FbyJdWFK+WjleKr/9FRGIBYYRGWPQf+14+hO2IepzE/x8MYxfPyh/2RBgTBGLJHY8abWgVYhea+3/Fbf
        ZNpxfJkdjIFbQTD8cT+6L/wOE7cDuWoYkn1xIjgE7R0dMJqax6trak+LxJKlvIv/DxqNZnmT0ZhhmZrC
        k9lnaJfkoDTEHXXxnuhJPoKJOycWZoLaczXMH4KcPIyOjmFoeAQDg0NQqev6iktK3+fdfTuo1Oo3O81m
        s8XyJaYogOnpR+iol0J59iC6/+yPyexQOoJ0IuiEtFw/iYjIGAyPPMAwiXf39KK5pQ29ff1QquogLCz6
        C+/2f0eDVvf30bExTEw8xMOHE5ictKBTXYWOzwMwmRuDOelVzEuvYCwnFnHhoajXaLjMe3r70NrWDp3+
        PmpqlVwwYqkcAkHux7zrV0MqlTmau7o5ZyyIMcbxcehqRBgtSMC8ir2WM9B29yziI86gskr0X+KGJiMq
        qsS4b2hCg1bPBZdXIBwU5OTa8BIvh1Kp+j0LoKW1DYNDw8QRjDx4AFNLK+IiwpCaEIXEqBDExp1jfcYg
        9buLMl0YwBbUKlTQaLRcBaSyagpCh+LScpqR3H28xHf49cVQq+tS29o7oKcMevv60E+97B8Y5KoxMDC0
        INCgJeFBrs9d3T0k3glTcyv09w3IzRdy16z0ZRVVUNdpUF4pQlp6xg1eguGbg1AoVRVNpmZoG/Uwm7u5
        PrLyMjFWERYMu2b7rFIsWFNzC4k3ccGVVVSiVqni2sAyZ5WoFIlx4VJSPbn/LnGxFS8OIitboGukTFgA
        zdQGJtDRaUanuYsTpNPB3XPC9LzJ2IzGRgNVRQdhUTFXdpFIgpKyCtwrKYNUXoMqsRR30jMHyf0a4g+I
        tkyL8PUgjh71qc/NL4BW1wg9TbPBaILR2AKTqZWOVyu3snu2z55rdWzQGqCgI5d+NxsiiQyl5ZWcOCNr
        hUgiRUFh0Qy5/ynxx8TVRPY19fXB9PLyXh8REdXKescca2iIWDXY0WokspXds332XKWqRw2VXiyVITNL
        wPWdCbPys5UFxFh4r/gpuXcmvkP8CXEt8cVvy8ioqO/fTL3112rqn0KphkJdD6VaA3W9hlvZvUKhhrxG
        CVl1NSdeVFzKvQ2Z8FcpYcNIQ3j8w48s5Jr9YWX/GTcQF9vwzUg8f+G3WQJBo4z6KKtmrOUEWV8lXGYS
        mnAxV/K8gkLkC4u43jOyvbQ7GQgLC4fTps1PVyxfcZtcbiWu5Jx/G0RFR7teSvpTVl6+cKKSpptNOCs1
        E1rMtPBeCZKSkumjNQzHjvlg+6+24+c/cxxdtXJVNrn4DXEV8eXvgFeBvgttvL2POgcGnfjkTFj4tbDw
        iJI/xMUr4uLP1dFau3nT5vK1a9akrXRwCLezs3MjEyb6Bmf8UixZ8i8hC7uie4//DAAAAABJRU5ErkJg
        gg==
</value>
  </data>
</root>