﻿namespace SMART_CLINIC_SOFTWARE_2020.Main_Form
{
    partial class frmDIAGNOIS_MAIN
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraEditors.TileItemElement tileItemElement1 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement2 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement3 = new DevExpress.XtraEditors.TileItemElement();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmDIAGNOIS_MAIN));
            DevExpress.XtraEditors.TileItemFrame tileItemFrame1 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement4 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement5 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement6 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame2 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement7 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement8 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement9 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame3 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement10 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement11 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement12 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement13 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement14 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement15 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame4 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement16 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement17 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement18 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame5 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement19 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement20 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement21 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame6 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement22 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement23 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement24 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame7 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement25 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement26 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement27 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement28 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement29 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement30 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame8 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement31 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement32 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement33 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame9 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement34 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement35 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement36 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame10 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement37 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement38 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement39 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement40 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement41 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement42 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame11 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement43 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement44 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement45 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame12 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement46 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement47 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement48 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame13 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement49 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement50 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement51 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame14 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement52 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement53 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement54 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement55 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement56 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement57 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame15 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement58 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement59 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement60 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame16 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement61 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement62 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement63 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement64 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement65 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement66 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame17 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement67 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement68 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement69 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame18 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement70 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement71 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement72 = new DevExpress.XtraEditors.TileItemElement();
            this.tileItem1 = new DevExpress.XtraEditors.TileItem();
            this.tileItem2 = new DevExpress.XtraEditors.TileItem();
            this.tileGroup1 = new DevExpress.XtraEditors.TileGroup();
            this.tileGroup2 = new DevExpress.XtraEditors.TileGroup();
            this.btnDOC_LIST = new DevExpress.XtraEditors.TileItem();
            this.btnEDIT_DOC = new DevExpress.XtraEditors.TileItem();
            this.tileControl1 = new DevExpress.XtraEditors.TileControl();
            this.tileLIST = new DevExpress.XtraEditors.TileGroup();
            this.btnDIG_LIST = new DevExpress.XtraEditors.TileItem();
            this.tileDATA = new DevExpress.XtraEditors.TileGroup();
            this.btnEDIT_DIG = new DevExpress.XtraEditors.TileItem();
            this.SuspendLayout();
            // 
            // tileItem1
            // 
            tileItemElement1.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement1.Appearance.Hovered.Options.UseFont = true;
            tileItemElement1.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement1.Appearance.Normal.Options.UseFont = true;
            tileItemElement1.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement1.Appearance.Selected.Options.UseFont = true;
            tileItemElement1.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.MaxWidth = 160;
            tileItemElement1.Text = "قائمة الاطباء";
            tileItemElement1.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement1.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement2.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Hovered.Options.UseFont = true;
            tileItemElement2.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Normal.Options.UseFont = true;
            tileItemElement2.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Selected.Options.UseFont = true;
            tileItemElement2.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.MaxWidth = 160;
            tileItemElement2.Text = "";
            tileItemElement2.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement2.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement3.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image")));
            tileItemElement3.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement3.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement3.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement3.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement3.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement3.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            this.tileItem1.Elements.Add(tileItemElement1);
            this.tileItem1.Elements.Add(tileItemElement2);
            this.tileItem1.Elements.Add(tileItemElement3);
            tileItemFrame1.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemElement4.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement4.Appearance.Hovered.Options.UseFont = true;
            tileItemElement4.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement4.Appearance.Normal.Options.UseFont = true;
            tileItemElement4.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement4.Appearance.Selected.Options.UseFont = true;
            tileItemElement4.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.MaxWidth = 160;
            tileItemElement4.Text = "قائمة الاطباء";
            tileItemElement4.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement4.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement5.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Hovered.Options.UseFont = true;
            tileItemElement5.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Normal.Options.UseFont = true;
            tileItemElement5.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Selected.Options.UseFont = true;
            tileItemElement5.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.MaxWidth = 160;
            tileItemElement5.Text = "";
            tileItemElement5.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement5.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement6.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image1")));
            tileItemElement6.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement6.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement6.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement6.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement6.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement6.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame1.Elements.Add(tileItemElement4);
            tileItemFrame1.Elements.Add(tileItemElement5);
            tileItemFrame1.Elements.Add(tileItemElement6);
            tileItemFrame2.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemElement7.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement7.Appearance.Hovered.Options.UseFont = true;
            tileItemElement7.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement7.Appearance.Normal.Options.UseFont = true;
            tileItemElement7.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement7.Appearance.Selected.Options.UseFont = true;
            tileItemElement7.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.MaxWidth = 160;
            tileItemElement7.Text = "قائمة الاطباء";
            tileItemElement7.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement7.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement8.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Hovered.Options.UseFont = true;
            tileItemElement8.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Normal.Options.UseFont = true;
            tileItemElement8.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Selected.Options.UseFont = true;
            tileItemElement8.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.MaxWidth = 160;
            tileItemElement8.Text = "";
            tileItemElement8.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement8.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement9.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image2")));
            tileItemElement9.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement9.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement9.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement9.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement9.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement9.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame2.Elements.Add(tileItemElement7);
            tileItemFrame2.Elements.Add(tileItemElement8);
            tileItemFrame2.Elements.Add(tileItemElement9);
            tileItemFrame3.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollLeft;
            tileItemElement10.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement10.Appearance.Hovered.Options.UseFont = true;
            tileItemElement10.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement10.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement10.Appearance.Normal.Options.UseFont = true;
            tileItemElement10.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement10.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement10.Appearance.Selected.Options.UseFont = true;
            tileItemElement10.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement10.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.MaxWidth = 160;
            tileItemElement10.Text = "قائمة الاطباء";
            tileItemElement10.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement10.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement11.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement11.Appearance.Hovered.Options.UseFont = true;
            tileItemElement11.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement11.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement11.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement11.Appearance.Normal.Options.UseFont = true;
            tileItemElement11.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement11.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement11.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement11.Appearance.Selected.Options.UseFont = true;
            tileItemElement11.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement11.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement11.MaxWidth = 160;
            tileItemElement11.Text = "";
            tileItemElement11.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement11.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement12.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image3")));
            tileItemElement12.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement12.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement12.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement12.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement12.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement12.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame3.Elements.Add(tileItemElement10);
            tileItemFrame3.Elements.Add(tileItemElement11);
            tileItemFrame3.Elements.Add(tileItemElement12);
            this.tileItem1.Frames.Add(tileItemFrame1);
            this.tileItem1.Frames.Add(tileItemFrame2);
            this.tileItem1.Frames.Add(tileItemFrame3);
            this.tileItem1.Id = 0;
            this.tileItem1.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tileItem1.Name = "tileItem1";
            // 
            // tileItem2
            // 
            tileItemElement13.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement13.Appearance.Hovered.Options.UseFont = true;
            tileItemElement13.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement13.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement13.Appearance.Normal.Options.UseFont = true;
            tileItemElement13.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement13.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement13.Appearance.Pressed.Options.UseFont = true;
            tileItemElement13.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement13.Appearance.Selected.Options.UseFont = true;
            tileItemElement13.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement13.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.MaxWidth = 160;
            tileItemElement13.Text = "أضافة _ تعديل الأطباء";
            tileItemElement13.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement13.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement14.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement14.Appearance.Hovered.Options.UseFont = true;
            tileItemElement14.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement14.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement14.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement14.Appearance.Normal.Options.UseFont = true;
            tileItemElement14.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement14.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement14.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement14.Appearance.Selected.Options.UseFont = true;
            tileItemElement14.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement14.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement14.MaxWidth = 160;
            tileItemElement14.Text = "";
            tileItemElement14.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement14.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement15.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image4")));
            tileItemElement15.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement15.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement15.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement15.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.tileItem2.Elements.Add(tileItemElement13);
            this.tileItem2.Elements.Add(tileItemElement14);
            this.tileItem2.Elements.Add(tileItemElement15);
            tileItemFrame4.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollRight;
            tileItemElement16.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement16.Appearance.Hovered.Options.UseFont = true;
            tileItemElement16.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement16.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement16.Appearance.Normal.Options.UseFont = true;
            tileItemElement16.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement16.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement16.Appearance.Pressed.Options.UseFont = true;
            tileItemElement16.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement16.Appearance.Selected.Options.UseFont = true;
            tileItemElement16.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement16.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.MaxWidth = 160;
            tileItemElement16.Text = "أضافة _ تعديل الأطباء";
            tileItemElement16.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement16.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement17.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement17.Appearance.Hovered.Options.UseFont = true;
            tileItemElement17.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement17.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement17.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement17.Appearance.Normal.Options.UseFont = true;
            tileItemElement17.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement17.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement17.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement17.Appearance.Selected.Options.UseFont = true;
            tileItemElement17.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement17.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement17.MaxWidth = 160;
            tileItemElement17.Text = "";
            tileItemElement17.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement17.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement18.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image5")));
            tileItemElement18.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement18.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement18.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement18.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame4.Elements.Add(tileItemElement16);
            tileItemFrame4.Elements.Add(tileItemElement17);
            tileItemFrame4.Elements.Add(tileItemElement18);
            tileItemFrame5.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.Fade;
            tileItemElement19.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement19.Appearance.Hovered.Options.UseFont = true;
            tileItemElement19.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement19.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement19.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement19.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement19.Appearance.Normal.Options.UseFont = true;
            tileItemElement19.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement19.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement19.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement19.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement19.Appearance.Pressed.Options.UseFont = true;
            tileItemElement19.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement19.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement19.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement19.Appearance.Selected.Options.UseFont = true;
            tileItemElement19.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement19.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement19.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement19.MaxWidth = 160;
            tileItemElement19.Text = "أضافة _ تعديل الأطباء";
            tileItemElement19.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement19.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement20.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement20.Appearance.Hovered.Options.UseFont = true;
            tileItemElement20.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement20.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement20.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement20.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement20.Appearance.Normal.Options.UseFont = true;
            tileItemElement20.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement20.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement20.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement20.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement20.Appearance.Selected.Options.UseFont = true;
            tileItemElement20.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement20.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement20.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement20.MaxWidth = 160;
            tileItemElement20.Text = "";
            tileItemElement20.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement20.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement21.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image6")));
            tileItemElement21.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement21.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement21.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement21.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame5.Elements.Add(tileItemElement19);
            tileItemFrame5.Elements.Add(tileItemElement20);
            tileItemFrame5.Elements.Add(tileItemElement21);
            tileItemFrame6.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.SegmentedFade;
            tileItemElement22.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement22.Appearance.Hovered.Options.UseFont = true;
            tileItemElement22.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement22.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement22.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement22.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement22.Appearance.Normal.Options.UseFont = true;
            tileItemElement22.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement22.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement22.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement22.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement22.Appearance.Pressed.Options.UseFont = true;
            tileItemElement22.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement22.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement22.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement22.Appearance.Selected.Options.UseFont = true;
            tileItemElement22.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement22.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement22.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement22.MaxWidth = 160;
            tileItemElement22.Text = "أضافة _ تعديل الأطباء";
            tileItemElement22.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement22.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement23.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement23.Appearance.Hovered.Options.UseFont = true;
            tileItemElement23.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement23.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement23.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement23.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement23.Appearance.Normal.Options.UseFont = true;
            tileItemElement23.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement23.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement23.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement23.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement23.Appearance.Selected.Options.UseFont = true;
            tileItemElement23.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement23.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement23.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement23.MaxWidth = 160;
            tileItemElement23.Text = "";
            tileItemElement23.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement23.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement24.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image7")));
            tileItemElement24.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement24.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement24.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement24.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame6.Elements.Add(tileItemElement22);
            tileItemFrame6.Elements.Add(tileItemElement23);
            tileItemFrame6.Elements.Add(tileItemElement24);
            tileItemElement25.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement25.Appearance.Hovered.Options.UseFont = true;
            tileItemElement25.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement25.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement25.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement25.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement25.Appearance.Normal.Options.UseFont = true;
            tileItemElement25.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement25.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement25.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement25.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement25.Appearance.Pressed.Options.UseFont = true;
            tileItemElement25.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement25.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement25.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement25.Appearance.Selected.Options.UseFont = true;
            tileItemElement25.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement25.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement25.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement25.MaxWidth = 160;
            tileItemElement25.Text = "أضافة _ تعديل الأطباء";
            tileItemElement25.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement25.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement26.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement26.Appearance.Hovered.Options.UseFont = true;
            tileItemElement26.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement26.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement26.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement26.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement26.Appearance.Normal.Options.UseFont = true;
            tileItemElement26.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement26.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement26.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement26.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement26.Appearance.Selected.Options.UseFont = true;
            tileItemElement26.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement26.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement26.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement26.MaxWidth = 160;
            tileItemElement26.Text = "";
            tileItemElement26.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement26.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement27.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image8")));
            tileItemElement27.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement27.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement27.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement27.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame7.Elements.Add(tileItemElement25);
            tileItemFrame7.Elements.Add(tileItemElement26);
            tileItemFrame7.Elements.Add(tileItemElement27);
            this.tileItem2.Frames.Add(tileItemFrame4);
            this.tileItem2.Frames.Add(tileItemFrame5);
            this.tileItem2.Frames.Add(tileItemFrame6);
            this.tileItem2.Frames.Add(tileItemFrame7);
            this.tileItem2.Id = 2;
            this.tileItem2.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tileItem2.Name = "tileItem2";
            // 
            // tileGroup1
            // 
            this.tileGroup1.Name = "tileGroup1";
            // 
            // tileGroup2
            // 
            this.tileGroup2.Name = "tileGroup2";
            // 
            // btnDOC_LIST
            // 
            tileItemElement28.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement28.Appearance.Hovered.Options.UseFont = true;
            tileItemElement28.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement28.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement28.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement28.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement28.Appearance.Normal.Options.UseFont = true;
            tileItemElement28.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement28.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement28.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement28.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement28.Appearance.Selected.Options.UseFont = true;
            tileItemElement28.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement28.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement28.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement28.MaxWidth = 160;
            tileItemElement28.Text = "قائمة الاطباء";
            tileItemElement28.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement28.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement29.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement29.Appearance.Hovered.Options.UseFont = true;
            tileItemElement29.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement29.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement29.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement29.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement29.Appearance.Normal.Options.UseFont = true;
            tileItemElement29.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement29.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement29.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement29.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement29.Appearance.Selected.Options.UseFont = true;
            tileItemElement29.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement29.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement29.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement29.MaxWidth = 160;
            tileItemElement29.Text = "";
            tileItemElement29.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement29.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement30.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image9")));
            tileItemElement30.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement30.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement30.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement30.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement30.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement30.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            this.btnDOC_LIST.Elements.Add(tileItemElement28);
            this.btnDOC_LIST.Elements.Add(tileItemElement29);
            this.btnDOC_LIST.Elements.Add(tileItemElement30);
            tileItemFrame8.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemElement31.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement31.Appearance.Hovered.Options.UseFont = true;
            tileItemElement31.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement31.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement31.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement31.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement31.Appearance.Normal.Options.UseFont = true;
            tileItemElement31.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement31.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement31.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement31.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement31.Appearance.Selected.Options.UseFont = true;
            tileItemElement31.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement31.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement31.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement31.MaxWidth = 160;
            tileItemElement31.Text = "قائمة الاطباء";
            tileItemElement31.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement31.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement32.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement32.Appearance.Hovered.Options.UseFont = true;
            tileItemElement32.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement32.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement32.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement32.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement32.Appearance.Normal.Options.UseFont = true;
            tileItemElement32.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement32.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement32.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement32.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement32.Appearance.Selected.Options.UseFont = true;
            tileItemElement32.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement32.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement32.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement32.MaxWidth = 160;
            tileItemElement32.Text = "";
            tileItemElement32.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement32.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement33.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image10")));
            tileItemElement33.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement33.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement33.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement33.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement33.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement33.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame8.Elements.Add(tileItemElement31);
            tileItemFrame8.Elements.Add(tileItemElement32);
            tileItemFrame8.Elements.Add(tileItemElement33);
            tileItemFrame9.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemElement34.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement34.Appearance.Hovered.Options.UseFont = true;
            tileItemElement34.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement34.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement34.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement34.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement34.Appearance.Normal.Options.UseFont = true;
            tileItemElement34.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement34.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement34.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement34.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement34.Appearance.Selected.Options.UseFont = true;
            tileItemElement34.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement34.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement34.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement34.MaxWidth = 160;
            tileItemElement34.Text = "قائمة الاطباء";
            tileItemElement34.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement34.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement35.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement35.Appearance.Hovered.Options.UseFont = true;
            tileItemElement35.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement35.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement35.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement35.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement35.Appearance.Normal.Options.UseFont = true;
            tileItemElement35.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement35.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement35.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement35.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement35.Appearance.Selected.Options.UseFont = true;
            tileItemElement35.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement35.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement35.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement35.MaxWidth = 160;
            tileItemElement35.Text = "";
            tileItemElement35.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement35.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement36.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image11")));
            tileItemElement36.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement36.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement36.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement36.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement36.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement36.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame9.Elements.Add(tileItemElement34);
            tileItemFrame9.Elements.Add(tileItemElement35);
            tileItemFrame9.Elements.Add(tileItemElement36);
            tileItemFrame10.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollLeft;
            tileItemElement37.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement37.Appearance.Hovered.Options.UseFont = true;
            tileItemElement37.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement37.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement37.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement37.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement37.Appearance.Normal.Options.UseFont = true;
            tileItemElement37.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement37.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement37.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement37.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement37.Appearance.Selected.Options.UseFont = true;
            tileItemElement37.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement37.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement37.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement37.MaxWidth = 160;
            tileItemElement37.Text = "قائمة الاطباء";
            tileItemElement37.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement37.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement38.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement38.Appearance.Hovered.Options.UseFont = true;
            tileItemElement38.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement38.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement38.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement38.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement38.Appearance.Normal.Options.UseFont = true;
            tileItemElement38.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement38.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement38.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement38.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement38.Appearance.Selected.Options.UseFont = true;
            tileItemElement38.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement38.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement38.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement38.MaxWidth = 160;
            tileItemElement38.Text = "";
            tileItemElement38.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement38.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement39.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image12")));
            tileItemElement39.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement39.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement39.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement39.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement39.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement39.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame10.Elements.Add(tileItemElement37);
            tileItemFrame10.Elements.Add(tileItemElement38);
            tileItemFrame10.Elements.Add(tileItemElement39);
            this.btnDOC_LIST.Frames.Add(tileItemFrame8);
            this.btnDOC_LIST.Frames.Add(tileItemFrame9);
            this.btnDOC_LIST.Frames.Add(tileItemFrame10);
            this.btnDOC_LIST.Id = 0;
            this.btnDOC_LIST.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnDOC_LIST.Name = "btnDOC_LIST";
            // 
            // btnEDIT_DOC
            // 
            tileItemElement40.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement40.Appearance.Hovered.Options.UseFont = true;
            tileItemElement40.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement40.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement40.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement40.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement40.Appearance.Normal.Options.UseFont = true;
            tileItemElement40.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement40.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement40.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement40.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement40.Appearance.Pressed.Options.UseFont = true;
            tileItemElement40.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement40.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement40.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement40.Appearance.Selected.Options.UseFont = true;
            tileItemElement40.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement40.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement40.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement40.MaxWidth = 160;
            tileItemElement40.Text = "أضافة _ تعديل الأطباء";
            tileItemElement40.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement40.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement41.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement41.Appearance.Hovered.Options.UseFont = true;
            tileItemElement41.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement41.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement41.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement41.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement41.Appearance.Normal.Options.UseFont = true;
            tileItemElement41.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement41.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement41.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement41.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement41.Appearance.Selected.Options.UseFont = true;
            tileItemElement41.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement41.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement41.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement41.MaxWidth = 160;
            tileItemElement41.Text = "";
            tileItemElement41.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement41.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement42.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image13")));
            tileItemElement42.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement42.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement42.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement42.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.btnEDIT_DOC.Elements.Add(tileItemElement40);
            this.btnEDIT_DOC.Elements.Add(tileItemElement41);
            this.btnEDIT_DOC.Elements.Add(tileItemElement42);
            tileItemFrame11.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollRight;
            tileItemElement43.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement43.Appearance.Hovered.Options.UseFont = true;
            tileItemElement43.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement43.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement43.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement43.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement43.Appearance.Normal.Options.UseFont = true;
            tileItemElement43.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement43.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement43.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement43.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement43.Appearance.Pressed.Options.UseFont = true;
            tileItemElement43.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement43.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement43.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement43.Appearance.Selected.Options.UseFont = true;
            tileItemElement43.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement43.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement43.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement43.MaxWidth = 160;
            tileItemElement43.Text = "أضافة _ تعديل الأطباء";
            tileItemElement43.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement43.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement44.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement44.Appearance.Hovered.Options.UseFont = true;
            tileItemElement44.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement44.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement44.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement44.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement44.Appearance.Normal.Options.UseFont = true;
            tileItemElement44.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement44.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement44.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement44.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement44.Appearance.Selected.Options.UseFont = true;
            tileItemElement44.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement44.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement44.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement44.MaxWidth = 160;
            tileItemElement44.Text = "";
            tileItemElement44.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement44.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement45.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image14")));
            tileItemElement45.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement45.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement45.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement45.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame11.Elements.Add(tileItemElement43);
            tileItemFrame11.Elements.Add(tileItemElement44);
            tileItemFrame11.Elements.Add(tileItemElement45);
            tileItemFrame12.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.Fade;
            tileItemElement46.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement46.Appearance.Hovered.Options.UseFont = true;
            tileItemElement46.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement46.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement46.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement46.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement46.Appearance.Normal.Options.UseFont = true;
            tileItemElement46.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement46.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement46.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement46.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement46.Appearance.Pressed.Options.UseFont = true;
            tileItemElement46.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement46.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement46.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement46.Appearance.Selected.Options.UseFont = true;
            tileItemElement46.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement46.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement46.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement46.MaxWidth = 160;
            tileItemElement46.Text = "أضافة _ تعديل الأطباء";
            tileItemElement46.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement46.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement47.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement47.Appearance.Hovered.Options.UseFont = true;
            tileItemElement47.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement47.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement47.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement47.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement47.Appearance.Normal.Options.UseFont = true;
            tileItemElement47.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement47.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement47.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement47.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement47.Appearance.Selected.Options.UseFont = true;
            tileItemElement47.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement47.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement47.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement47.MaxWidth = 160;
            tileItemElement47.Text = "";
            tileItemElement47.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement47.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement48.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image15")));
            tileItemElement48.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement48.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement48.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement48.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame12.Elements.Add(tileItemElement46);
            tileItemFrame12.Elements.Add(tileItemElement47);
            tileItemFrame12.Elements.Add(tileItemElement48);
            tileItemFrame13.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.SegmentedFade;
            tileItemElement49.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement49.Appearance.Hovered.Options.UseFont = true;
            tileItemElement49.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement49.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement49.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement49.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement49.Appearance.Normal.Options.UseFont = true;
            tileItemElement49.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement49.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement49.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement49.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement49.Appearance.Pressed.Options.UseFont = true;
            tileItemElement49.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement49.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement49.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement49.Appearance.Selected.Options.UseFont = true;
            tileItemElement49.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement49.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement49.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement49.MaxWidth = 160;
            tileItemElement49.Text = "أضافة _ تعديل الأطباء";
            tileItemElement49.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement49.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement50.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement50.Appearance.Hovered.Options.UseFont = true;
            tileItemElement50.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement50.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement50.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement50.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement50.Appearance.Normal.Options.UseFont = true;
            tileItemElement50.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement50.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement50.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement50.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement50.Appearance.Selected.Options.UseFont = true;
            tileItemElement50.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement50.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement50.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement50.MaxWidth = 160;
            tileItemElement50.Text = "";
            tileItemElement50.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement50.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement51.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image16")));
            tileItemElement51.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement51.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement51.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement51.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame13.Elements.Add(tileItemElement49);
            tileItemFrame13.Elements.Add(tileItemElement50);
            tileItemFrame13.Elements.Add(tileItemElement51);
            tileItemElement52.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement52.Appearance.Hovered.Options.UseFont = true;
            tileItemElement52.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement52.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement52.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement52.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement52.Appearance.Normal.Options.UseFont = true;
            tileItemElement52.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement52.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement52.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement52.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement52.Appearance.Pressed.Options.UseFont = true;
            tileItemElement52.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement52.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement52.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement52.Appearance.Selected.Options.UseFont = true;
            tileItemElement52.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement52.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement52.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement52.MaxWidth = 160;
            tileItemElement52.Text = "أضافة _ تعديل الأطباء";
            tileItemElement52.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement52.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement53.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement53.Appearance.Hovered.Options.UseFont = true;
            tileItemElement53.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement53.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement53.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement53.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement53.Appearance.Normal.Options.UseFont = true;
            tileItemElement53.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement53.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement53.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement53.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement53.Appearance.Selected.Options.UseFont = true;
            tileItemElement53.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement53.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement53.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement53.MaxWidth = 160;
            tileItemElement53.Text = "";
            tileItemElement53.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement53.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement54.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image17")));
            tileItemElement54.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement54.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement54.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement54.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame14.Elements.Add(tileItemElement52);
            tileItemFrame14.Elements.Add(tileItemElement53);
            tileItemFrame14.Elements.Add(tileItemElement54);
            this.btnEDIT_DOC.Frames.Add(tileItemFrame11);
            this.btnEDIT_DOC.Frames.Add(tileItemFrame12);
            this.btnEDIT_DOC.Frames.Add(tileItemFrame13);
            this.btnEDIT_DOC.Frames.Add(tileItemFrame14);
            this.btnEDIT_DOC.Id = 2;
            this.btnEDIT_DOC.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnEDIT_DOC.Name = "btnEDIT_DOC";
            // 
            // tileControl1
            // 
            this.tileControl1.AllowItemHover = true;
            this.tileControl1.AllowSelectedItem = true;
            this.tileControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Style3D;
            this.tileControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tileControl1.Groups.Add(this.tileLIST);
            this.tileControl1.Groups.Add(this.tileDATA);
            this.tileControl1.IndentBetweenGroups = 10;
            this.tileControl1.Location = new System.Drawing.Point(0, 0);
            this.tileControl1.MaxId = 3;
            this.tileControl1.Name = "tileControl1";
            this.tileControl1.Size = new System.Drawing.Size(822, 380);
            this.tileControl1.TabIndex = 3;
            this.tileControl1.Text = "tileControl1";
            // 
            // tileLIST
            // 
            this.tileLIST.Items.Add(this.btnDIG_LIST);
            this.tileLIST.Name = "tileLIST";
            this.tileLIST.Tag = "قائمة_التشخيص";
            // 
            // btnDIG_LIST
            // 
            this.btnDIG_LIST.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDIG_LIST.AppearanceItem.Hovered.Options.UseFont = true;
            this.btnDIG_LIST.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.btnDIG_LIST.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.btnDIG_LIST.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.btnDIG_LIST.AppearanceItem.Normal.Options.UseBackColor = true;
            this.btnDIG_LIST.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.btnDIG_LIST.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.btnDIG_LIST.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.btnDIG_LIST.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.btnDIG_LIST.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.btnDIG_LIST.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement55.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement55.Appearance.Hovered.Options.UseFont = true;
            tileItemElement55.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement55.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement55.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement55.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement55.Appearance.Normal.Options.UseFont = true;
            tileItemElement55.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement55.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement55.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement55.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement55.Appearance.Pressed.Options.UseFont = true;
            tileItemElement55.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement55.Appearance.Selected.Options.UseFont = true;
            tileItemElement55.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement55.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement55.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement55.MaxWidth = 160;
            tileItemElement55.Text = "قائمة التشخيص";
            tileItemElement55.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement55.TextLocation = new System.Drawing.Point(75, -25);
            tileItemElement56.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement56.Appearance.Hovered.Options.UseFont = true;
            tileItemElement56.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement56.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement56.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement56.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement56.Appearance.Normal.Options.UseFont = true;
            tileItemElement56.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement56.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement56.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement56.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement56.Appearance.Selected.Options.UseFont = true;
            tileItemElement56.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement56.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement56.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement56.MaxWidth = 160;
            tileItemElement56.Text = "";
            tileItemElement56.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement56.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement57.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image18")));
            tileItemElement57.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement57.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement57.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement57.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement57.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement57.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            this.btnDIG_LIST.Elements.Add(tileItemElement55);
            this.btnDIG_LIST.Elements.Add(tileItemElement56);
            this.btnDIG_LIST.Elements.Add(tileItemElement57);
            tileItemFrame15.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame15.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(71)))), ((int)(((byte)(0)))));
            tileItemFrame15.Appearance.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(71)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            tileItemFrame15.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame15.Appearance.Options.UseBackColor = true;
            tileItemFrame15.Appearance.Options.UseBorderColor = true;
            tileItemElement58.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement58.Appearance.Hovered.Options.UseFont = true;
            tileItemElement58.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement58.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement58.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement58.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement58.Appearance.Normal.Options.UseFont = true;
            tileItemElement58.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement58.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement58.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement58.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement58.Appearance.Pressed.Options.UseFont = true;
            tileItemElement58.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement58.Appearance.Selected.Options.UseFont = true;
            tileItemElement58.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement58.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement58.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement58.MaxWidth = 160;
            tileItemElement58.Text = "قائمة التشخيص";
            tileItemElement58.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement58.TextLocation = new System.Drawing.Point(75, -25);
            tileItemElement59.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement59.Appearance.Hovered.Options.UseFont = true;
            tileItemElement59.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement59.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement59.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement59.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement59.Appearance.Normal.Options.UseFont = true;
            tileItemElement59.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement59.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement59.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement59.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement59.Appearance.Selected.Options.UseFont = true;
            tileItemElement59.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement59.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement59.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement59.MaxWidth = 160;
            tileItemElement59.Text = "";
            tileItemElement59.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement59.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement60.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image19")));
            tileItemElement60.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement60.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement60.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement60.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement60.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement60.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame15.Elements.Add(tileItemElement58);
            tileItemFrame15.Elements.Add(tileItemElement59);
            tileItemFrame15.Elements.Add(tileItemElement60);
            tileItemFrame16.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame16.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(71)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            tileItemFrame16.Appearance.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(71)))), ((int)(((byte)(0)))));
            tileItemFrame16.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame16.Appearance.Options.UseBackColor = true;
            tileItemFrame16.Appearance.Options.UseBorderColor = true;
            tileItemElement61.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement61.Appearance.Hovered.Options.UseFont = true;
            tileItemElement61.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement61.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement61.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement61.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement61.Appearance.Normal.Options.UseFont = true;
            tileItemElement61.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement61.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement61.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement61.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement61.Appearance.Pressed.Options.UseFont = true;
            tileItemElement61.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement61.Appearance.Selected.Options.UseFont = true;
            tileItemElement61.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement61.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement61.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement61.MaxWidth = 160;
            tileItemElement61.Text = "قائمة التشخيص";
            tileItemElement61.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement61.TextLocation = new System.Drawing.Point(75, -25);
            tileItemElement62.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement62.Appearance.Hovered.Options.UseFont = true;
            tileItemElement62.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement62.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement62.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement62.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement62.Appearance.Normal.Options.UseFont = true;
            tileItemElement62.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement62.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement62.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement62.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement62.Appearance.Selected.Options.UseFont = true;
            tileItemElement62.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement62.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement62.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement62.MaxWidth = 160;
            tileItemElement62.Text = "";
            tileItemElement62.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement62.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement63.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image20")));
            tileItemElement63.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement63.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement63.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement63.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement63.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement63.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame16.Elements.Add(tileItemElement61);
            tileItemFrame16.Elements.Add(tileItemElement62);
            tileItemFrame16.Elements.Add(tileItemElement63);
            this.btnDIG_LIST.Frames.Add(tileItemFrame15);
            this.btnDIG_LIST.Frames.Add(tileItemFrame16);
            this.btnDIG_LIST.Id = 0;
            this.btnDIG_LIST.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnDIG_LIST.Name = "btnDIG_LIST";
            this.btnDIG_LIST.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnDIG_LIST_ItemClick);
            // 
            // tileDATA
            // 
            this.tileDATA.Items.Add(this.btnEDIT_DIG);
            this.tileDATA.Name = "tileDATA";
            this.tileDATA.Tag = "بيانات_التشخيص";
            // 
            // btnEDIT_DIG
            // 
            this.btnEDIT_DIG.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnEDIT_DIG.AppearanceItem.Hovered.Options.UseFont = true;
            this.btnEDIT_DIG.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.btnEDIT_DIG.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.btnEDIT_DIG.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.btnEDIT_DIG.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnEDIT_DIG.AppearanceItem.Normal.Options.UseBackColor = true;
            this.btnEDIT_DIG.AppearanceItem.Normal.Options.UseFont = true;
            this.btnEDIT_DIG.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.btnEDIT_DIG.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.btnEDIT_DIG.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnEDIT_DIG.AppearanceItem.Pressed.Options.UseFont = true;
            this.btnEDIT_DIG.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.btnEDIT_DIG.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.btnEDIT_DIG.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnEDIT_DIG.AppearanceItem.Selected.Options.UseFont = true;
            this.btnEDIT_DIG.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.btnEDIT_DIG.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement64.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement64.Appearance.Hovered.Options.UseFont = true;
            tileItemElement64.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement64.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement64.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement64.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement64.Appearance.Normal.Options.UseFont = true;
            tileItemElement64.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement64.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement64.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement64.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement64.Appearance.Pressed.Options.UseFont = true;
            tileItemElement64.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement64.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement64.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement64.Appearance.Selected.Options.UseFont = true;
            tileItemElement64.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement64.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement64.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement64.MaxWidth = 160;
            tileItemElement64.Text = "تعديل التشخيص";
            tileItemElement64.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement64.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement65.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement65.Appearance.Hovered.Options.UseFont = true;
            tileItemElement65.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement65.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement65.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement65.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement65.Appearance.Normal.Options.UseFont = true;
            tileItemElement65.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement65.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement65.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement65.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement65.Appearance.Pressed.Options.UseFont = true;
            tileItemElement65.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement65.Appearance.Selected.Options.UseFont = true;
            tileItemElement65.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement65.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement65.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement65.MaxWidth = 160;
            tileItemElement65.Text = "أضافة";
            tileItemElement65.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement65.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement66.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image21")));
            tileItemElement66.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement66.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement66.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement66.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.btnEDIT_DIG.Elements.Add(tileItemElement64);
            this.btnEDIT_DIG.Elements.Add(tileItemElement65);
            this.btnEDIT_DIG.Elements.Add(tileItemElement66);
            tileItemFrame17.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame17.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(71)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            tileItemFrame17.Appearance.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(71)))), ((int)(((byte)(0)))));
            tileItemFrame17.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame17.Appearance.Options.UseBackColor = true;
            tileItemFrame17.Appearance.Options.UseBorderColor = true;
            tileItemElement67.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement67.Appearance.Hovered.Options.UseFont = true;
            tileItemElement67.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement67.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement67.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement67.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement67.Appearance.Normal.Options.UseFont = true;
            tileItemElement67.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement67.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement67.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement67.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement67.Appearance.Pressed.Options.UseFont = true;
            tileItemElement67.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement67.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement67.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement67.Appearance.Selected.Options.UseFont = true;
            tileItemElement67.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement67.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement67.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement67.MaxWidth = 160;
            tileItemElement67.Text = "تعديل التشخيص";
            tileItemElement67.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement67.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement68.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement68.Appearance.Hovered.Options.UseFont = true;
            tileItemElement68.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement68.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement68.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement68.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement68.Appearance.Normal.Options.UseFont = true;
            tileItemElement68.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement68.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement68.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement68.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement68.Appearance.Pressed.Options.UseFont = true;
            tileItemElement68.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement68.Appearance.Selected.Options.UseFont = true;
            tileItemElement68.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement68.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement68.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement68.MaxWidth = 160;
            tileItemElement68.Text = "أضافة";
            tileItemElement68.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement68.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement69.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image22")));
            tileItemElement69.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement69.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement69.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement69.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame17.Elements.Add(tileItemElement67);
            tileItemFrame17.Elements.Add(tileItemElement68);
            tileItemFrame17.Elements.Add(tileItemElement69);
            tileItemFrame18.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame18.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(71)))), ((int)(((byte)(0)))));
            tileItemFrame18.Appearance.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(71)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            tileItemFrame18.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame18.Appearance.Options.UseBackColor = true;
            tileItemFrame18.Appearance.Options.UseBorderColor = true;
            tileItemElement70.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement70.Appearance.Hovered.Options.UseFont = true;
            tileItemElement70.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement70.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement70.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement70.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement70.Appearance.Normal.Options.UseFont = true;
            tileItemElement70.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement70.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement70.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement70.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement70.Appearance.Pressed.Options.UseFont = true;
            tileItemElement70.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement70.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement70.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement70.Appearance.Selected.Options.UseFont = true;
            tileItemElement70.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement70.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement70.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement70.MaxWidth = 160;
            tileItemElement70.Text = "تعديل التشخيص";
            tileItemElement70.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement70.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement71.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement71.Appearance.Hovered.Options.UseFont = true;
            tileItemElement71.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement71.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement71.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement71.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement71.Appearance.Normal.Options.UseFont = true;
            tileItemElement71.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement71.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement71.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement71.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement71.Appearance.Pressed.Options.UseFont = true;
            tileItemElement71.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement71.Appearance.Selected.Options.UseFont = true;
            tileItemElement71.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement71.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement71.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement71.MaxWidth = 160;
            tileItemElement71.Text = "أضافة";
            tileItemElement71.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement71.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement72.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image23")));
            tileItemElement72.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement72.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement72.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement72.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame18.Elements.Add(tileItemElement70);
            tileItemFrame18.Elements.Add(tileItemElement71);
            tileItemFrame18.Elements.Add(tileItemElement72);
            this.btnEDIT_DIG.Frames.Add(tileItemFrame17);
            this.btnEDIT_DIG.Frames.Add(tileItemFrame18);
            this.btnEDIT_DIG.Id = 2;
            this.btnEDIT_DIG.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnEDIT_DIG.Name = "btnEDIT_DIG";
            this.btnEDIT_DIG.Tag = "frmDIAGNOIS";
            this.btnEDIT_DIG.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnEDIT_DIG_ItemClick);
            // 
            // frmDIAGNOIS_MAIN
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(822, 380);
            this.Controls.Add(this.tileControl1);
            this.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.MaximizeBox = false;
            this.Name = "frmDIAGNOIS_MAIN";
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "frmDIAGNOIS_MAIN";
            this.Load += new System.EventHandler(this.frmDIAGNOIS_MAIN_Load);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.TileItem tileItem1;
        private DevExpress.XtraEditors.TileItem tileItem2;
        private DevExpress.XtraEditors.TileGroup tileGroup1;
        private DevExpress.XtraEditors.TileGroup tileGroup2;
        private DevExpress.XtraEditors.TileItem btnDOC_LIST;
        private DevExpress.XtraEditors.TileItem btnEDIT_DOC;
        private DevExpress.XtraEditors.TileControl tileControl1;
        private DevExpress.XtraEditors.TileGroup tileLIST;
        private DevExpress.XtraEditors.TileItem btnDIG_LIST;
        private DevExpress.XtraEditors.TileGroup tileDATA;
        private DevExpress.XtraEditors.TileItem btnEDIT_DIG;
    }
}