﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="resource.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image1" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image2" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image3" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABbdJREFUWEe1
        V2lMVFcYVdRqXaqtdrO1SdPaajWxjal/qk0Ta2sj1hUFREqBooAwrCoMRRZZZJFFBKGIVHZEhEE2BxgH
        EByiBBBQHJBFLLZp+qc/2tjg6bnPNwkhMFKdnuSEAO/7vnO/e+737ptmDEf7rqw+0lft6d1bdVahryh2
        7inLtL99yceq68L78iP/H1g0yUtfiUN3L+NAjwrf374Em+4i7OkqwM5beaPbbuUWkm/Kj5sWP9wpWcmi
        o+OKggXHc4RcK4eZDmxxpJGi4/k7+Z4cahowYceYAlNhMzldDn8+MNE8cnR3UzqcSmPgdSEC3oXhcC6N
        hmX9GWzryJlIgOAqOcXzYWdb9lybmiS9KlqBugQf1MZ7Q33SA9Ux7rgc4YKMkwr4XDqB7W3ZY4s/IufI
        KZ4fFT8FuDQkH4U26Qg0ib6SiCuxHqiKdkN5pAtUYQdgbvElNqpiDAJa5VDToDk1IKUpVYnGFH9oTwsR
        PqiJ85S6UHHCFWXhB7FjwyeYvXA+Ps8Jwda2rFQ51DRoPB+munYuBNqMINSkB0Kd4ocaqQsKCjhEAc7w
        9rCEn/UmrF77ITaWRjvKoaZBbH99U+iAFsp7teBAgiuHkSNngtPNHHjVJuP4+SBk0B8Xg+zhue+r0fXH
        HD+SQ02DqKHG1vECHO6UYB+H0q7OfFhRxPW8U5IA991flMhhpgMF1BkTYHc1Dc25CTiutP/bxmbLCjnM
        dDgx2JhsTIADBQSlh2K/6x7Tms+AyMGGb40J2NqQBk+lHZS2m/PlENMibEBrFjpwtX0iAfa6TITGuiPc
        wRxpXnvb5RDTI7y7/N0oXcHI0S4VDrcWILg6GanpAcj0s0WC605k++9HfqTL42hNjm9wZ+ULcphpkV0Q
        tbosVtFZGnYQuQF2SPO2wnkKEL+LWRCvOoOw1nIoe9W9bm3F5nKY6RCnr3M4N9QymHY5HYXHHDiGXaVp
        KAZSTkYIgnQl+LGrAh76CmmrbHU5+Za6rJfl8GdH/P1ms9iha1nJD3QoftiFs5XnpAkoXkriBVV1xh9B
        2jwE3iiB+91yONIfvK7RtDXY25E/sKM1a42c6tmQOHw9nAKQ92sHCkY6kNpShqJwV6m4KvM4QtQ/w7+p
        CIrbZeAdEbzASKdjT2cB/PvUsOwq/GNXR+7Hcrr/hqRh3QcU8E8MBRT81omskXak6hsR31aFSNHy5ovw
        bSqEG80prmyi6Ha+EQ3cy98DeXr4985dt/LM5LRTx+nhlqiE+9cRPdSIrIftSPvlJhKHmhGt1yC4uxqH
        u8vhynbbsThXKl3bxgoQdOH/3bg1m6sTt8hppw4KuEkPIGqoAakPboDdAAcTjvXXwbe3+slMkC+rYrU7
        xhUX3Mct4ZUen8Yqss1mz1rAtLPIqV3ZkoZb/oy734QTLCp+ji3uxuIH7pSy9cUcShdgMa79Boqu+FGA
        uSalnymXkovJeeTThZwa1uGkVLge4YNaqbhYjeEbwYkC7CjAmqvcTeNNJEDQk0eTzzx+YdEC8bJ6jVxI
        ziWNi2DLH4kTEEEBwf0aBPBoHebqxVkXe89vBnzXfZHOLwRNNmFxwYP8mBHPvbJm+TdM+wYp5oPogpia
        k5uT+58dQwOK1Qdx9cp7ara/Cgp9uWQuceZtmXgyAxootkkc0aWb1tkxrWEbhB/E5XUGOTG4+vk04BW+
        kKT2i3PtQwFi4IhVCQPuN2JAA4VJxbPLzNe7MO3b5Kuk2IYXyZnk5IiQ3oYa58D+2hFhJu/eyicG7CmV
        ViV9rj1FgBBo3Z7/10vLl21iynfI18lFpGEbnn4qlH3qOUd6q51oQB0N+FgYUDoBNKCFEQMK8qLas3jt
        CmumWU6KDiwhxRbMJiffgsnA/X+LBrSiscI4erN4Air4laT5Wn1KuyEzsP6zs0rNujjPopWHLCKXrFu1
        dfrMGeLLeWzLJzDetGn/AhJ7ILGdFoVZAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="resource.Image4" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABbdJREFUWEe1
        V2lMVFcYVdRqXaqtdrO1SdPaajWxjal/qk0Ta2sj1hUFREqBooAwrCoMRRZZZJFFBKGIVHZEhEE2BxgH
        EByiBBBQHJBFLLZp+qc/2tjg6bnPNwkhMFKdnuSEAO/7vnO/e+737ptmDEf7rqw+0lft6d1bdVahryh2
        7inLtL99yceq68L78iP/H1g0yUtfiUN3L+NAjwrf374Em+4i7OkqwM5beaPbbuUWkm/Kj5sWP9wpWcmi
        o+OKggXHc4RcK4eZDmxxpJGi4/k7+Z4cahowYceYAlNhMzldDn8+MNE8cnR3UzqcSmPgdSEC3oXhcC6N
        hmX9GWzryJlIgOAqOcXzYWdb9lybmiS9KlqBugQf1MZ7Q33SA9Ux7rgc4YKMkwr4XDqB7W3ZY4s/IufI
        KZ4fFT8FuDQkH4U26Qg0ib6SiCuxHqiKdkN5pAtUYQdgbvElNqpiDAJa5VDToDk1IKUpVYnGFH9oTwsR
        PqiJ85S6UHHCFWXhB7FjwyeYvXA+Ps8Jwda2rFQ51DRoPB+munYuBNqMINSkB0Kd4ocaqQsKCjhEAc7w
        9rCEn/UmrF77ITaWRjvKoaZBbH99U+iAFsp7teBAgiuHkSNngtPNHHjVJuP4+SBk0B8Xg+zhue+r0fXH
        HD+SQ02DqKHG1vECHO6UYB+H0q7OfFhRxPW8U5IA991flMhhpgMF1BkTYHc1Dc25CTiutP/bxmbLCjnM
        dDgx2JhsTIADBQSlh2K/6x7Tms+AyMGGb40J2NqQBk+lHZS2m/PlENMibEBrFjpwtX0iAfa6TITGuiPc
        wRxpXnvb5RDTI7y7/N0oXcHI0S4VDrcWILg6GanpAcj0s0WC605k++9HfqTL42hNjm9wZ+ULcphpkV0Q
        tbosVtFZGnYQuQF2SPO2wnkKEL+LWRCvOoOw1nIoe9W9bm3F5nKY6RCnr3M4N9QymHY5HYXHHDiGXaVp
        KAZSTkYIgnQl+LGrAh76CmmrbHU5+Za6rJfl8GdH/P1ms9iha1nJD3QoftiFs5XnpAkoXkriBVV1xh9B
        2jwE3iiB+91yONIfvK7RtDXY25E/sKM1a42c6tmQOHw9nAKQ92sHCkY6kNpShqJwV6m4KvM4QtQ/w7+p
        CIrbZeAdEbzASKdjT2cB/PvUsOwq/GNXR+7Hcrr/hqRh3QcU8E8MBRT81omskXak6hsR31aFSNHy5ovw
        bSqEG80prmyi6Ha+EQ3cy98DeXr4985dt/LM5LRTx+nhlqiE+9cRPdSIrIftSPvlJhKHmhGt1yC4uxqH
        u8vhynbbsThXKl3bxgoQdOH/3bg1m6sTt8hppw4KuEkPIGqoAakPboDdAAcTjvXXwbe3+slMkC+rYrU7
        xhUX3Mct4ZUen8Yqss1mz1rAtLPIqV3ZkoZb/oy734QTLCp+ji3uxuIH7pSy9cUcShdgMa79Boqu+FGA
        uSalnymXkovJeeTThZwa1uGkVLge4YNaqbhYjeEbwYkC7CjAmqvcTeNNJEDQk0eTzzx+YdEC8bJ6jVxI
        ziWNi2DLH4kTEEEBwf0aBPBoHebqxVkXe89vBnzXfZHOLwRNNmFxwYP8mBHPvbJm+TdM+wYp5oPogpia
        k5uT+58dQwOK1Qdx9cp7ara/Cgp9uWQuceZtmXgyAxootkkc0aWb1tkxrWEbhB/E5XUGOTG4+vk04BW+
        kKT2i3PtQwFi4IhVCQPuN2JAA4VJxbPLzNe7MO3b5Kuk2IYXyZnk5IiQ3oYa58D+2hFhJu/eyicG7CmV
        ViV9rj1FgBBo3Z7/10vLl21iynfI18lFpGEbnn4qlH3qOUd6q51oQB0N+FgYUDoBNKCFEQMK8qLas3jt
        CmumWU6KDiwhxRbMJiffgsnA/X+LBrSiscI4erN4Air4laT5Wn1KuyEzsP6zs0rNujjPopWHLCKXrFu1
        dfrMGeLLeWzLJzDetGn/AhJ7ILGdFoVZAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="resource.Image5" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABbdJREFUWEe1
        V2lMVFcYVdRqXaqtdrO1SdPaajWxjal/qk0Ta2sj1hUFREqBooAwrCoMRRZZZJFFBKGIVHZEhEE2BxgH
        EByiBBBQHJBFLLZp+qc/2tjg6bnPNwkhMFKdnuSEAO/7vnO/e+737ptmDEf7rqw+0lft6d1bdVahryh2
        7inLtL99yceq68L78iP/H1g0yUtfiUN3L+NAjwrf374Em+4i7OkqwM5beaPbbuUWkm/Kj5sWP9wpWcmi
        o+OKggXHc4RcK4eZDmxxpJGi4/k7+Z4cahowYceYAlNhMzldDn8+MNE8cnR3UzqcSmPgdSEC3oXhcC6N
        hmX9GWzryJlIgOAqOcXzYWdb9lybmiS9KlqBugQf1MZ7Q33SA9Ux7rgc4YKMkwr4XDqB7W3ZY4s/IufI
        KZ4fFT8FuDQkH4U26Qg0ib6SiCuxHqiKdkN5pAtUYQdgbvElNqpiDAJa5VDToDk1IKUpVYnGFH9oTwsR
        PqiJ85S6UHHCFWXhB7FjwyeYvXA+Ps8Jwda2rFQ51DRoPB+munYuBNqMINSkB0Kd4ocaqQsKCjhEAc7w
        9rCEn/UmrF77ITaWRjvKoaZBbH99U+iAFsp7teBAgiuHkSNngtPNHHjVJuP4+SBk0B8Xg+zhue+r0fXH
        HD+SQ02DqKHG1vECHO6UYB+H0q7OfFhRxPW8U5IA991flMhhpgMF1BkTYHc1Dc25CTiutP/bxmbLCjnM
        dDgx2JhsTIADBQSlh2K/6x7Tms+AyMGGb40J2NqQBk+lHZS2m/PlENMibEBrFjpwtX0iAfa6TITGuiPc
        wRxpXnvb5RDTI7y7/N0oXcHI0S4VDrcWILg6GanpAcj0s0WC605k++9HfqTL42hNjm9wZ+ULcphpkV0Q
        tbosVtFZGnYQuQF2SPO2wnkKEL+LWRCvOoOw1nIoe9W9bm3F5nKY6RCnr3M4N9QymHY5HYXHHDiGXaVp
        KAZSTkYIgnQl+LGrAh76CmmrbHU5+Za6rJfl8GdH/P1ms9iha1nJD3QoftiFs5XnpAkoXkriBVV1xh9B
        2jwE3iiB+91yONIfvK7RtDXY25E/sKM1a42c6tmQOHw9nAKQ92sHCkY6kNpShqJwV6m4KvM4QtQ/w7+p
        CIrbZeAdEbzASKdjT2cB/PvUsOwq/GNXR+7Hcrr/hqRh3QcU8E8MBRT81omskXak6hsR31aFSNHy5ovw
        bSqEG80prmyi6Ha+EQ3cy98DeXr4985dt/LM5LRTx+nhlqiE+9cRPdSIrIftSPvlJhKHmhGt1yC4uxqH
        u8vhynbbsThXKl3bxgoQdOH/3bg1m6sTt8hppw4KuEkPIGqoAakPboDdAAcTjvXXwbe3+slMkC+rYrU7
        xhUX3Mct4ZUen8Yqss1mz1rAtLPIqV3ZkoZb/oy734QTLCp+ji3uxuIH7pSy9cUcShdgMa79Boqu+FGA
        uSalnymXkovJeeTThZwa1uGkVLge4YNaqbhYjeEbwYkC7CjAmqvcTeNNJEDQk0eTzzx+YdEC8bJ6jVxI
        ziWNi2DLH4kTEEEBwf0aBPBoHebqxVkXe89vBnzXfZHOLwRNNmFxwYP8mBHPvbJm+TdM+wYp5oPogpia
        k5uT+58dQwOK1Qdx9cp7ara/Cgp9uWQuceZtmXgyAxootkkc0aWb1tkxrWEbhB/E5XUGOTG4+vk04BW+
        kKT2i3PtQwFi4IhVCQPuN2JAA4VJxbPLzNe7MO3b5Kuk2IYXyZnk5IiQ3oYa58D+2hFhJu/eyicG7CmV
        ViV9rj1FgBBo3Z7/10vLl21iynfI18lFpGEbnn4qlH3qOUd6q51oQB0N+FgYUDoBNKCFEQMK8qLas3jt
        CmumWU6KDiwhxRbMJiffgsnA/X+LBrSiscI4erN4Air4laT5Wn1KuyEzsP6zs0rNujjPopWHLCKXrFu1
        dfrMGeLLeWzLJzDetGn/AhJ7ILGdFoVZAAAAAElFTkSuQmCC
</value>
  </data>
</root>