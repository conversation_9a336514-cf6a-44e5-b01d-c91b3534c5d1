﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="resource.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAAGqRJREFUeF7V
        WwdUVde2pYiAgqCUS5WmNBUUsCAqVUGpomIBAQvYEbtiQ0E6WOi9F0F6L1ZQVEBRAcESu4maxERTTfLn
        X/tw/T/j/f/H+MlL3sM1xhr73HvPPWfPueZae+3DReCvsk2manIbZ6hZbpuj5bvXWvfg9lkaMbstxsXs
        NteM2W81LiTAUmvnpmlKbv6mKpP4X/n0bZWxyqTVxsoxG03VHhxxMESsxyykrLJAlo818tfZ4PQGa5Ss
        t0Lh2jnI8jJFynITRLlMxHYz1a99psgnrJksp8a/1Kdl680nqHkYKtbuttHDUWcjhC2aiuhlM3DKfRaS
        iYBMHxvk+tog39cK+T4WyFk1CxmepkgiAk4uMkSEoz6C7LSxzUz1Z3f90Zv5l/00zNNEfXbKoW3vi6MP
        IMLbFoGOk3HM1QSRbtMR52WBwj0rURm8EWf2r0ThVmcUbnFE/sb5SF1pijg3Y8QsNECIgy6OztPGAWtN
        7Jg9Fkt1pI7yLz/0zU5R8NxSreHw1JdAgr87CkO2I2zZbFRE7UPe3lUIXaCLgCni2DdJGIeNxXHQUBiB
        U0cibO5YRDnpI8RuPILpnMPzxiPAShM756hh43QluGqNtOffYmjbXHmB7mU6I7FUQxju44Zhv/1kFIXs
        wLZpctisNwx+ekLYaSCKHfqC2DdFDLsm0Ggkju16AtiqI4DN2gLYqCMIvymjsGvOWGwjBWw2VcZyPemn
        y/Wlhfm3GbpmpyDQ6KohgkVqQnAfPxzeeuJYqSEAX31RbNAfjg06wthEABlQBpi5v+7g603jBbBhHJ2r
        JYBVmgLw0hLCpmkK2EAKWG3Eg7PGCBf+bYauOakMi5mvJABnFQEsVhOEl/5IrFAXwBo9MawiYD4E0odG
        XwK6mohZQ0DX0/E6Bpw+W0ujN33uRe+z7y3XEMJaEwV4T5GHk8bIDP5thq5tMZByinfWhaeuBJyVBbBM
        SwRuYwXgoSUMDwLEQIXN00DpWlNc2mGN8/60LC7Whb+BBLyIEG8CvpIBV6Pv0vcWqdIxXcvDUA4O6iN6
        +bcZuhZvO3Z37xEn9Bx1hv80HpxICa4EYhGNO2co4up+R9wPX4KBY4twN8gFvYGOuL3PGjf8pyLKSglL
        CfRyIsCNCHClY6YkJ1VBLJskA0dNiQ/82wxdy3PVLugl8H3BC4mEhdg5UwmbjWWR4TEVj2NW4mHUCtwP
        W4r+0MXop3P6iKzuXbPRtXUqbvgZY9+00VzUXQk4U5ADpZOdIr3WkYa9liRsVcWk+bcamlawWO9G71GK
        bLAr+kMW416YGx5ELiNfgYfR7njACIhYioFQUsAxV/QcsMXNbTMI/DR0bjFGvZc+7AkwU449EWBHBNgo
        0LHmSMzXlICNsqgq/1ZD00rdDd8wAvqIgD4iYIAIuBe+jEAv50hg471QIoA+6ztsj+4dM0n+RAApoHOz
        Ea5tMMRKbTEu6rYE3IYnAAt5GseKwVptJKyVRIYuASXuhkINPtNJ+kTAUVcuwv2hlO9hSzgSBp1FfwmB
        X4BbO81wc8cM3Nw6HV1+JujYNAXt6w2wdYoUrAm4FRFgQeMsOQHMURwOSxVxWCoMk+PfbuhZ9kIduQtb
        ZqPniDN6gyi/g0nmwZTrBLg/1I3zu6SMOwHWHPhbLPpM/ltNOPlf2zgZrb6TOALMKeoMuKnsoM9SFIGF
        0nDM4YkI8W839CzLeZxG2zYL3Al05lQwSAIpgbw30IGqvRVJnoBvnzmY9xz4aejawqI/GVfWG+LS2onY
        PHkUTGWoMZrKQ6TTJNirDocZT4hIGfaMf6uhaRkOGgZtfma4scca3XuscHOXOUmcXvtPx03mBLh7+3QO
        OHvNIt+1xYgD3065f3HtJJxfrQ/fCZI4aiaHKg9dNPhZIctzOmbKEQE8wTr+rYampdmpTGrx1sUVkvFV
        AtRJOd1Fha3Tj5Y4Assq/aDcp3I537nJGNdZ3q8z5KR/fs0ENHnpIsRcHpXLtFCyWB2nl2ihfoc91QMh
        WMoL7uXfamhaog3PoGGlDkVyIlp9DIgEA1zfOIUiTCRsnsLlOSOFVXsG/NqGyWgj4Jd8JuHsqglo9tZD
        /crxqFo+DmVu6ihyHYuCRZqo8rPFbKoDDqrDx/FvNTQtzlJOrdxNCy2r9LhoXvKZgDYiguU2i3I7EcKk
        zl5fXmfARf3CWn2c9danyOug1n08qleMQ/lSTZxePBb5ziooXjkZkfY6MJcTbODfZuhavJWsWK6jCkVR
        hwPUvIrArZ6AC5TXTBUX1wz6BTpmBLXQ+yzqDZ7aqPXQRtWycSgl8Ez6hQtVkO2ohCIvE2qGBH9bpi35
        aTwrTLCW/4pJuJYiyYho9NQdJIOANpGzsdGTQFOuN3jqoI6AV6/QQgXlfCnJvphkn0/gcwh8rqsmfCdK
        wkFF5AD/8kPfYmaPuVTqpoGyZRpcLlcRuGqSNoswA1tDzsipofcqKeLlBLxsCUV9kRqKFqohz0UVOQ7K
        yLBXxGFTacyVF8zkX/rTsNDpoyJZ8SqmHD6zhIigqLKcLiNSWJSZly+l9wl0KX3OVXoCX8CiTjmfTcDT
        FyggZb4CNumJpfMv++lYsImEbbaDEvJdVDgpMzIYwGLmjBjyIjoudCVfOHYw4gy4oyIyCXyqnTzSndQR
        M0caeyeKWvAv++lY2AypYZFmUl9nOihyRSzbSZnyWQV5BDKXnI3csZMSshyVkUVkMbmnUcRT5skh0ZaH
        FCdNHDAUxV79Yev4l/207NBk8cgUW3mSMY+Tc8Z8kjWBZBFmI/d6AY8DnWzHQzKdm2gjizhrGaS66iDU
        VAoBBsOwS18kl3/JT8sCjUbIhM6QfJ9AoBJt5JA0j8a58hzQZBrZ6wSKdvxcGcTTOaesx+CExRgku2gj
        xloBBwyGI9BYFLt0hV5vt50qyr/sp2UBk0QPMVAnLEfjlJUMYplbDo4nyU9ZjMZJyzGIMR+NaPJEBt5G
        CfsNRBE2YySOmohhn4EQQta4Xl6+M7Zoolf8aP6lPw0LmCIhcdhI/Nuo2dKIpIIW/Q8eOUsaEWajEGUu
        gwRXPYTOlsUhAzFEzZREpKkER8CBKWKIjU+Bqnce5JemvJJZGLeAf/lPw7ZMGnMwxHQkKB0QSsBCmBO4
        kBnkppKIsVXFScfxOGQsgWNGI3GKyIo1J3LMJBFsIoq4bR4w31YAjfWV0PA9A1WvXMi4nEzV8kgW599i
        aNvUeb5z/Y3lccREHIHkR6aKI4jICLdSRIy9Fo7MlMFhQ3GcMhuNZEqPVBuqCZQy0WYSiLTTxK7oAqhv
        qoPW5lporiuDuk8xZu0qh4J9cJ+2Z8o0/m2GnvGWpQvKuZzcJOeR/9Z0TRICpoyg/b0MQiwVccxCAfuN
        JXGQgB+fLoVUSoHsuXLInS+PDFsqlpbSOD6H3j91EtqbqjBu+zmM29IIzY1VsDzYiBnrEuCX0wervZVQ
        dD1Zou+TN7T+hD7GNcFEdknKLU2KmqpvJfQO3cG87fmIWW6BXZPEKa8lEWVGQM3lkGUliyJaCkucFFHk
        oIBMOxkkEQFZgZvhfLAMOnuvYfyuVozzb4b10YuY7puIjdn92Fz8ObaWvIDryW7oemf8Iu9yIkLNM3sE
        fwr/HpNbFCco5Xg8VMUz5z801pdDaxMRsL4aeofvYElUG2pOHkaImTwtd4pIsVFAnq0iSqkZql6kigpX
        6hideMgmAjI2OSEstQLj9lyH3v4b0N59BeZB7TD3y4bHqWvwKfgcHpnPsCztEXaceYolSfdhsLMRkvMj
        v5ByPLGYP51/rY1xOj5S2jHmoqpnNtRWF1LBKoWaL+XsxjroHujG1uTLKDm0HglzVZBmq4ICe1VUumqg
        cbkmGpepo8pVmVRABKybi8qaekzefRYTgwagd/AWpgfdgOPhKiwIrIVX7kssTX8Kp8TPYHN8AGbhvfDN
        eQDXuD6o+p+H1MIEiNkcy5JemPive3Aq4xQjLGUf2a7qnoaxnllQW5UP9bXFUF1dBPXN9dDeex2Hsi6i
        Ono3Lq43QdVS2hUu18Z52hZf8NZG83J11CxSRvFWJzQ2NcMzpAKTwp9hYshDGAb1YcXJdpj552Jp5nO4
        JD+BbexDzI4egElILyYcugmtvR1wO3UDbsevQnvneYxelg1R62PF/On9/SY5PyxEaWk8VFakQMU9A2O9
        BlWg4p0PNVq+xu24hLDsFrSdbUBPgCX3h5COLSZoXz8JF7110OSuhZrInei4cRNZZ5qgHdANw4gXHAHe
        afdgtCaOIv4QdnGfwSLmPqaF9sPgSA+099+E+t5OqPhdwNS9TVif2QO7sHaMpdSTdI7HCNuIVfwp/n02
        2jFaSsb5xI9KS05CZXkSVN2JBI9MjPXOhfLKbIxdewaafk0IS6vAq9evcbUkEV27zdHlZ4zWzdPRFLwG
        XW3n8ejpc9y50wPz/fWYEHwfE489xOLUp5i6LhmWYTdhTsBnhPfD8GgPdCkttAJuDILfdonSrQTzwjpg
        GXwFjhFXMGNfExRWncZwq2NfSCyI/nvbaEm7cF9Ft3gouMZAmVNBMlQ9UrlUUCI1qHjTOr6hEskl5/DZ
        Z4/w3fff4/mLF7jff5fGl/j6m2/x6tVrPHr0GKFpVZQundA/3IvZUfcxL6ACRrsbKeJ9XMR1Dt6G1v5u
        aOztwtjdHVDZfgVyK3PhFHkVOiR9Tb8GOIa1wvJQMzSoX5Byiad6EPL3/sBKakFkncKSOPAWxkBp0Qko
        kQpUllPr6pFGZKRB0Z3UQPUgvqgZt2/fxlsC/O7de87fvv0Gb778CvfuP8DNm90w3l4NnYAOTDrUDdeo
        dqh7pUM/8DYmH+7EwuMdWBvfBr/Ec9iV1ISAtLM4nNGCwJQahBe0YWVkPWwO1sA2sBEW+xswYVMxFFek
        Q8Qy6D6bp4jlUUFhfTdBOmT+15m0Q9Q7hcVxkHeJJhKioLwskUhIJPBEhHsqeG6UFpQO0Tl1ePzkMbq7
        b+Hx0yecGnp6+8h78ZKUkFTZCa1tZ6G9qw0OMZTbbjHwjb+IqPRSpGbmIDo2EREnE8jZ+N9+IikdSTmF
        yC6pQF5pJU5XVCM+vwrrIsugRDVppF0ExiwIM2NzHTZz90cC/hoiJO0i1GSdT0BhUSx4zlGQdwqH0tKE
        QRKWMiUkQ35RHJQ90hGUVEHR/hKv33yJp0+f4cXLl6SGb/DNt9/i67ffwnDzGYzb2ozpBy9j4uok5JQ1
        ICYumQ80CaHhEThyYAcO7fLB/q0rOD/g74HgfWsRE7IbiXFRyMjLR15FHfLKa1FQXo2EghrIusRghHVQ
        JJuvsOn/IOCfI2Hk3GA7ancp/09Bzikacg6hUFxC9WBJAhEQzxEht/AklImIDcE5+OLVK46AL0n2X331
        Nb7+6i2+oTSILW2H2roK6PvXIrayG4nFLei4eQshwYex1cMMa+ZKw2uOIFZbCsPHejg2zBuBFbPEMX+K
        ODwtJOE5ZxQ2zx+DAFcejq7UR8ze5cjITEXLpcuckkTMA6/zp0yQhf83Epj/cRtpc2S9HFMAgeQ5R0LO
        PgSKlA6KS2KhQIWRqUHWheoCjS4706joPaeV4A1eEQmviYQviQTmButpufQpRfP1fvz004/4ipTxkIpi
        57U2ZERvRYCXIXysRImA4XA0Hg4HIzHYG42Ak4kEFk4bhSWm0lhmJo211tRl+luh4NQeNDc1IiqzkuYV
        AVGroJ+Ha9mICSpP/wj0H8F/9D9mogbLI+SdjkOBSOA5RXEEsHRQJEUokvSZGmSc6XNSxEzfeDx4+JBT
        ASPh9ZvX+JKISCpvh7JXHmZuL8G3775FUVMH9mS04tbAI3z48Aud/5rO/xLvKFUe3u3GxaZy1JzJxJnc
        eBRnx6I0LwkVxdloaqjGlY5OXGi/jmaK/KXrnfCPPg2eYwhG2ARDYJSKgYCwmBglwu9/Z/jnCRiuYSk0
        3MC9UN4xBgpEAs+BKSBsUA0cAUQE+RinGMiTKsYtjcEtWgVefv4FPv/iFQfs9Zs3mLqBegWvHNjtPU1F
        8ik2xpSDt7oEnjHn8MsvH/DDjz/h199+w2/kv/76Kz788ive01L6iOpIz8B9dN3uweXOmzh3hYC3XkNz
        21VuZAQ470iGwoIgjGIEyE1YQtOWIpdg0yf/2Cr/ORKEZLWHixp5N8s5UPUnEniONDpEQIEkz5HgQiQQ
        EWPofTkaZZ2jcfXqVTx99hzPX37OEZFXdx2KtGRqrcqGgsNR9PUPIDrjDBQ886konsbPH36mvuEH/Pzz
        ByLjV3r9AT/+9BP33pNnL4mAB+ggAs63d6LhwmU0XmyjsQ1Nl67gIilhsns0FG2DIGUdDMGxs/1p2jzy
        MeSMCLZzHEb+0f4YASLqs4RFjddclLePIuAE3j4a8gRWwZEk70QkuLDUYEScAC2VGEXnlFbV4TPKbRY9
        tgqYb83m2mc5x3AqlseRVd2G6sazcN2fB/kVmXj8/BXXOH33/Y/46eefSQ0/4v133+MtpcODx09wo+cu
        rnR1o5EAV7dc4Lzm7EWOgJK6c9B2jYQiRX+0ZRARMCeYps2eGyiTs5/YsF+aSZKzTvH3avj/m4iBe/KY
        +UQAc/tIyBNIBS4l+EQ40zHVh1ELIoiEGESnFlHT8xAPqAcoa+ngUkORmqgx8w5R0UyE075cXL3Rjezi
        CszYmIaq1h68++47Wirf4fsffsS799/RkvkNV0Tv0nWu3byNC1c7ONBl9S0ob2hBVfN5joCTWRUY7xAO
        BYujkCYXVLeIpynrkGuSsx9ZKZDLkDMiRpKztPhjNlxrrqjIZO+y0fMJ/IJIIoF6gQUEnkaOCEYI1Qep
        +eGQouMNhxNxd2CASLgPpz35UHRLgPTcQ1BcSOdRF8lzPYHUkkY0nm9FVsFpXO+6QYXxPd7QSvHu/Xt8
        ReC/oAL65PlL3L57D63XuziwZfXNKKqqx5naJk4F7L3y2kZkFZZi29E0THELh4DqrFSasjE5+wuzNrkG
        OVPDx7RgKfHHFMBsmMp0cWEd50Qp6rjkiQgeRZulhQIRwhFALkuvJezCMW9dFG7dukO52sEebEKGVg7Z
        eUeIpCjou59AYFwRCssqUEVpwIA0nL/EyZ2B/pJ6hs9fvcHjZy/Q/+AzXO++g7OXr3HnFVbWIq+sGiUE
        mimg8eJlpOcWIS41C4mZecgmMoPCY+7r6Or60JRnkZuQG5AzIlTIWU34fT34YyaiMn2YkPL0DSNsjn0v
        S0TwOEVQQaTUYGpgyhg5LxTai0PR2nYZnkcKIe8aC2nLA3ReFOy2JFELW0sRbEQRgck/U47TbCyrwQsq
        lk+ocD55/gIPHj3BnbsD6Opmhe8aqprO4TTVlayScmQVl6GQOsDSuiYisIVa52SEnxhsnU9Su5yYkYus
        /KLfFi9xS6Ap25DPJNcnl2UQyP85E9GyERKU1TMUmbbxirQtqYAjIpxThQK51NxQIiEEOYW0zJHUpecF
        QX5eMGaviSe51qOY5FvOj3wljSVVtSgmcLdoi3zv4Wfo7b+HOz19XId4+XoHGs61oqS6nkiqQlpBCVLz
        i5F9pmKQOCIw7Hj8fxFwIjEdCem5REI20nLy4ODoxFpjlg6sKLIa8Mfz//8yYfmJokKqZr5ic/a/GM2I
        sA0Hzy6MG8Wsj0HbLQKjqTcYY3mYCAhDUl4ViqsbUMYKWNN51J0frOINF1pRRe+1E9ieu/24QanT1X0b
        7R1duHD5Kmqbz1G/X4XMolLaDBUgMTsfGQXF3IYolY4Z8Oi4FMSnZdEeoYirKcm0qTqekExBKHorLCys
        S9NlBCiRsxT4a/8RQ4hnMEpI3WKH6OyAZ1LzaJkjsBJWwZCcHwEpq0DwKCXkbUJQ10zRJumXk5xrKfos
        f1luX7zWiXOX25GVm0fgGfBOXGPgr1Cjc+ESKuubkFNcirTcQsSlZyM2LRspdJxVdAah0ScIbB7yKC3O
        kJJOl1dyBCRlZNIGKxG59D11dXUrmiarAVrk8uR/nQp+b0LyE0cIqcxwH2a0tkncMviDhG0YZKyCwKOU
        kCNCaprOopSqOJN+PQFrvnQV565epwp/g1vmoo+f4FRwse0KWgn8WVoh6in6pdV1SM8vQlJWHo4np+N4
        UhriM3KQkpOPwKAQroA2nqN0qmtAUWkZMomc+LR0RJ+iFpoI4fF47HcHbEVgSmBL4t9DwEcTVpo2TFBW
        V12QZ7RJ2GBltajFkbcjqEHZf7IY1UQCW8tZF9dC0b9Ey1vHrR5a6gZwgYCnZ2ShsqYWLZQe9aSUalJM
        cXkFkrNycSolA5FxSYiITeLkfTw2Do0t53CJimTj2fOoqKlDfskZpBMxsUmpOBGfiOS0jFs0pankRuRM
        Bawf+POrwB81YUUTYaHRGhLCozVNR6gY7TxTUfWs7uwFrp9vp86u63YvbvX1o3fgHgYePsIALXttFP3G
        5hbU1jdyZJRWVCGbFJCZm4+C0yWoqW9A6+UruN3bh86btGmi9KkjYsuqapBHn6dkZiM+ORWllTXfqKio
        rKBpzCCfSM5qAG2S/k02Rl5JTEpKavKJ2LjLbdc60E4bmxt3+rjo9w6wrvEp1/h8Tpsn1hCxZuhr7kHK
        O645Yq3yj7RhYt3hZ7SZYgWTNVDnW9tQ29iEYuorGElFpeVIzcwaUFJSYuBNyRl4RfI/1wD9lUYEsEmo
        z54zZ2dWTl5/951eLpJ99x5wT4ufUy/AALLnBN8S6O9/+IHbFLHN0YdffuFaZLa7ZM8Qbvf04ioVzOZz
        51FeVY0qUkxWQeEzewdHtvSZk08hZ4WPyZ7tA/694D+apKSkKM2EVeMJ48eP99q9Z09+U1PTgwcPHv72
        mEh4TM0Qa4rYQ1QWebY5Ys8LvqO9AttY9d+7R8vkLbRR7rdQMc3My3/it217mYGhoR9dkz0TnEzO9gGs
        8WF/Wh96P7kXFx8hJCIiwibHosN6dUMxMTGriRMnblrouih6tY9vfsDBQ3XBYeEXQiMi20MiIq8eC49o
        PXD4SJ3P+g0FLq6LYgwNJ/sTmbb0XfancyZzBpr1+2ytZ7k+dP/X4KMRCYLDyISEhBgZbOJs+8p6dgaG
        /Vh6PLkeOVvGWDvLXjPCPjY2LMof9/y/f/jxJ0xA4D8BY2FnSrQWVFMAAAAASUVORK5CYII=
</value>
  </data>
  <data name="resource.Image1" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAAGqRJREFUeF7V
        WwdUVde2pYiAgqCUS5WmNBUUsCAqVUGpomIBAQvYEbtiQ0E6WOi9F0F6L1ZQVEBRAcESu4maxERTTfLn
        X/tw/T/j/f/H+MlL3sM1xhr73HvPPWfPueZae+3DReCvsk2manIbZ6hZbpuj5bvXWvfg9lkaMbstxsXs
        NteM2W81LiTAUmvnpmlKbv6mKpP4X/n0bZWxyqTVxsoxG03VHhxxMESsxyykrLJAlo818tfZ4PQGa5Ss
        t0Lh2jnI8jJFynITRLlMxHYz1a99psgnrJksp8a/1Kdl680nqHkYKtbuttHDUWcjhC2aiuhlM3DKfRaS
        iYBMHxvk+tog39cK+T4WyFk1CxmepkgiAk4uMkSEoz6C7LSxzUz1Z3f90Zv5l/00zNNEfXbKoW3vi6MP
        IMLbFoGOk3HM1QSRbtMR52WBwj0rURm8EWf2r0ThVmcUbnFE/sb5SF1pijg3Y8QsNECIgy6OztPGAWtN
        7Jg9Fkt1pI7yLz/0zU5R8NxSreHw1JdAgr87CkO2I2zZbFRE7UPe3lUIXaCLgCni2DdJGIeNxXHQUBiB
        U0cibO5YRDnpI8RuPILpnMPzxiPAShM756hh43QluGqNtOffYmjbXHmB7mU6I7FUQxju44Zhv/1kFIXs
        wLZpctisNwx+ekLYaSCKHfqC2DdFDLsm0Ggkju16AtiqI4DN2gLYqCMIvymjsGvOWGwjBWw2VcZyPemn
        y/Wlhfm3GbpmpyDQ6KohgkVqQnAfPxzeeuJYqSEAX31RbNAfjg06wthEABlQBpi5v+7g603jBbBhHJ2r
        JYBVmgLw0hLCpmkK2EAKWG3Eg7PGCBf+bYauOakMi5mvJABnFQEsVhOEl/5IrFAXwBo9MawiYD4E0odG
        XwK6mohZQ0DX0/E6Bpw+W0ujN33uRe+z7y3XEMJaEwV4T5GHk8bIDP5thq5tMZByinfWhaeuBJyVBbBM
        SwRuYwXgoSUMDwLEQIXN00DpWlNc2mGN8/60LC7Whb+BBLyIEG8CvpIBV6Pv0vcWqdIxXcvDUA4O6iN6
        +bcZuhZvO3Z37xEn9Bx1hv80HpxICa4EYhGNO2co4up+R9wPX4KBY4twN8gFvYGOuL3PGjf8pyLKSglL
        CfRyIsCNCHClY6YkJ1VBLJskA0dNiQ/82wxdy3PVLugl8H3BC4mEhdg5UwmbjWWR4TEVj2NW4mHUCtwP
        W4r+0MXop3P6iKzuXbPRtXUqbvgZY9+00VzUXQk4U5ADpZOdIr3WkYa9liRsVcWk+bcamlawWO9G71GK
        bLAr+kMW416YGx5ELiNfgYfR7njACIhYioFQUsAxV/QcsMXNbTMI/DR0bjFGvZc+7AkwU449EWBHBNgo
        0LHmSMzXlICNsqgq/1ZD00rdDd8wAvqIgD4iYIAIuBe+jEAv50hg471QIoA+6ztsj+4dM0n+RAApoHOz
        Ea5tMMRKbTEu6rYE3IYnAAt5GseKwVptJKyVRIYuASXuhkINPtNJ+kTAUVcuwv2hlO9hSzgSBp1FfwmB
        X4BbO81wc8cM3Nw6HV1+JujYNAXt6w2wdYoUrAm4FRFgQeMsOQHMURwOSxVxWCoMk+PfbuhZ9kIduQtb
        ZqPniDN6gyi/g0nmwZTrBLg/1I3zu6SMOwHWHPhbLPpM/ltNOPlf2zgZrb6TOALMKeoMuKnsoM9SFIGF
        0nDM4YkI8W839CzLeZxG2zYL3Al05lQwSAIpgbw30IGqvRVJnoBvnzmY9xz4aejawqI/GVfWG+LS2onY
        PHkUTGWoMZrKQ6TTJNirDocZT4hIGfaMf6uhaRkOGgZtfma4scca3XuscHOXOUmcXvtPx03mBLh7+3QO
        OHvNIt+1xYgD3065f3HtJJxfrQ/fCZI4aiaHKg9dNPhZIctzOmbKEQE8wTr+rYampdmpTGrx1sUVkvFV
        AtRJOd1Fha3Tj5Y4Assq/aDcp3I537nJGNdZ3q8z5KR/fs0ENHnpIsRcHpXLtFCyWB2nl2ihfoc91QMh
        WMoL7uXfamhaog3PoGGlDkVyIlp9DIgEA1zfOIUiTCRsnsLlOSOFVXsG/NqGyWgj4Jd8JuHsqglo9tZD
        /crxqFo+DmVu6ihyHYuCRZqo8rPFbKoDDqrDx/FvNTQtzlJOrdxNCy2r9LhoXvKZgDYiguU2i3I7EcKk
        zl5fXmfARf3CWn2c9danyOug1n08qleMQ/lSTZxePBb5ziooXjkZkfY6MJcTbODfZuhavJWsWK6jCkVR
        hwPUvIrArZ6AC5TXTBUX1wz6BTpmBLXQ+yzqDZ7aqPXQRtWycSgl8Ez6hQtVkO2ohCIvE2qGBH9bpi35
        aTwrTLCW/4pJuJYiyYho9NQdJIOANpGzsdGTQFOuN3jqoI6AV6/QQgXlfCnJvphkn0/gcwh8rqsmfCdK
        wkFF5AD/8kPfYmaPuVTqpoGyZRpcLlcRuGqSNoswA1tDzsipofcqKeLlBLxsCUV9kRqKFqohz0UVOQ7K
        yLBXxGFTacyVF8zkX/rTsNDpoyJZ8SqmHD6zhIigqLKcLiNSWJSZly+l9wl0KX3OVXoCX8CiTjmfTcDT
        FyggZb4CNumJpfMv++lYsImEbbaDEvJdVDgpMzIYwGLmjBjyIjoudCVfOHYw4gy4oyIyCXyqnTzSndQR
        M0caeyeKWvAv++lY2AypYZFmUl9nOihyRSzbSZnyWQV5BDKXnI3csZMSshyVkUVkMbmnUcRT5skh0ZaH
        FCdNHDAUxV79Yev4l/207NBk8cgUW3mSMY+Tc8Z8kjWBZBFmI/d6AY8DnWzHQzKdm2gjizhrGaS66iDU
        VAoBBsOwS18kl3/JT8sCjUbIhM6QfJ9AoBJt5JA0j8a58hzQZBrZ6wSKdvxcGcTTOaesx+CExRgku2gj
        xloBBwyGI9BYFLt0hV5vt50qyr/sp2UBk0QPMVAnLEfjlJUMYplbDo4nyU9ZjMZJyzGIMR+NaPJEBt5G
        CfsNRBE2YySOmohhn4EQQta4Xl6+M7Zoolf8aP6lPw0LmCIhcdhI/Nuo2dKIpIIW/Q8eOUsaEWajEGUu
        gwRXPYTOlsUhAzFEzZREpKkER8CBKWKIjU+Bqnce5JemvJJZGLeAf/lPw7ZMGnMwxHQkKB0QSsBCmBO4
        kBnkppKIsVXFScfxOGQsgWNGI3GKyIo1J3LMJBFsIoq4bR4w31YAjfWV0PA9A1WvXMi4nEzV8kgW599i
        aNvUeb5z/Y3lccREHIHkR6aKI4jICLdSRIy9Fo7MlMFhQ3GcMhuNZEqPVBuqCZQy0WYSiLTTxK7oAqhv
        qoPW5lporiuDuk8xZu0qh4J9cJ+2Z8o0/m2GnvGWpQvKuZzcJOeR/9Z0TRICpoyg/b0MQiwVccxCAfuN
        JXGQgB+fLoVUSoHsuXLInS+PDFsqlpbSOD6H3j91EtqbqjBu+zmM29IIzY1VsDzYiBnrEuCX0wervZVQ
        dD1Zou+TN7T+hD7GNcFEdknKLU2KmqpvJfQO3cG87fmIWW6BXZPEKa8lEWVGQM3lkGUliyJaCkucFFHk
        oIBMOxkkEQFZgZvhfLAMOnuvYfyuVozzb4b10YuY7puIjdn92Fz8ObaWvIDryW7oemf8Iu9yIkLNM3sE
        fwr/HpNbFCco5Xg8VMUz5z801pdDaxMRsL4aeofvYElUG2pOHkaImTwtd4pIsVFAnq0iSqkZql6kigpX
        6hideMgmAjI2OSEstQLj9lyH3v4b0N59BeZB7TD3y4bHqWvwKfgcHpnPsCztEXaceYolSfdhsLMRkvMj
        v5ByPLGYP51/rY1xOj5S2jHmoqpnNtRWF1LBKoWaL+XsxjroHujG1uTLKDm0HglzVZBmq4ICe1VUumqg
        cbkmGpepo8pVmVRABKybi8qaekzefRYTgwagd/AWpgfdgOPhKiwIrIVX7kssTX8Kp8TPYHN8AGbhvfDN
        eQDXuD6o+p+H1MIEiNkcy5JemPive3Aq4xQjLGUf2a7qnoaxnllQW5UP9bXFUF1dBPXN9dDeex2Hsi6i
        Ono3Lq43QdVS2hUu18Z52hZf8NZG83J11CxSRvFWJzQ2NcMzpAKTwp9hYshDGAb1YcXJdpj552Jp5nO4
        JD+BbexDzI4egElILyYcugmtvR1wO3UDbsevQnvneYxelg1R62PF/On9/SY5PyxEaWk8VFakQMU9A2O9
        BlWg4p0PNVq+xu24hLDsFrSdbUBPgCX3h5COLSZoXz8JF7110OSuhZrInei4cRNZZ5qgHdANw4gXHAHe
        afdgtCaOIv4QdnGfwSLmPqaF9sPgSA+099+E+t5OqPhdwNS9TVif2QO7sHaMpdSTdI7HCNuIVfwp/n02
        2jFaSsb5xI9KS05CZXkSVN2JBI9MjPXOhfLKbIxdewaafk0IS6vAq9evcbUkEV27zdHlZ4zWzdPRFLwG
        XW3n8ejpc9y50wPz/fWYEHwfE489xOLUp5i6LhmWYTdhTsBnhPfD8GgPdCkttAJuDILfdonSrQTzwjpg
        GXwFjhFXMGNfExRWncZwq2NfSCyI/nvbaEm7cF9Ft3gouMZAmVNBMlQ9UrlUUCI1qHjTOr6hEskl5/DZ
        Z4/w3fff4/mLF7jff5fGl/j6m2/x6tVrPHr0GKFpVZQundA/3IvZUfcxL6ACRrsbKeJ9XMR1Dt6G1v5u
        aOztwtjdHVDZfgVyK3PhFHkVOiR9Tb8GOIa1wvJQMzSoX5Byiad6EPL3/sBKakFkncKSOPAWxkBp0Qko
        kQpUllPr6pFGZKRB0Z3UQPUgvqgZt2/fxlsC/O7de87fvv0Gb778CvfuP8DNm90w3l4NnYAOTDrUDdeo
        dqh7pUM/8DYmH+7EwuMdWBvfBr/Ec9iV1ISAtLM4nNGCwJQahBe0YWVkPWwO1sA2sBEW+xswYVMxFFek
        Q8Qy6D6bp4jlUUFhfTdBOmT+15m0Q9Q7hcVxkHeJJhKioLwskUhIJPBEhHsqeG6UFpQO0Tl1ePzkMbq7
        b+Hx0yecGnp6+8h78ZKUkFTZCa1tZ6G9qw0OMZTbbjHwjb+IqPRSpGbmIDo2EREnE8jZ+N9+IikdSTmF
        yC6pQF5pJU5XVCM+vwrrIsugRDVppF0ExiwIM2NzHTZz90cC/hoiJO0i1GSdT0BhUSx4zlGQdwqH0tKE
        QRKWMiUkQ35RHJQ90hGUVEHR/hKv33yJp0+f4cXLl6SGb/DNt9/i67ffwnDzGYzb2ozpBy9j4uok5JQ1
        ICYumQ80CaHhEThyYAcO7fLB/q0rOD/g74HgfWsRE7IbiXFRyMjLR15FHfLKa1FQXo2EghrIusRghHVQ
        JJuvsOn/IOCfI2Hk3GA7ancp/09Bzikacg6hUFxC9WBJAhEQzxEht/AklImIDcE5+OLVK46AL0n2X331
        Nb7+6i2+oTSILW2H2roK6PvXIrayG4nFLei4eQshwYex1cMMa+ZKw2uOIFZbCsPHejg2zBuBFbPEMX+K
        ODwtJOE5ZxQ2zx+DAFcejq7UR8ze5cjITEXLpcuckkTMA6/zp0yQhf83Epj/cRtpc2S9HFMAgeQ5R0LO
        PgSKlA6KS2KhQIWRqUHWheoCjS4706joPaeV4A1eEQmviYQviQTmButpufQpRfP1fvz004/4ipTxkIpi
        57U2ZERvRYCXIXysRImA4XA0Hg4HIzHYG42Ak4kEFk4bhSWm0lhmJo211tRl+luh4NQeNDc1IiqzkuYV
        AVGroJ+Ha9mICSpP/wj0H8F/9D9mogbLI+SdjkOBSOA5RXEEsHRQJEUokvSZGmSc6XNSxEzfeDx4+JBT
        ASPh9ZvX+JKISCpvh7JXHmZuL8G3775FUVMH9mS04tbAI3z48Aud/5rO/xLvKFUe3u3GxaZy1JzJxJnc
        eBRnx6I0LwkVxdloaqjGlY5OXGi/jmaK/KXrnfCPPg2eYwhG2ARDYJSKgYCwmBglwu9/Z/jnCRiuYSk0
        3MC9UN4xBgpEAs+BKSBsUA0cAUQE+RinGMiTKsYtjcEtWgVefv4FPv/iFQfs9Zs3mLqBegWvHNjtPU1F
        8ik2xpSDt7oEnjHn8MsvH/DDjz/h199+w2/kv/76Kz788ive01L6iOpIz8B9dN3uweXOmzh3hYC3XkNz
        21VuZAQ470iGwoIgjGIEyE1YQtOWIpdg0yf/2Cr/ORKEZLWHixp5N8s5UPUnEniONDpEQIEkz5HgQiQQ
        EWPofTkaZZ2jcfXqVTx99hzPX37OEZFXdx2KtGRqrcqGgsNR9PUPIDrjDBQ886konsbPH36mvuEH/Pzz
        ByLjV3r9AT/+9BP33pNnL4mAB+ggAs63d6LhwmU0XmyjsQ1Nl67gIilhsns0FG2DIGUdDMGxs/1p2jzy
        MeSMCLZzHEb+0f4YASLqs4RFjddclLePIuAE3j4a8gRWwZEk70QkuLDUYEScAC2VGEXnlFbV4TPKbRY9
        tgqYb83m2mc5x3AqlseRVd2G6sazcN2fB/kVmXj8/BXXOH33/Y/46eefSQ0/4v133+MtpcODx09wo+cu
        rnR1o5EAV7dc4Lzm7EWOgJK6c9B2jYQiRX+0ZRARMCeYps2eGyiTs5/YsF+aSZKzTvH3avj/m4iBe/KY
        +UQAc/tIyBNIBS4l+EQ40zHVh1ELIoiEGESnFlHT8xAPqAcoa+ngUkORmqgx8w5R0UyE075cXL3Rjezi
        CszYmIaq1h68++47Wirf4fsffsS799/RkvkNV0Tv0nWu3byNC1c7ONBl9S0ob2hBVfN5joCTWRUY7xAO
        BYujkCYXVLeIpynrkGuSsx9ZKZDLkDMiRpKztPhjNlxrrqjIZO+y0fMJ/IJIIoF6gQUEnkaOCEYI1Qep
        +eGQouMNhxNxd2CASLgPpz35UHRLgPTcQ1BcSOdRF8lzPYHUkkY0nm9FVsFpXO+6QYXxPd7QSvHu/Xt8
        ReC/oAL65PlL3L57D63XuziwZfXNKKqqx5naJk4F7L3y2kZkFZZi29E0THELh4DqrFSasjE5+wuzNrkG
        OVPDx7RgKfHHFMBsmMp0cWEd50Qp6rjkiQgeRZulhQIRwhFALkuvJezCMW9dFG7dukO52sEebEKGVg7Z
        eUeIpCjou59AYFwRCssqUEVpwIA0nL/EyZ2B/pJ6hs9fvcHjZy/Q/+AzXO++g7OXr3HnFVbWIq+sGiUE
        mimg8eJlpOcWIS41C4mZecgmMoPCY+7r6Or60JRnkZuQG5AzIlTIWU34fT34YyaiMn2YkPL0DSNsjn0v
        S0TwOEVQQaTUYGpgyhg5LxTai0PR2nYZnkcKIe8aC2nLA3ReFOy2JFELW0sRbEQRgck/U47TbCyrwQsq
        lk+ocD55/gIPHj3BnbsD6Opmhe8aqprO4TTVlayScmQVl6GQOsDSuiYisIVa52SEnxhsnU9Su5yYkYus
        /KLfFi9xS6Ap25DPJNcnl2UQyP85E9GyERKU1TMUmbbxirQtqYAjIpxThQK51NxQIiEEOYW0zJHUpecF
        QX5eMGaviSe51qOY5FvOj3wljSVVtSgmcLdoi3zv4Wfo7b+HOz19XId4+XoHGs61oqS6nkiqQlpBCVLz
        i5F9pmKQOCIw7Hj8fxFwIjEdCem5REI20nLy4ODoxFpjlg6sKLIa8Mfz//8yYfmJokKqZr5ic/a/GM2I
        sA0Hzy6MG8Wsj0HbLQKjqTcYY3mYCAhDUl4ViqsbUMYKWNN51J0frOINF1pRRe+1E9ieu/24QanT1X0b
        7R1duHD5Kmqbz1G/X4XMolLaDBUgMTsfGQXF3IYolY4Z8Oi4FMSnZdEeoYirKcm0qTqekExBKHorLCys
        S9NlBCiRsxT4a/8RQ4hnMEpI3WKH6OyAZ1LzaJkjsBJWwZCcHwEpq0DwKCXkbUJQ10zRJumXk5xrKfos
        f1luX7zWiXOX25GVm0fgGfBOXGPgr1Cjc+ESKuubkFNcirTcQsSlZyM2LRspdJxVdAah0ScIbB7yKC3O
        kJJOl1dyBCRlZNIGKxG59D11dXUrmiarAVrk8uR/nQp+b0LyE0cIqcxwH2a0tkncMviDhG0YZKyCwKOU
        kCNCaprOopSqOJN+PQFrvnQV565epwp/g1vmoo+f4FRwse0KWgn8WVoh6in6pdV1SM8vQlJWHo4np+N4
        UhriM3KQkpOPwKAQroA2nqN0qmtAUWkZMomc+LR0RJ+iFpoI4fF47HcHbEVgSmBL4t9DwEcTVpo2TFBW
        V12QZ7RJ2GBltajFkbcjqEHZf7IY1UQCW8tZF9dC0b9Ey1vHrR5a6gZwgYCnZ2ShsqYWLZQe9aSUalJM
        cXkFkrNycSolA5FxSYiITeLkfTw2Do0t53CJimTj2fOoqKlDfskZpBMxsUmpOBGfiOS0jFs0pankRuRM
        Bawf+POrwB81YUUTYaHRGhLCozVNR6gY7TxTUfWs7uwFrp9vp86u63YvbvX1o3fgHgYePsIALXttFP3G
        5hbU1jdyZJRWVCGbFJCZm4+C0yWoqW9A6+UruN3bh86btGmi9KkjYsuqapBHn6dkZiM+ORWllTXfqKio
        rKBpzCCfSM5qAG2S/k02Rl5JTEpKavKJ2LjLbdc60E4bmxt3+rjo9w6wrvEp1/h8Tpsn1hCxZuhr7kHK
        O645Yq3yj7RhYt3hZ7SZYgWTNVDnW9tQ29iEYuorGElFpeVIzcwaUFJSYuBNyRl4RfI/1wD9lUYEsEmo
        z54zZ2dWTl5/951eLpJ99x5wT4ufUy/AALLnBN8S6O9/+IHbFLHN0YdffuFaZLa7ZM8Qbvf04ioVzOZz
        51FeVY0qUkxWQeEzewdHtvSZk08hZ4WPyZ7tA/694D+apKSkKM2EVeMJ48eP99q9Z09+U1PTgwcPHv72
        mEh4TM0Qa4rYQ1QWebY5Ys8LvqO9AttY9d+7R8vkLbRR7rdQMc3My3/it217mYGhoR9dkz0TnEzO9gGs
        8WF/Wh96P7kXFx8hJCIiwibHosN6dUMxMTGriRMnblrouih6tY9vfsDBQ3XBYeEXQiMi20MiIq8eC49o
        PXD4SJ3P+g0FLq6LYgwNJ/sTmbb0XfancyZzBpr1+2ytZ7k+dP/X4KMRCYLDyISEhBgZbOJs+8p6dgaG
        /Vh6PLkeOVvGWDvLXjPCPjY2LMof9/y/f/jxJ0xA4D8BY2FnSrQWVFMAAAAASUVORK5CYII=
</value>
  </data>
  <data name="resource.Image2" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAAGqRJREFUeF7V
        WwdUVde2pYiAgqCUS5WmNBUUsCAqVUGpomIBAQvYEbtiQ0E6WOi9F0F6L1ZQVEBRAcESu4maxERTTfLn
        X/tw/T/j/f/H+MlL3sM1xhr73HvPPWfPueZae+3DReCvsk2manIbZ6hZbpuj5bvXWvfg9lkaMbstxsXs
        NteM2W81LiTAUmvnpmlKbv6mKpP4X/n0bZWxyqTVxsoxG03VHhxxMESsxyykrLJAlo818tfZ4PQGa5Ss
        t0Lh2jnI8jJFynITRLlMxHYz1a99psgnrJksp8a/1Kdl680nqHkYKtbuttHDUWcjhC2aiuhlM3DKfRaS
        iYBMHxvk+tog39cK+T4WyFk1CxmepkgiAk4uMkSEoz6C7LSxzUz1Z3f90Zv5l/00zNNEfXbKoW3vi6MP
        IMLbFoGOk3HM1QSRbtMR52WBwj0rURm8EWf2r0ThVmcUbnFE/sb5SF1pijg3Y8QsNECIgy6OztPGAWtN
        7Jg9Fkt1pI7yLz/0zU5R8NxSreHw1JdAgr87CkO2I2zZbFRE7UPe3lUIXaCLgCni2DdJGIeNxXHQUBiB
        U0cibO5YRDnpI8RuPILpnMPzxiPAShM756hh43QluGqNtOffYmjbXHmB7mU6I7FUQxju44Zhv/1kFIXs
        wLZpctisNwx+ekLYaSCKHfqC2DdFDLsm0Ggkju16AtiqI4DN2gLYqCMIvymjsGvOWGwjBWw2VcZyPemn
        y/Wlhfm3GbpmpyDQ6KohgkVqQnAfPxzeeuJYqSEAX31RbNAfjg06wthEABlQBpi5v+7g603jBbBhHJ2r
        JYBVmgLw0hLCpmkK2EAKWG3Eg7PGCBf+bYauOakMi5mvJABnFQEsVhOEl/5IrFAXwBo9MawiYD4E0odG
        XwK6mohZQ0DX0/E6Bpw+W0ujN33uRe+z7y3XEMJaEwV4T5GHk8bIDP5thq5tMZByinfWhaeuBJyVBbBM
        SwRuYwXgoSUMDwLEQIXN00DpWlNc2mGN8/60LC7Whb+BBLyIEG8CvpIBV6Pv0vcWqdIxXcvDUA4O6iN6
        +bcZuhZvO3Z37xEn9Bx1hv80HpxICa4EYhGNO2co4up+R9wPX4KBY4twN8gFvYGOuL3PGjf8pyLKSglL
        CfRyIsCNCHClY6YkJ1VBLJskA0dNiQ/82wxdy3PVLugl8H3BC4mEhdg5UwmbjWWR4TEVj2NW4mHUCtwP
        W4r+0MXop3P6iKzuXbPRtXUqbvgZY9+00VzUXQk4U5ADpZOdIr3WkYa9liRsVcWk+bcamlawWO9G71GK
        bLAr+kMW416YGx5ELiNfgYfR7njACIhYioFQUsAxV/QcsMXNbTMI/DR0bjFGvZc+7AkwU449EWBHBNgo
        0LHmSMzXlICNsqgq/1ZD00rdDd8wAvqIgD4iYIAIuBe+jEAv50hg471QIoA+6ztsj+4dM0n+RAApoHOz
        Ea5tMMRKbTEu6rYE3IYnAAt5GseKwVptJKyVRIYuASXuhkINPtNJ+kTAUVcuwv2hlO9hSzgSBp1FfwmB
        X4BbO81wc8cM3Nw6HV1+JujYNAXt6w2wdYoUrAm4FRFgQeMsOQHMURwOSxVxWCoMk+PfbuhZ9kIduQtb
        ZqPniDN6gyi/g0nmwZTrBLg/1I3zu6SMOwHWHPhbLPpM/ltNOPlf2zgZrb6TOALMKeoMuKnsoM9SFIGF
        0nDM4YkI8W839CzLeZxG2zYL3Al05lQwSAIpgbw30IGqvRVJnoBvnzmY9xz4aejawqI/GVfWG+LS2onY
        PHkUTGWoMZrKQ6TTJNirDocZT4hIGfaMf6uhaRkOGgZtfma4scca3XuscHOXOUmcXvtPx03mBLh7+3QO
        OHvNIt+1xYgD3065f3HtJJxfrQ/fCZI4aiaHKg9dNPhZIctzOmbKEQE8wTr+rYampdmpTGrx1sUVkvFV
        AtRJOd1Fha3Tj5Y4Assq/aDcp3I537nJGNdZ3q8z5KR/fs0ENHnpIsRcHpXLtFCyWB2nl2ihfoc91QMh
        WMoL7uXfamhaog3PoGGlDkVyIlp9DIgEA1zfOIUiTCRsnsLlOSOFVXsG/NqGyWgj4Jd8JuHsqglo9tZD
        /crxqFo+DmVu6ihyHYuCRZqo8rPFbKoDDqrDx/FvNTQtzlJOrdxNCy2r9LhoXvKZgDYiguU2i3I7EcKk
        zl5fXmfARf3CWn2c9danyOug1n08qleMQ/lSTZxePBb5ziooXjkZkfY6MJcTbODfZuhavJWsWK6jCkVR
        hwPUvIrArZ6AC5TXTBUX1wz6BTpmBLXQ+yzqDZ7aqPXQRtWycSgl8Ez6hQtVkO2ohCIvE2qGBH9bpi35
        aTwrTLCW/4pJuJYiyYho9NQdJIOANpGzsdGTQFOuN3jqoI6AV6/QQgXlfCnJvphkn0/gcwh8rqsmfCdK
        wkFF5AD/8kPfYmaPuVTqpoGyZRpcLlcRuGqSNoswA1tDzsipofcqKeLlBLxsCUV9kRqKFqohz0UVOQ7K
        yLBXxGFTacyVF8zkX/rTsNDpoyJZ8SqmHD6zhIigqLKcLiNSWJSZly+l9wl0KX3OVXoCX8CiTjmfTcDT
        FyggZb4CNumJpfMv++lYsImEbbaDEvJdVDgpMzIYwGLmjBjyIjoudCVfOHYw4gy4oyIyCXyqnTzSndQR
        M0caeyeKWvAv++lY2AypYZFmUl9nOihyRSzbSZnyWQV5BDKXnI3csZMSshyVkUVkMbmnUcRT5skh0ZaH
        FCdNHDAUxV79Yev4l/207NBk8cgUW3mSMY+Tc8Z8kjWBZBFmI/d6AY8DnWzHQzKdm2gjizhrGaS66iDU
        VAoBBsOwS18kl3/JT8sCjUbIhM6QfJ9AoBJt5JA0j8a58hzQZBrZ6wSKdvxcGcTTOaesx+CExRgku2gj
        xloBBwyGI9BYFLt0hV5vt50qyr/sp2UBk0QPMVAnLEfjlJUMYplbDo4nyU9ZjMZJyzGIMR+NaPJEBt5G
        CfsNRBE2YySOmohhn4EQQta4Xl6+M7Zoolf8aP6lPw0LmCIhcdhI/Nuo2dKIpIIW/Q8eOUsaEWajEGUu
        gwRXPYTOlsUhAzFEzZREpKkER8CBKWKIjU+Bqnce5JemvJJZGLeAf/lPw7ZMGnMwxHQkKB0QSsBCmBO4
        kBnkppKIsVXFScfxOGQsgWNGI3GKyIo1J3LMJBFsIoq4bR4w31YAjfWV0PA9A1WvXMi4nEzV8kgW599i
        aNvUeb5z/Y3lccREHIHkR6aKI4jICLdSRIy9Fo7MlMFhQ3GcMhuNZEqPVBuqCZQy0WYSiLTTxK7oAqhv
        qoPW5lporiuDuk8xZu0qh4J9cJ+2Z8o0/m2GnvGWpQvKuZzcJOeR/9Z0TRICpoyg/b0MQiwVccxCAfuN
        JXGQgB+fLoVUSoHsuXLInS+PDFsqlpbSOD6H3j91EtqbqjBu+zmM29IIzY1VsDzYiBnrEuCX0wervZVQ
        dD1Zou+TN7T+hD7GNcFEdknKLU2KmqpvJfQO3cG87fmIWW6BXZPEKa8lEWVGQM3lkGUliyJaCkucFFHk
        oIBMOxkkEQFZgZvhfLAMOnuvYfyuVozzb4b10YuY7puIjdn92Fz8ObaWvIDryW7oemf8Iu9yIkLNM3sE
        fwr/HpNbFCco5Xg8VMUz5z801pdDaxMRsL4aeofvYElUG2pOHkaImTwtd4pIsVFAnq0iSqkZql6kigpX
        6hideMgmAjI2OSEstQLj9lyH3v4b0N59BeZB7TD3y4bHqWvwKfgcHpnPsCztEXaceYolSfdhsLMRkvMj
        v5ByPLGYP51/rY1xOj5S2jHmoqpnNtRWF1LBKoWaL+XsxjroHujG1uTLKDm0HglzVZBmq4ICe1VUumqg
        cbkmGpepo8pVmVRABKybi8qaekzefRYTgwagd/AWpgfdgOPhKiwIrIVX7kssTX8Kp8TPYHN8AGbhvfDN
        eQDXuD6o+p+H1MIEiNkcy5JemPive3Aq4xQjLGUf2a7qnoaxnllQW5UP9bXFUF1dBPXN9dDeex2Hsi6i
        Ono3Lq43QdVS2hUu18Z52hZf8NZG83J11CxSRvFWJzQ2NcMzpAKTwp9hYshDGAb1YcXJdpj552Jp5nO4
        JD+BbexDzI4egElILyYcugmtvR1wO3UDbsevQnvneYxelg1R62PF/On9/SY5PyxEaWk8VFakQMU9A2O9
        BlWg4p0PNVq+xu24hLDsFrSdbUBPgCX3h5COLSZoXz8JF7110OSuhZrInei4cRNZZ5qgHdANw4gXHAHe
        afdgtCaOIv4QdnGfwSLmPqaF9sPgSA+099+E+t5OqPhdwNS9TVif2QO7sHaMpdSTdI7HCNuIVfwp/n02
        2jFaSsb5xI9KS05CZXkSVN2JBI9MjPXOhfLKbIxdewaafk0IS6vAq9evcbUkEV27zdHlZ4zWzdPRFLwG
        XW3n8ejpc9y50wPz/fWYEHwfE489xOLUp5i6LhmWYTdhTsBnhPfD8GgPdCkttAJuDILfdonSrQTzwjpg
        GXwFjhFXMGNfExRWncZwq2NfSCyI/nvbaEm7cF9Ft3gouMZAmVNBMlQ9UrlUUCI1qHjTOr6hEskl5/DZ
        Z4/w3fff4/mLF7jff5fGl/j6m2/x6tVrPHr0GKFpVZQundA/3IvZUfcxL6ACRrsbKeJ9XMR1Dt6G1v5u
        aOztwtjdHVDZfgVyK3PhFHkVOiR9Tb8GOIa1wvJQMzSoX5Byiad6EPL3/sBKakFkncKSOPAWxkBp0Qko
        kQpUllPr6pFGZKRB0Z3UQPUgvqgZt2/fxlsC/O7de87fvv0Gb778CvfuP8DNm90w3l4NnYAOTDrUDdeo
        dqh7pUM/8DYmH+7EwuMdWBvfBr/Ec9iV1ISAtLM4nNGCwJQahBe0YWVkPWwO1sA2sBEW+xswYVMxFFek
        Q8Qy6D6bp4jlUUFhfTdBOmT+15m0Q9Q7hcVxkHeJJhKioLwskUhIJPBEhHsqeG6UFpQO0Tl1ePzkMbq7
        b+Hx0yecGnp6+8h78ZKUkFTZCa1tZ6G9qw0OMZTbbjHwjb+IqPRSpGbmIDo2EREnE8jZ+N9+IikdSTmF
        yC6pQF5pJU5XVCM+vwrrIsugRDVppF0ExiwIM2NzHTZz90cC/hoiJO0i1GSdT0BhUSx4zlGQdwqH0tKE
        QRKWMiUkQ35RHJQ90hGUVEHR/hKv33yJp0+f4cXLl6SGb/DNt9/i67ffwnDzGYzb2ozpBy9j4uok5JQ1
        ICYumQ80CaHhEThyYAcO7fLB/q0rOD/g74HgfWsRE7IbiXFRyMjLR15FHfLKa1FQXo2EghrIusRghHVQ
        JJuvsOn/IOCfI2Hk3GA7ancp/09Bzikacg6hUFxC9WBJAhEQzxEht/AklImIDcE5+OLVK46AL0n2X331
        Nb7+6i2+oTSILW2H2roK6PvXIrayG4nFLei4eQshwYex1cMMa+ZKw2uOIFZbCsPHejg2zBuBFbPEMX+K
        ODwtJOE5ZxQ2zx+DAFcejq7UR8ze5cjITEXLpcuckkTMA6/zp0yQhf83Epj/cRtpc2S9HFMAgeQ5R0LO
        PgSKlA6KS2KhQIWRqUHWheoCjS4706joPaeV4A1eEQmviYQviQTmButpufQpRfP1fvz004/4ipTxkIpi
        57U2ZERvRYCXIXysRImA4XA0Hg4HIzHYG42Ak4kEFk4bhSWm0lhmJo211tRl+luh4NQeNDc1IiqzkuYV
        AVGroJ+Ha9mICSpP/wj0H8F/9D9mogbLI+SdjkOBSOA5RXEEsHRQJEUokvSZGmSc6XNSxEzfeDx4+JBT
        ASPh9ZvX+JKISCpvh7JXHmZuL8G3775FUVMH9mS04tbAI3z48Aud/5rO/xLvKFUe3u3GxaZy1JzJxJnc
        eBRnx6I0LwkVxdloaqjGlY5OXGi/jmaK/KXrnfCPPg2eYwhG2ARDYJSKgYCwmBglwu9/Z/jnCRiuYSk0
        3MC9UN4xBgpEAs+BKSBsUA0cAUQE+RinGMiTKsYtjcEtWgVefv4FPv/iFQfs9Zs3mLqBegWvHNjtPU1F
        8ik2xpSDt7oEnjHn8MsvH/DDjz/h199+w2/kv/76Kz788ive01L6iOpIz8B9dN3uweXOmzh3hYC3XkNz
        21VuZAQ470iGwoIgjGIEyE1YQtOWIpdg0yf/2Cr/ORKEZLWHixp5N8s5UPUnEniONDpEQIEkz5HgQiQQ
        EWPofTkaZZ2jcfXqVTx99hzPX37OEZFXdx2KtGRqrcqGgsNR9PUPIDrjDBQ886konsbPH36mvuEH/Pzz
        ByLjV3r9AT/+9BP33pNnL4mAB+ggAs63d6LhwmU0XmyjsQ1Nl67gIilhsns0FG2DIGUdDMGxs/1p2jzy
        MeSMCLZzHEb+0f4YASLqs4RFjddclLePIuAE3j4a8gRWwZEk70QkuLDUYEScAC2VGEXnlFbV4TPKbRY9
        tgqYb83m2mc5x3AqlseRVd2G6sazcN2fB/kVmXj8/BXXOH33/Y/46eefSQ0/4v133+MtpcODx09wo+cu
        rnR1o5EAV7dc4Lzm7EWOgJK6c9B2jYQiRX+0ZRARMCeYps2eGyiTs5/YsF+aSZKzTvH3avj/m4iBe/KY
        +UQAc/tIyBNIBS4l+EQ40zHVh1ELIoiEGESnFlHT8xAPqAcoa+ngUkORmqgx8w5R0UyE075cXL3Rjezi
        CszYmIaq1h68++47Wirf4fsffsS799/RkvkNV0Tv0nWu3byNC1c7ONBl9S0ob2hBVfN5joCTWRUY7xAO
        BYujkCYXVLeIpynrkGuSsx9ZKZDLkDMiRpKztPhjNlxrrqjIZO+y0fMJ/IJIIoF6gQUEnkaOCEYI1Qep
        +eGQouMNhxNxd2CASLgPpz35UHRLgPTcQ1BcSOdRF8lzPYHUkkY0nm9FVsFpXO+6QYXxPd7QSvHu/Xt8
        ReC/oAL65PlL3L57D63XuziwZfXNKKqqx5naJk4F7L3y2kZkFZZi29E0THELh4DqrFSasjE5+wuzNrkG
        OVPDx7RgKfHHFMBsmMp0cWEd50Qp6rjkiQgeRZulhQIRwhFALkuvJezCMW9dFG7dukO52sEebEKGVg7Z
        eUeIpCjou59AYFwRCssqUEVpwIA0nL/EyZ2B/pJ6hs9fvcHjZy/Q/+AzXO++g7OXr3HnFVbWIq+sGiUE
        mimg8eJlpOcWIS41C4mZecgmMoPCY+7r6Or60JRnkZuQG5AzIlTIWU34fT34YyaiMn2YkPL0DSNsjn0v
        S0TwOEVQQaTUYGpgyhg5LxTai0PR2nYZnkcKIe8aC2nLA3ReFOy2JFELW0sRbEQRgck/U47TbCyrwQsq
        lk+ocD55/gIPHj3BnbsD6Opmhe8aqprO4TTVlayScmQVl6GQOsDSuiYisIVa52SEnxhsnU9Su5yYkYus
        /KLfFi9xS6Ap25DPJNcnl2UQyP85E9GyERKU1TMUmbbxirQtqYAjIpxThQK51NxQIiEEOYW0zJHUpecF
        QX5eMGaviSe51qOY5FvOj3wljSVVtSgmcLdoi3zv4Wfo7b+HOz19XId4+XoHGs61oqS6nkiqQlpBCVLz
        i5F9pmKQOCIw7Hj8fxFwIjEdCem5REI20nLy4ODoxFpjlg6sKLIa8Mfz//8yYfmJokKqZr5ic/a/GM2I
        sA0Hzy6MG8Wsj0HbLQKjqTcYY3mYCAhDUl4ViqsbUMYKWNN51J0frOINF1pRRe+1E9ieu/24QanT1X0b
        7R1duHD5Kmqbz1G/X4XMolLaDBUgMTsfGQXF3IYolY4Z8Oi4FMSnZdEeoYirKcm0qTqekExBKHorLCys
        S9NlBCiRsxT4a/8RQ4hnMEpI3WKH6OyAZ1LzaJkjsBJWwZCcHwEpq0DwKCXkbUJQ10zRJumXk5xrKfos
        f1luX7zWiXOX25GVm0fgGfBOXGPgr1Cjc+ESKuubkFNcirTcQsSlZyM2LRspdJxVdAah0ScIbB7yKC3O
        kJJOl1dyBCRlZNIGKxG59D11dXUrmiarAVrk8uR/nQp+b0LyE0cIqcxwH2a0tkncMviDhG0YZKyCwKOU
        kCNCaprOopSqOJN+PQFrvnQV565epwp/g1vmoo+f4FRwse0KWgn8WVoh6in6pdV1SM8vQlJWHo4np+N4
        UhriM3KQkpOPwKAQroA2nqN0qmtAUWkZMomc+LR0RJ+iFpoI4fF47HcHbEVgSmBL4t9DwEcTVpo2TFBW
        V12QZ7RJ2GBltajFkbcjqEHZf7IY1UQCW8tZF9dC0b9Ey1vHrR5a6gZwgYCnZ2ShsqYWLZQe9aSUalJM
        cXkFkrNycSolA5FxSYiITeLkfTw2Do0t53CJimTj2fOoqKlDfskZpBMxsUmpOBGfiOS0jFs0pankRuRM
        Bawf+POrwB81YUUTYaHRGhLCozVNR6gY7TxTUfWs7uwFrp9vp86u63YvbvX1o3fgHgYePsIALXttFP3G
        5hbU1jdyZJRWVCGbFJCZm4+C0yWoqW9A6+UruN3bh86btGmi9KkjYsuqapBHn6dkZiM+ORWllTXfqKio
        rKBpzCCfSM5qAG2S/k02Rl5JTEpKavKJ2LjLbdc60E4bmxt3+rjo9w6wrvEp1/h8Tpsn1hCxZuhr7kHK
        O645Yq3yj7RhYt3hZ7SZYgWTNVDnW9tQ29iEYuorGElFpeVIzcwaUFJSYuBNyRl4RfI/1wD9lUYEsEmo
        z54zZ2dWTl5/951eLpJ99x5wT4ufUy/AALLnBN8S6O9/+IHbFLHN0YdffuFaZLa7ZM8Qbvf04ioVzOZz
        51FeVY0qUkxWQeEzewdHtvSZk08hZ4WPyZ7tA/694D+apKSkKM2EVeMJ48eP99q9Z09+U1PTgwcPHv72
        mEh4TM0Qa4rYQ1QWebY5Ys8LvqO9AttY9d+7R8vkLbRR7rdQMc3My3/it217mYGhoR9dkz0TnEzO9gGs
        8WF/Wh96P7kXFx8hJCIiwibHosN6dUMxMTGriRMnblrouih6tY9vfsDBQ3XBYeEXQiMi20MiIq8eC49o
        PXD4SJ3P+g0FLq6LYgwNJ/sTmbb0XfancyZzBpr1+2ytZ7k+dP/X4KMRCYLDyISEhBgZbOJs+8p6dgaG
        /Vh6PLkeOVvGWDvLXjPCPjY2LMof9/y/f/jxJ0xA4D8BY2FnSrQWVFMAAAAASUVORK5CYII=
</value>
  </data>
  <data name="resource.Image3" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAABsVJREFUeF7l
        mltsFFUYgIn6woNKYrw+iCES4oMkRGyIYOUiavTBBK8PglEDEg3xocG+4S2oESIUSuUilAKuLC2lUNqS
        dtm29L7ddtvudtvtdu+zbfd+KSb4oL/n3+7B2dlDuzPD7uzqSb5kL3P++c+3Z86cOXsWyS23Lqz8/s8L
        z44TtLeqV72c/Pi/WTiOW0fYTSjlOE+pz9xUHun6dtZvqvs6MKw+fLPx7T/CPT/u9bpt5Hs8Zg6v16sI
        5NzbCQ8k05dXSKBdhL8JcCd8xjqItpUwv1MQq8fjeTDZDOmFBJkhwHyEW3dDsHsf8zuF+TTZDOmFERT8
        3fuBc44nTM8MVUOs7nXg3HbhL5B13O703Pi43e7SZDOkFxIEAyXAk0ZMaohf3QKzsQjEAw6I1RRD3GuA
        2dnZnLOvzQo2p+t2fkJcLpd8ASQIBkoQ9o5CVL0W4tNmiMfjEG3aBjHDcWZyuWBbrRlKGkbB6nDezlGA
        fAFOpxMSOGwQvrIFosZzc40f/AUijVvJ61jivRKggFfPDsEXV4xgtTvm8kxFvgCHwwGIV/sNhJt3Jk4c
        deshqFoDMb8jLalcQgVsrhqEXXXDYJm0J3LlIU8AV79jmd02AS5DPcycJQ0OchCL+MGv3gSR8XpmUrmE
        L2BTpR52XjTAmNUGdrudIl2At+KJe6ePLe2yW8eAq3kHnLrfIRaLQUBbSvgy8VpphAI2nNTBjuoBME9M
        gs1mQ+QIeHwPV7n63OTkJLiulYKrsQSC+hMwrXoJoiEfM6FcwxJQfLwHPjrfDyaLFSzWSWkC3Icfvc9T
        /pjXeemTJVarFaxWCzi0+8Cn2Q2RqTGIRqN5wbbaUaaAdUe74ENVH1zqNUkT4Dr0yGKCCV9PTEwAhZVE
        dGYS4iNVhMqcs/V87x0FvFDRARuOdvQXV7QvSTRKbHGWPayyV21612KxAIUlYPbaB3Dzt2cU4fNjv84r
        YE15OxQdatWt3t8kXgIRcL+jfGm9xWyE8fHxBJFIJI34pVdg9twKRWg5+ga8VnYFNh7RwvpyDRSXNcO6
        A02wdv9VWPPTZSj6oRae31sDRd+d1392svGhZNPElbGxMaCwBMRqN0PszHJFCJ9eDgMVRXC27H04c/A9
        UB18Cy4cfBMuajRwuXMAGroHobnXAK36EegyTuxJNklcMZvNQAmHw2lEasic4PTTihI89S+hSoLHCKFQ
        KCXPYCj0VbJJ4sro6ChQ+AEpoeqNEDq1LL/gTKxcpQkwmUxAYQSFoHo9BE8+lVeEPOm5kh4hTYDRaAQK
        dishAfUG8J9YmleEPKOsXKUJGBkZAQojKAR0R8B35jnwVa3KC/wNH0MwGGDlKk3A8PAwUBhBC4ZgMChN
        gMFgAAoJUshIEzA4OAgURtCCIRAISBMwMDAAFBKkkJEmQK/XA4URtJCQJqC/vx8ojKAFg9/vlyagr68P
        KCSIaHB9XrBOlwBXbVnH+3y+tPX9hWDFYSBNQG9vL1AYQRekq6sLWltbE2i12hSmp6fTjscFGHq8EGF9
        Cq4AC+MwkCagp6cHKIygCyJWAPYOrMOis7MzDfw8k15AepY0AfwEsHuKBZMUNpwyNTXFrJMlpAng22YE
        XZB8ETAzMyNfAAkiGr6A69evp+D1epl1soQyAoaGhm7fRfjodLrEGMCqwwfHnra2thT4AyO+x0GQVVeA
        MgLkguflXzbCXoTgwMmqy4fIli8Af7Fcg+MEXirzwarHoDAF3C2ISPkC8NfIBPxjkr+anG2wF7DyEJA7
        AThrZF2r2QJnj6w8BOROAK4g858isw1uh2HlwYf0EvkC+ANPAfL/FsBxnHwB/C1qYhA+vlJYx2YRZQR0
        dHSARqNJAwcvvHaFx+O/0PyJTybgJg5hHAbKCLhx4wZTAIKLIsLj8bbGOnY+8C4gjCOE9Dj5AoRdOBP4
        AlpaWlLAOTyrTnKLW0agRFYMBoUjIBuQy02+ALoFVQwLCWDVyRLKCOju7k4ZsOjsDR9lMxGAq9H8VSmE
        nxO+xy1xrLoClBEgF3zepz2I1YsQ3L7DqiugMAXgCE/3KN0JHAhZdQUUpoC7SO4E4AoNTk7EMrcpUxxY
        L+96AK7j8a/bTGFd35mAs0dWHgJyJwD/Scbbn1ja29slgb2AlYcAaQIwMbEC8hRpApqbm//6XwtQq9Um
        IiHR1RhBCwYyUEoToFKpXiQECJmOtnmJZAFYSOMXNzQ0rMxwypmXkAmTdAG06HS6eCH2ApwvkGeGkmQz
        pBfyMPMzDoasldl8Bf+HJA9eUcKTyWZIL0TAPYTthFOEygLhAGFFsgnJsmjRPwk9KEIEk5aLAAAAAElF
        TkSuQmCC
</value>
  </data>
  <data name="resource.Image4" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAABsVJREFUeF7l
        mltsFFUYgIn6woNKYrw+iCES4oMkRGyIYOUiavTBBK8PglEDEg3xocG+4S2oESIUSuUilAKuLC2lUNqS
        dtm29L7ddtvudtvtdu+zbfd+KSb4oL/n3+7B2dlDuzPD7uzqSb5kL3P++c+3Z86cOXsWyS23Lqz8/s8L
        z44TtLeqV72c/Pi/WTiOW0fYTSjlOE+pz9xUHun6dtZvqvs6MKw+fLPx7T/CPT/u9bpt5Hs8Zg6v16sI
        5NzbCQ8k05dXSKBdhL8JcCd8xjqItpUwv1MQq8fjeTDZDOmFBJkhwHyEW3dDsHsf8zuF+TTZDOmFERT8
        3fuBc44nTM8MVUOs7nXg3HbhL5B13O703Pi43e7SZDOkFxIEAyXAk0ZMaohf3QKzsQjEAw6I1RRD3GuA
        2dnZnLOvzQo2p+t2fkJcLpd8ASQIBkoQ9o5CVL0W4tNmiMfjEG3aBjHDcWZyuWBbrRlKGkbB6nDezlGA
        fAFOpxMSOGwQvrIFosZzc40f/AUijVvJ61jivRKggFfPDsEXV4xgtTvm8kxFvgCHwwGIV/sNhJt3Jk4c
        deshqFoDMb8jLalcQgVsrhqEXXXDYJm0J3LlIU8AV79jmd02AS5DPcycJQ0OchCL+MGv3gSR8XpmUrmE
        L2BTpR52XjTAmNUGdrudIl2At+KJe6ePLe2yW8eAq3kHnLrfIRaLQUBbSvgy8VpphAI2nNTBjuoBME9M
        gs1mQ+QIeHwPV7n63OTkJLiulYKrsQSC+hMwrXoJoiEfM6FcwxJQfLwHPjrfDyaLFSzWSWkC3Icfvc9T
        /pjXeemTJVarFaxWCzi0+8Cn2Q2RqTGIRqN5wbbaUaaAdUe74ENVH1zqNUkT4Dr0yGKCCV9PTEwAhZVE
        dGYS4iNVhMqcs/V87x0FvFDRARuOdvQXV7QvSTRKbHGWPayyV21612KxAIUlYPbaB3Dzt2cU4fNjv84r
        YE15OxQdatWt3t8kXgIRcL+jfGm9xWyE8fHxBJFIJI34pVdg9twKRWg5+ga8VnYFNh7RwvpyDRSXNcO6
        A02wdv9VWPPTZSj6oRae31sDRd+d1392svGhZNPElbGxMaCwBMRqN0PszHJFCJ9eDgMVRXC27H04c/A9
        UB18Cy4cfBMuajRwuXMAGroHobnXAK36EegyTuxJNklcMZvNQAmHw2lEasic4PTTihI89S+hSoLHCKFQ
        KCXPYCj0VbJJ4sro6ChQ+AEpoeqNEDq1LL/gTKxcpQkwmUxAYQSFoHo9BE8+lVeEPOm5kh4hTYDRaAQK
        dishAfUG8J9YmleEPKOsXKUJGBkZAQojKAR0R8B35jnwVa3KC/wNH0MwGGDlKk3A8PAwUBhBC4ZgMChN
        gMFgAAoJUshIEzA4OAgURtCCIRAISBMwMDAAFBKkkJEmQK/XA4URtJCQJqC/vx8ojKAFg9/vlyagr68P
        KCSIaHB9XrBOlwBXbVnH+3y+tPX9hWDFYSBNQG9vL1AYQRekq6sLWltbE2i12hSmp6fTjscFGHq8EGF9
        Cq4AC+MwkCagp6cHKIygCyJWAPYOrMOis7MzDfw8k15AepY0AfwEsHuKBZMUNpwyNTXFrJMlpAng22YE
        XZB8ETAzMyNfAAkiGr6A69evp+D1epl1soQyAoaGhm7fRfjodLrEGMCqwwfHnra2thT4AyO+x0GQVVeA
        MgLkguflXzbCXoTgwMmqy4fIli8Af7Fcg+MEXirzwarHoDAF3C2ISPkC8NfIBPxjkr+anG2wF7DyEJA7
        AThrZF2r2QJnj6w8BOROAK4g858isw1uh2HlwYf0EvkC+ANPAfL/FsBxnHwB/C1qYhA+vlJYx2YRZQR0
        dHSARqNJAwcvvHaFx+O/0PyJTybgJg5hHAbKCLhx4wZTAIKLIsLj8bbGOnY+8C4gjCOE9Dj5AoRdOBP4
        AlpaWlLAOTyrTnKLW0agRFYMBoUjIBuQy02+ALoFVQwLCWDVyRLKCOju7k4ZsOjsDR9lMxGAq9H8VSmE
        nxO+xy1xrLoClBEgF3zepz2I1YsQ3L7DqiugMAXgCE/3KN0JHAhZdQUUpoC7SO4E4AoNTk7EMrcpUxxY
        L+96AK7j8a/bTGFd35mAs0dWHgJyJwD/Scbbn1ja29slgb2AlYcAaQIwMbEC8hRpApqbm//6XwtQq9Um
        IiHR1RhBCwYyUEoToFKpXiQECJmOtnmJZAFYSOMXNzQ0rMxwypmXkAmTdAG06HS6eCH2ApwvkGeGkmQz
        pBfyMPMzDoasldl8Bf+HJA9eUcKTyWZIL0TAPYTthFOEygLhAGFFsgnJsmjRPwk9KEIEk5aLAAAAAElF
        TkSuQmCC
</value>
  </data>
  <data name="resource.Image5" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAABsVJREFUeF7l
        mltsFFUYgIn6woNKYrw+iCES4oMkRGyIYOUiavTBBK8PglEDEg3xocG+4S2oESIUSuUilAKuLC2lUNqS
        dtm29L7ddtvudtvtdu+zbfd+KSb4oL/n3+7B2dlDuzPD7uzqSb5kL3P++c+3Z86cOXsWyS23Lqz8/s8L
        z44TtLeqV72c/Pi/WTiOW0fYTSjlOE+pz9xUHun6dtZvqvs6MKw+fLPx7T/CPT/u9bpt5Hs8Zg6v16sI
        5NzbCQ8k05dXSKBdhL8JcCd8xjqItpUwv1MQq8fjeTDZDOmFBJkhwHyEW3dDsHsf8zuF+TTZDOmFERT8
        3fuBc44nTM8MVUOs7nXg3HbhL5B13O703Pi43e7SZDOkFxIEAyXAk0ZMaohf3QKzsQjEAw6I1RRD3GuA
        2dnZnLOvzQo2p+t2fkJcLpd8ASQIBkoQ9o5CVL0W4tNmiMfjEG3aBjHDcWZyuWBbrRlKGkbB6nDezlGA
        fAFOpxMSOGwQvrIFosZzc40f/AUijVvJ61jivRKggFfPDsEXV4xgtTvm8kxFvgCHwwGIV/sNhJt3Jk4c
        deshqFoDMb8jLalcQgVsrhqEXXXDYJm0J3LlIU8AV79jmd02AS5DPcycJQ0OchCL+MGv3gSR8XpmUrmE
        L2BTpR52XjTAmNUGdrudIl2At+KJe6ePLe2yW8eAq3kHnLrfIRaLQUBbSvgy8VpphAI2nNTBjuoBME9M
        gs1mQ+QIeHwPV7n63OTkJLiulYKrsQSC+hMwrXoJoiEfM6FcwxJQfLwHPjrfDyaLFSzWSWkC3Icfvc9T
        /pjXeemTJVarFaxWCzi0+8Cn2Q2RqTGIRqN5wbbaUaaAdUe74ENVH1zqNUkT4Dr0yGKCCV9PTEwAhZVE
        dGYS4iNVhMqcs/V87x0FvFDRARuOdvQXV7QvSTRKbHGWPayyV21612KxAIUlYPbaB3Dzt2cU4fNjv84r
        YE15OxQdatWt3t8kXgIRcL+jfGm9xWyE8fHxBJFIJI34pVdg9twKRWg5+ga8VnYFNh7RwvpyDRSXNcO6
        A02wdv9VWPPTZSj6oRae31sDRd+d1392svGhZNPElbGxMaCwBMRqN0PszHJFCJ9eDgMVRXC27H04c/A9
        UB18Cy4cfBMuajRwuXMAGroHobnXAK36EegyTuxJNklcMZvNQAmHw2lEasic4PTTihI89S+hSoLHCKFQ
        KCXPYCj0VbJJ4sro6ChQ+AEpoeqNEDq1LL/gTKxcpQkwmUxAYQSFoHo9BE8+lVeEPOm5kh4hTYDRaAQK
        dishAfUG8J9YmleEPKOsXKUJGBkZAQojKAR0R8B35jnwVa3KC/wNH0MwGGDlKk3A8PAwUBhBC4ZgMChN
        gMFgAAoJUshIEzA4OAgURtCCIRAISBMwMDAAFBKkkJEmQK/XA4URtJCQJqC/vx8ojKAFg9/vlyagr68P
        KCSIaHB9XrBOlwBXbVnH+3y+tPX9hWDFYSBNQG9vL1AYQRekq6sLWltbE2i12hSmp6fTjscFGHq8EGF9
        Cq4AC+MwkCagp6cHKIygCyJWAPYOrMOis7MzDfw8k15AepY0AfwEsHuKBZMUNpwyNTXFrJMlpAng22YE
        XZB8ETAzMyNfAAkiGr6A69evp+D1epl1soQyAoaGhm7fRfjodLrEGMCqwwfHnra2thT4AyO+x0GQVVeA
        MgLkguflXzbCXoTgwMmqy4fIli8Af7Fcg+MEXirzwarHoDAF3C2ISPkC8NfIBPxjkr+anG2wF7DyEJA7
        AThrZF2r2QJnj6w8BOROAK4g858isw1uh2HlwYf0EvkC+ANPAfL/FsBxnHwB/C1qYhA+vlJYx2YRZQR0
        dHSARqNJAwcvvHaFx+O/0PyJTybgJg5hHAbKCLhx4wZTAIKLIsLj8bbGOnY+8C4gjCOE9Dj5AoRdOBP4
        AlpaWlLAOTyrTnKLW0agRFYMBoUjIBuQy02+ALoFVQwLCWDVyRLKCOju7k4ZsOjsDR9lMxGAq9H8VSmE
        nxO+xy1xrLoClBEgF3zepz2I1YsQ3L7DqiugMAXgCE/3KN0JHAhZdQUUpoC7SO4E4AoNTk7EMrcpUxxY
        L+96AK7j8a/bTGFd35mAs0dWHgJyJwD/Scbbn1ja29slgb2AlYcAaQIwMbEC8hRpApqbm//6XwtQq9Um
        IiHR1RhBCwYyUEoToFKpXiQECJmOtnmJZAFYSOMXNzQ0rMxwypmXkAmTdAG06HS6eCH2ApwvkGeGkmQz
        pBfyMPMzDoasldl8Bf+HJA9eUcKTyWZIL0TAPYTthFOEygLhAGFFsgnJsmjRPwk9KEIEk5aLAAAAAElF
        TkSuQmCC
</value>
  </data>
</root>