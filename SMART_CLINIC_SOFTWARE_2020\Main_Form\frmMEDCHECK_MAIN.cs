﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.Main_Form
{
    public partial class frmMEDCHECK_MAIN : DevExpress.XtraEditors.XtraForm
    {
        public frmMEDCHECK_MAIN()
        {
            InitializeComponent();
        }

        private void btnDIG_LIST_ItemClick(object sender, TileItemEventArgs e)
        {
            LIST_FORM.frmMEDCHEK_LIST frmMEDCHEK_LIST = new LIST_FORM.frmMEDCHEK_LIST();
            frmMEDCHEK_LIST.Show();
        }

        private void btnEDIT_MEDCHEK_ItemClick(object sender, TileItemEventArgs e)
        {
            frmMAIN.GETNEWUSER.OPEN_FORM(((DevExpress.XtraEditors.TileItem)sender).Tag.ToString());
            this.Close();
        }

        private void btnMEDREQ_ItemClick(object sender, TileItemEventArgs e)
        {
            Classes.clsVISIT.VIS_ID = 0;
            Classes.clsCUST.CUST_ID = 0;
            frmMAIN.GETNEWUSER.OPEN_FORM(((DevExpress.XtraEditors.TileItem)sender).Tag.ToString());
            this.Close();
        }

        private void frmMEDCHECK_MAIN_Load(object sender, EventArgs e)
        {
            if (Classes.clsUSER_PER.PER_DT.Columns.Contains(tileDATA.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][tileDATA.Tag.ToString()]) == 0)
            {
                tileDATA.Visible = false;
            }
            else
            {
                tileDATA.Visible = true;
            }
            foreach (var item in tileLIST.Items.OfType<TileItem>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Name) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Name]) == 0)
                {
                    item.Visible = false;
                }
                else
                {
                    item.Visible = true;
                }
            }
        }
    }
}