﻿namespace SMART_CLINIC_SOFTWARE_2020.Main_Form
{
    partial class frmMEDCHECK_MAIN
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraEditors.TileItemElement tileItemElement1 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement2 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement3 = new DevExpress.XtraEditors.TileItemElement();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmMEDCHECK_MAIN));
            DevExpress.XtraEditors.TileItemFrame tileItemFrame1 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement4 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement5 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement6 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame2 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement7 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement8 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement9 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement10 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement11 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement12 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame3 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement13 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement14 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement15 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame4 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement16 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement17 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement18 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement19 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement20 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement21 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame5 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement22 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement23 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement24 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame6 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement25 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement26 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement27 = new DevExpress.XtraEditors.TileItemElement();
            this.tileControl1 = new DevExpress.XtraEditors.TileControl();
            this.tileLIST = new DevExpress.XtraEditors.TileGroup();
            this.قائمة_الفحوصات = new DevExpress.XtraEditors.TileItem();
            this.طلب_الفحوصات = new DevExpress.XtraEditors.TileItem();
            this.tileDATA = new DevExpress.XtraEditors.TileGroup();
            this.btnEDIT_MEDCHEK = new DevExpress.XtraEditors.TileItem();
            this.SuspendLayout();
            // 
            // tileControl1
            // 
            this.tileControl1.AllowItemHover = true;
            this.tileControl1.AllowSelectedItem = true;
            this.tileControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Style3D;
            this.tileControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tileControl1.Groups.Add(this.tileLIST);
            this.tileControl1.Groups.Add(this.tileDATA);
            this.tileControl1.IndentBetweenGroups = 10;
            this.tileControl1.Location = new System.Drawing.Point(0, 0);
            this.tileControl1.MaxId = 4;
            this.tileControl1.Name = "tileControl1";
            this.tileControl1.Size = new System.Drawing.Size(822, 380);
            this.tileControl1.TabIndex = 1;
            this.tileControl1.Text = "tileControl1";
            // 
            // tileLIST
            // 
            this.tileLIST.Items.Add(this.قائمة_الفحوصات);
            this.tileLIST.Items.Add(this.طلب_الفحوصات);
            this.tileLIST.Name = "tileLIST";
            // 
            // قائمة_الفحوصات
            // 
            tileItemElement1.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Hovered.Options.UseFont = true;
            tileItemElement1.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Normal.Options.UseFont = true;
            tileItemElement1.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Pressed.Options.UseFont = true;
            tileItemElement1.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Selected.Options.UseFont = true;
            tileItemElement1.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.MaxWidth = 160;
            tileItemElement1.Text = "قائمة_الفحوصات";
            tileItemElement1.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement1.TextLocation = new System.Drawing.Point(75, -25);
            tileItemElement2.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Hovered.Options.UseFont = true;
            tileItemElement2.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Normal.Options.UseFont = true;
            tileItemElement2.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Selected.Options.UseFont = true;
            tileItemElement2.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.MaxWidth = 160;
            tileItemElement2.Text = "";
            tileItemElement2.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement2.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement3.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image")));
            tileItemElement3.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement3.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement3.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement3.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement3.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement3.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            this.قائمة_الفحوصات.Elements.Add(tileItemElement1);
            this.قائمة_الفحوصات.Elements.Add(tileItemElement2);
            this.قائمة_الفحوصات.Elements.Add(tileItemElement3);
            tileItemFrame1.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame1.Appearance.BackColor = System.Drawing.Color.Green;
            tileItemFrame1.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame1.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame1.Appearance.Options.UseBackColor = true;
            tileItemFrame1.Appearance.Options.UseBorderColor = true;
            tileItemElement4.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Hovered.Options.UseFont = true;
            tileItemElement4.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Normal.Options.UseFont = true;
            tileItemElement4.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Pressed.Options.UseFont = true;
            tileItemElement4.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Selected.Options.UseFont = true;
            tileItemElement4.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.MaxWidth = 160;
            tileItemElement4.Text = "قائمة_الفحوصات";
            tileItemElement4.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement4.TextLocation = new System.Drawing.Point(75, -25);
            tileItemElement5.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Hovered.Options.UseFont = true;
            tileItemElement5.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Normal.Options.UseFont = true;
            tileItemElement5.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Selected.Options.UseFont = true;
            tileItemElement5.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.MaxWidth = 160;
            tileItemElement5.Text = "";
            tileItemElement5.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement5.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement6.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image1")));
            tileItemElement6.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement6.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement6.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement6.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement6.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement6.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame1.Elements.Add(tileItemElement4);
            tileItemFrame1.Elements.Add(tileItemElement5);
            tileItemFrame1.Elements.Add(tileItemElement6);
            tileItemFrame2.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame2.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame2.Appearance.BackColor2 = System.Drawing.Color.Green;
            tileItemFrame2.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame2.Appearance.Options.UseBackColor = true;
            tileItemFrame2.Appearance.Options.UseBorderColor = true;
            tileItemElement7.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Hovered.Options.UseFont = true;
            tileItemElement7.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Normal.Options.UseFont = true;
            tileItemElement7.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Pressed.Options.UseFont = true;
            tileItemElement7.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Selected.Options.UseFont = true;
            tileItemElement7.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.MaxWidth = 160;
            tileItemElement7.Text = "قائمة_الفحوصات";
            tileItemElement7.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement7.TextLocation = new System.Drawing.Point(75, -25);
            tileItemElement8.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Hovered.Options.UseFont = true;
            tileItemElement8.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Normal.Options.UseFont = true;
            tileItemElement8.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Selected.Options.UseFont = true;
            tileItemElement8.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.MaxWidth = 160;
            tileItemElement8.Text = "";
            tileItemElement8.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement8.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement9.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image2")));
            tileItemElement9.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement9.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement9.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement9.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement9.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement9.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame2.Elements.Add(tileItemElement7);
            tileItemFrame2.Elements.Add(tileItemElement8);
            tileItemFrame2.Elements.Add(tileItemElement9);
            this.قائمة_الفحوصات.Frames.Add(tileItemFrame1);
            this.قائمة_الفحوصات.Frames.Add(tileItemFrame2);
            this.قائمة_الفحوصات.Id = 0;
            this.قائمة_الفحوصات.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.قائمة_الفحوصات.Name = "قائمة_الفحوصات";
            this.قائمة_الفحوصات.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnDIG_LIST_ItemClick);
            // 
            // طلب_الفحوصات
            // 
            this.طلب_الفحوصات.CurrentFrameIndex = 1;
            tileItemElement10.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Hovered.Options.UseFont = true;
            tileItemElement10.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement10.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Normal.Options.UseFont = true;
            tileItemElement10.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement10.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Pressed.Options.UseFont = true;
            tileItemElement10.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Selected.Options.UseFont = true;
            tileItemElement10.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement10.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.MaxWidth = 160;
            tileItemElement10.Text = "طلب_الفحوصات";
            tileItemElement10.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement10.TextLocation = new System.Drawing.Point(75, 10);
            tileItemElement11.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement11.Appearance.Hovered.Options.UseFont = true;
            tileItemElement11.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement11.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement11.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement11.Appearance.Normal.Options.UseFont = true;
            tileItemElement11.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement11.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement11.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement11.Appearance.Selected.Options.UseFont = true;
            tileItemElement11.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement11.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement11.MaxWidth = 160;
            tileItemElement11.Text = "";
            tileItemElement11.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement11.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement12.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image3")));
            tileItemElement12.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement12.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement12.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement12.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.طلب_الفحوصات.Elements.Add(tileItemElement10);
            this.طلب_الفحوصات.Elements.Add(tileItemElement11);
            this.طلب_الفحوصات.Elements.Add(tileItemElement12);
            tileItemFrame3.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame3.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame3.Appearance.BackColor2 = System.Drawing.Color.Green;
            tileItemFrame3.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame3.Appearance.Options.UseBackColor = true;
            tileItemFrame3.Appearance.Options.UseBorderColor = true;
            tileItemElement13.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Hovered.Options.UseFont = true;
            tileItemElement13.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement13.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Normal.Options.UseFont = true;
            tileItemElement13.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement13.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Pressed.Options.UseFont = true;
            tileItemElement13.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Selected.Options.UseFont = true;
            tileItemElement13.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement13.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.MaxWidth = 160;
            tileItemElement13.Text = "طلب_الفحوصات";
            tileItemElement13.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement13.TextLocation = new System.Drawing.Point(75, 10);
            tileItemElement14.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement14.Appearance.Hovered.Options.UseFont = true;
            tileItemElement14.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement14.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement14.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement14.Appearance.Normal.Options.UseFont = true;
            tileItemElement14.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement14.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement14.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement14.Appearance.Selected.Options.UseFont = true;
            tileItemElement14.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement14.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement14.MaxWidth = 160;
            tileItemElement14.Text = "";
            tileItemElement14.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement14.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement15.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image4")));
            tileItemElement15.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement15.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement15.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement15.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame3.Elements.Add(tileItemElement13);
            tileItemFrame3.Elements.Add(tileItemElement14);
            tileItemFrame3.Elements.Add(tileItemElement15);
            tileItemFrame4.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame4.Appearance.BackColor = System.Drawing.Color.Green;
            tileItemFrame4.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame4.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame4.Appearance.Options.UseBackColor = true;
            tileItemFrame4.Appearance.Options.UseBorderColor = true;
            tileItemElement16.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Hovered.Options.UseFont = true;
            tileItemElement16.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement16.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Normal.Options.UseFont = true;
            tileItemElement16.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement16.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Pressed.Options.UseFont = true;
            tileItemElement16.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Selected.Options.UseFont = true;
            tileItemElement16.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement16.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.MaxWidth = 160;
            tileItemElement16.Text = "طلب_الفحوصات";
            tileItemElement16.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement16.TextLocation = new System.Drawing.Point(75, 10);
            tileItemElement17.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement17.Appearance.Hovered.Options.UseFont = true;
            tileItemElement17.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement17.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement17.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement17.Appearance.Normal.Options.UseFont = true;
            tileItemElement17.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement17.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement17.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement17.Appearance.Selected.Options.UseFont = true;
            tileItemElement17.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement17.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement17.MaxWidth = 160;
            tileItemElement17.Text = "";
            tileItemElement17.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement17.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement18.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image5")));
            tileItemElement18.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement18.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement18.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement18.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame4.Elements.Add(tileItemElement16);
            tileItemFrame4.Elements.Add(tileItemElement17);
            tileItemFrame4.Elements.Add(tileItemElement18);
            this.طلب_الفحوصات.Frames.Add(tileItemFrame3);
            this.طلب_الفحوصات.Frames.Add(tileItemFrame4);
            this.طلب_الفحوصات.Id = 3;
            this.طلب_الفحوصات.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.طلب_الفحوصات.Name = "طلب_الفحوصات";
            this.طلب_الفحوصات.Tag = "frmMEDREQ";
            this.طلب_الفحوصات.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnMEDREQ_ItemClick);
            // 
            // tileDATA
            // 
            this.tileDATA.Items.Add(this.btnEDIT_MEDCHEK);
            this.tileDATA.Name = "tileDATA";
            this.tileDATA.Tag = "بيانات_الفحوصات";
            // 
            // btnEDIT_MEDCHEK
            // 
            tileItemElement19.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement19.Appearance.Hovered.Options.UseFont = true;
            tileItemElement19.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement19.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement19.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement19.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement19.Appearance.Normal.Options.UseFont = true;
            tileItemElement19.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement19.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement19.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement19.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement19.Appearance.Pressed.Options.UseFont = true;
            tileItemElement19.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement19.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement19.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement19.Appearance.Selected.Options.UseFont = true;
            tileItemElement19.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement19.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement19.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement19.MaxWidth = 160;
            tileItemElement19.Text = "الفحوصات";
            tileItemElement19.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement19.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement20.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement20.Appearance.Hovered.Options.UseFont = true;
            tileItemElement20.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement20.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement20.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement20.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement20.Appearance.Normal.Options.UseFont = true;
            tileItemElement20.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement20.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement20.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement20.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement20.Appearance.Pressed.Options.UseFont = true;
            tileItemElement20.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement20.Appearance.Selected.Options.UseFont = true;
            tileItemElement20.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement20.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement20.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement20.MaxWidth = 160;
            tileItemElement20.Text = "أضافة_تعديل";
            tileItemElement20.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement20.TextLocation = new System.Drawing.Point(75, 5);
            tileItemElement21.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image6")));
            tileItemElement21.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement21.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement21.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement21.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.btnEDIT_MEDCHEK.Elements.Add(tileItemElement19);
            this.btnEDIT_MEDCHEK.Elements.Add(tileItemElement20);
            this.btnEDIT_MEDCHEK.Elements.Add(tileItemElement21);
            tileItemFrame5.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame5.Appearance.BackColor = System.Drawing.Color.Green;
            tileItemFrame5.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame5.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame5.Appearance.Options.UseBackColor = true;
            tileItemFrame5.Appearance.Options.UseBorderColor = true;
            tileItemElement22.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement22.Appearance.Hovered.Options.UseFont = true;
            tileItemElement22.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement22.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement22.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement22.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement22.Appearance.Normal.Options.UseFont = true;
            tileItemElement22.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement22.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement22.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement22.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement22.Appearance.Pressed.Options.UseFont = true;
            tileItemElement22.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement22.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement22.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement22.Appearance.Selected.Options.UseFont = true;
            tileItemElement22.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement22.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement22.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement22.MaxWidth = 160;
            tileItemElement22.Text = "الفحوصات";
            tileItemElement22.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement22.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement23.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement23.Appearance.Hovered.Options.UseFont = true;
            tileItemElement23.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement23.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement23.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement23.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement23.Appearance.Normal.Options.UseFont = true;
            tileItemElement23.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement23.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement23.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement23.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement23.Appearance.Pressed.Options.UseFont = true;
            tileItemElement23.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement23.Appearance.Selected.Options.UseFont = true;
            tileItemElement23.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement23.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement23.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement23.MaxWidth = 160;
            tileItemElement23.Text = "أضافة_تعديل";
            tileItemElement23.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement23.TextLocation = new System.Drawing.Point(75, 5);
            tileItemElement24.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image7")));
            tileItemElement24.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement24.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement24.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement24.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame5.Elements.Add(tileItemElement22);
            tileItemFrame5.Elements.Add(tileItemElement23);
            tileItemFrame5.Elements.Add(tileItemElement24);
            tileItemFrame6.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame6.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame6.Appearance.BackColor2 = System.Drawing.Color.Green;
            tileItemFrame6.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame6.Appearance.Options.UseBackColor = true;
            tileItemFrame6.Appearance.Options.UseBorderColor = true;
            tileItemElement25.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement25.Appearance.Hovered.Options.UseFont = true;
            tileItemElement25.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement25.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement25.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement25.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement25.Appearance.Normal.Options.UseFont = true;
            tileItemElement25.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement25.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement25.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement25.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement25.Appearance.Pressed.Options.UseFont = true;
            tileItemElement25.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement25.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement25.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement25.Appearance.Selected.Options.UseFont = true;
            tileItemElement25.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement25.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement25.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement25.MaxWidth = 160;
            tileItemElement25.Text = "الفحوصات";
            tileItemElement25.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement25.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement26.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement26.Appearance.Hovered.Options.UseFont = true;
            tileItemElement26.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement26.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement26.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement26.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement26.Appearance.Normal.Options.UseFont = true;
            tileItemElement26.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement26.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement26.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement26.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement26.Appearance.Pressed.Options.UseFont = true;
            tileItemElement26.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement26.Appearance.Selected.Options.UseFont = true;
            tileItemElement26.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement26.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement26.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement26.MaxWidth = 160;
            tileItemElement26.Text = "أضافة_تعديل";
            tileItemElement26.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement26.TextLocation = new System.Drawing.Point(75, 5);
            tileItemElement27.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image8")));
            tileItemElement27.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement27.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement27.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement27.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame6.Elements.Add(tileItemElement25);
            tileItemFrame6.Elements.Add(tileItemElement26);
            tileItemFrame6.Elements.Add(tileItemElement27);
            this.btnEDIT_MEDCHEK.Frames.Add(tileItemFrame5);
            this.btnEDIT_MEDCHEK.Frames.Add(tileItemFrame6);
            this.btnEDIT_MEDCHEK.Id = 2;
            this.btnEDIT_MEDCHEK.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnEDIT_MEDCHEK.Name = "btnEDIT_MEDCHEK";
            this.btnEDIT_MEDCHEK.Tag = "frmMEDCHEK";
            this.btnEDIT_MEDCHEK.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnEDIT_MEDCHEK_ItemClick);
            // 
            // frmMEDCHECK_MAIN
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(822, 380);
            this.Controls.Add(this.tileControl1);
            this.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.MaximizeBox = false;
            this.Name = "frmMEDCHECK_MAIN";
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "frmMEDCHECK_MAIN";
            this.Load += new System.EventHandler(this.frmMEDCHECK_MAIN_Load);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.TileControl tileControl1;
        private DevExpress.XtraEditors.TileGroup tileLIST;
        private DevExpress.XtraEditors.TileItem قائمة_الفحوصات;
        private DevExpress.XtraEditors.TileGroup tileDATA;
        private DevExpress.XtraEditors.TileItem btnEDIT_MEDCHEK;
        private DevExpress.XtraEditors.TileItem طلب_الفحوصات;
    }
}