﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.LIST_FORM;
using SMART_CLINIC_SOFTWARE_2020.MED_FORM;

namespace SMART_CLINIC_SOFTWARE_2020.Main_Form
{
    public partial class frmMEDCIN_MAIN : DevExpress.XtraEditors.XtraForm
    {
        public frmMEDCIN_MAIN()
        {
            InitializeComponent();
        }

        private void btnMEDCIN_ItemClick(object sender, TileItemEventArgs e)
        {
            frmMEDCIN_LIST medlist = new frmMEDCIN_LIST();
            medlist.ShowDialog();
        }

        private void btnEDIT_MED_ItemClick(object sender, TileItemEventArgs e)
        {
            frmMAIN.GETNEWUSER.OPEN_FORM(((DevExpress.XtraEditors.TileItem)sender).Tag.ToString());
            this.Close();
        }

        private void btnMED_LIST_ItemClick(object sender, TileItemEventArgs e)
        {
            frmOLD_MED_LIST oldmedlist = new frmOLD_MED_LIST();
            oldmedlist.ShowDialog();
        }

        private void btnMED_LIST_REQ_ItemClick(object sender, TileItemEventArgs e)
        {
            Classes.clsVISIT.VIS_ID = 0;
            Classes.clsCUST.CUST_ID = 0;
            frmMAIN.GETNEWUSER.OPEN_FORM(((DevExpress.XtraEditors.TileItem)sender).Tag.ToString());
            this.Close();
        }

        private void frmMEDCIN_MAIN_Load(object sender, EventArgs e)
        {
            foreach (var item in tileGroup1.Items.OfType<TileItem>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Name) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Name]) == 0)
                {
                    item.Visible = false;
                }
                else
                {
                    item.Visible = true;
                }
            }
            foreach (var item in tileGroup3.Items.OfType<TileItem>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Name) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Name]) == 0)
                {
                    item.Visible = false;
                }
                else
                {
                    item.Visible = true;
                }
            }
        }
    }
}