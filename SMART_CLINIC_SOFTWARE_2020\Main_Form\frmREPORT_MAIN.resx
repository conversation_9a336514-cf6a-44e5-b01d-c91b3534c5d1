﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="resource.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image1" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAACXFJREFUWEft
        V31Q0/cdLurmaruuLefUWu1Za72tL9fbtXOrtruttz9qe221Rfum9m5Wq1NQQFEwAkHRCgoBIiThnZCQ
        BALkPSEvhIQAeQFDeCcERAgJIALtytSTZ9/8/K2u43rtuvW/PXef+yW5/L7Pk+f7fD6/b+77P/4TaDXq
        +1UK+WatWhmtkNWIBeVlV8UVwqtqlUJs0OuiNWrlZn2d9n766/8dmptsYY1Wy0Zbo3U3ubJ1Wo2zWlp1
        i19WipzsLGRkXEJ2FosqVmYG2DnZEJTzIautuWXQ1znJ/Wx7S/PuJlvjRntLSxi97LfD6bA/0uZybm1r
        a00mN2tMRsOUSqlAhVCAfC4XnLxcVFVWoloqJVVFkVVKxNCoVaitqYa0qhI11VLqms/jUiUUlIO4BnO9
        aaqluUnjaXcnO1qatxrqdI/QtPegVGujzDYHujo7YLE0QEYWDS0gEYtQRYhCZA6HHWoiKlT6Oh2IOzAa
        9JQAtUqJkGCHvYW6p5KUqEJIlVxWCxP5nsVsgkBUhVwOL4qmvYd8vuT1NxJKkCpphK1jACOBCfj9o2h1
        uVCn01IkIVHEGZAt+bos5npYGszU65AYsgVQEEKdVk0JdBHRCp0Jn3NF2BbLxrPb43H89PnXadp7yC0R
        v/huUhliS8woM7mh6xiB59p1BKZmMPPlHG5MzyAQCGDQ50NvTzc6OzwglqLdfYWq0OvQZz1dnWhr74Da
        4kBhjQHxRTrszZTh4+RivHqAhd/sSEBU/NkXadp7yCmqeHzPOQEloNR4hRLgGppE53AQXf4b6ByZgq17
        GLY+P4yd11DX2gexpRMFGgd4MiuYpXoc46rxWaYUkZeVOJirRhxPhUiOlhKwixbw6u5EHIo78zhNew8l
        FVUPRWaK56OLTCjSt0HRNoTmgSA8g2Po9k+ja3QKzX0jMHX7UeMaRLnZg7RaJ+L5Zpwq0mJ/thKfkjqY
        I8NRrhZ/zdUgNleGfSxCfl6CnafysWV/Bt7cz5yPTz7/EE17DyKpbJlAppupMZE9s7mhcQ8RBybQRRzo
        D86id2warT4/Gr1BqD3XUNnUjRxtO1Iqm3FGaMKRAj0i84kLBTrElZgQW2xCfKHmrgMXpTiWXghGWh5O
        p2bMJJ1NW0bTfhMxWeLe46VmqO3d6LsWwNDYBHqH/fBNzMIbnEH7UAD2wQkYukchc/SBZ+pCusyFtCor
        TvIbcIJUIt8IhtBK1QWBFhyxEmx+NaLTS/GHgyy8c4DZS9MtRFyOxBzKQLH+bgZCIRwjIRyfmoZ/8gYl
        qnWAZMAzBAURWWLuQpayFZlSC5jCBlystqFQaYVIa0WFQg+uRIM44kgoAx8mFVEZ2H6QWU/TLUQSp1JM
        CTC2Q+7yweYN4AqxvYPsfyepvpFx9I9Nod8/iZ6rfji7B2F196LB1YlacyvyZRZkCLWIIiH8NEuByCwp
        /nKpGrvOibCDUYRXiICdh5LFNN1CVMg0kkKFhTjQhhp7H1p8JITE9h6y/x2jN+D0jsLc64eMBLTC0oGL
        8lacEliQWFKHA5fV2M9WIZoT6gANDnN0iMmT47NsOXadFeL9BA4OMDKQkHJBQtMtxOdF0kSmyAKbxwuP
        d5iEbowKoXd8Fj2BGeLGGJpIZ2jJ9kibe3C5zoOz0hakVtQjpsiIo4VGJBAxMcX1CGUpnnRHHE+JCzwh
        krNK8MYRFvYcTWHQdAvxeYFkf2gLCki46j0+DE+QvZ+4joGxSfQFpokbY3CSzjCSVpQ7+1BQ342Lilak
        VzUiodyCeFJMgYlyJV1iQrFEDla5HFEsCd5N5FMZ2BWVvI+mW4hcvjQitqQeHL0HKmc/rH1jaCdzwH/j
        SwSvT2M0SISMBOHsH0G9ux+Vtm4UG9wo1dmRr24BX9eMWr0VQoUBaXw1orKq8EkaqdRyvJ1wdxAdPM7c
        TtMthEZXt0NMEszRtEJJfmFoELnpQeQhkzBE7BoMwu71o7lrkEzDftRYPRDp7WBVmXGa2B8b2ne2mhpE
        R9m1ZBDV4pOzfGyPy0VMUjpSz53fQdMtREO9cYu7zQWfbwDu/qtw+gLUKO4nM6D7n4OoPwBV+zAkti5k
        a9xgSprALDfiCBlCh3l6HC/QkqFkoDIRl6/GkTwlUi+XgsfJRW4OCeGJY6/QdAuhUil/daXViSEi4Prk
        JGZmv8CQn4QwOI0+ahCNwe4bh75rlHRJL7jGTlwg4/i8mAyhMjMVvNNlRpwkWThVbkZOeS2E5DzBLy5A
        fh4bbNZFbHx6w6ZFixb9lNB985Dyx7d2LDl0Imlvm8tBnnheImACc3Nf4c6dO/jbV19hZGIKHb4ROIgr
        5t4xKEhGiht6wFJdoQZQYoUNDEEDMiuNKJYZIVcoUCuVQCQoQ0khF9zL2cjOSCMCnk4LCwt7gVCGk1pC
        kT/44INhm17eEvlxZMJ86NnuG/Bi8l8EzM/PU9fbt29jdnYW/cOj6PGRNu0hw8rdA4vDDXWDHS6nA802
        Czl86KBWyFBdKUIFvwTF+RzksVk4l5KEqMhD8xs3Ps0itI99LSCEFStXLHnmmV9vZzKTBptsVgSDAcyR
        X/4NAbdu4e9zc1C4vCQDd0fxGTF5FAuMiOZpyeHFAZu1AUa9Fip5DaSSCghKi8DLywntPT76cKd/1aqV
        ny1evDh0JFt0l/nfsHr16kffeuvNTJVSfnN8PLhAwBwRoGwbuNuC5k6kiBuRQgTEkpnf6rSj0VIPQ50G
        Slk1qkQC5LAuIebo4dubf/9S8QMPLFu3dOnSxTTVt+Oxx1Yt2rBhw+8STzPsXm8/bt68SQm4RQuQtXSh
        TNcEdqUWDK4ESQW1iOXIyfGrBdaGeuh1akiJ/edTUxDx3rb2NWtW//nhh3/xE3r57481ax7/2Wuv/Sla
        JquZniRd8cUXs5QAocqAoloduDV6MDginGbzEZPGg9PeDGOdliKPP3H8y+efezYxPDz85/RyPwyrVq0K
        W09w5EhkNXFjPiQkKSMPMSnpOMxIxc4Dsfjg4DFE7IumTr3lJHTbtr1tWLlyxXPh4Y9+9/+B74snnli7
        ZNNvX3pPIOBfZZVJkZzLJ6ccLnZEJeGj6GR8EHkKDEbC+IYN6/f+cvnyUJ//OHhy3brwiIiI7Evc4lvH
        L/Lw/lEm9hyKvbN5y2b+ihUrFh42fwwQNxa98MLzL59kMBzvvBfRu3btmq3Lly//7nT/r/HUU+uXrF//
        5FL67Q/Afff9AypXrk0uUxXzAAAAAElFTkSuQmCC
</value>
  </data>
</root>