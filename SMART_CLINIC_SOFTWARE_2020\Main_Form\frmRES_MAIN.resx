﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="resource.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABuBJREFUWEet
        lwtUk2UYx0W2scG4bXMwcGOMyYTBkHHzkqLGuJScBDEVFXVmSqAnRKwILSnRrFOg6VG8QiJqaqIYXhBv
        pUjZRc9JSlP0dLU8oeJd/Pe8L192SkuL/c/5ne/bd/Zc3/f99qzLwxQfH2/JyMh4ZcKECdsmTZr0SVZW
        VmN6evrGAQMGTImMjJQLX3O8IiIiwhISEg5QUOTn56OwsBBFRUUoKChAdnY2xowZA5vNdjEkJCRFMHGc
        yOnIpKSkWyNHjoTdbkdOTg7y8vI4ubm5mDhxIkaMGIEhQ4agX79+dwwGg0Uw7bz8/Pzi+/fvj8TERKSm
        pmJoWhqo5UgjMoYPx2iqPDMzE2n0nJJEnz59YDQaqwXzzsvQXeM8KqH3osLssZg97RksKHweq958FTvX
        lOLwhndRNX8mhqWmICUlBX379oVOp2O0CuaO0Q/byubi1EHg20bg3GfA+S+As0eBbw4Ax7fjl/pymHoE
        gboFlUoFjUYDT09PrWDeebU2rNyKr/dTAkco8DGghZI4Qwmc3At8tgX4dD0mDn0cCqUS7u7u8PLyYtc+
        gnnn1dqw6hiaG4BThzq6wGjeB3yxDTi2CWiqwvRRKZDJXCGRSOgqg6ur60DBvPO6crDyLE7u6QjKEjnx
        IfD5Vqp+M/BJNXC0EmOT+2JovBWheg1cXFwYcYJ559W6o/QnfLqBgq2ndm/8k6Z1uHukAncPLUfFS+OA
        vWWoL5sBkUgMsVjsJZh3Xr99UPL93UMr0H54DdBYSbyH9iOVuPvxarQfLMedhsW4vftt3NoxHzXzcuDs
        7LxXMHWMLr7/6pd3Gt5F+/6lRDkP2n5gGe7sW4Lbexbi1q63cXPHPNysLUFcqP6azEUSKpg6RhfWvrDx
        1s63cHP3OxSQUYbbu0o7AtctwA0KfKNmDkrGDv4lQO0VL5g5TueX5eTc2PYarlMgXim1+nrtPNzYPhfX
        a4pxbfMsXNk0Gw0vpw0TTByr06V2Veu6gpss0NUts3GVrtfoem1zEdo2vsifXVg5FfXzJ+9f9EaxRDBz
        rFoW2Re0Vc9A2/qCe1ypmo629wvxc8V0XFhqx6EtazA2a9wGXx9fZ8HMcarOTfb6dfmzbZcrctBBLrV9
        Fs4tmYwzJeloqXoFi5cshdZfC4W34oRcLjcLpo6ROTioaM7oxy9fWp2NS1V5OF+eiy9fz8CZBU/j++VT
        sXdnLcJCw2AINMBP48e40sPYY6ZerxcLLv6f1N3UcnK2JtoaDVtiMo58UIHaGSk4XJyBlrJxaF49C6e/
        Oo5EWxLCzeEwGozw9/NHUFAQRmeOhinYdM7D3WOo4O6/iRxp9Dr9KVZZlDUK9gl2fLRlK6ptsajLGoya
        0hL8dOFXTJs6DZERkbCEWRAdFY242DjExMQgzBxGiSVC4+vLXk5LBbePLn2Avj4iPAI6rQ69LL0wZUo2
        agpeQH2gCZVqf8y12VC+YiUie1kRFRnFO8CWwBpp5Z2g7kGr1fJBxc3NDeTymQ7PjyClUhkc2jMUBr0B
        5hAzWCLFc4pxsrERdRod1potaGlupkpjeXDWgRBTCMyhZqpYw5eBNiMs4RYYg4ygvcAS+I54tBNCR+m5
        nsE9QV3gsGQKXy5CU/5MfGwKx+7xdpQtXAQrVc+CMwYNHIQAXQB03XVQKpR8Kbw8+WwAk8kEJycnlkQq
        4UR0Fa4Plo/aZwlrYyAFZxWxKlJo3jtgDEOtNggLw8IRG9cbEZYI9I7tjeSkZL5ULAF2ClhQth9kUhlb
        f94BNiuQ68UdER6ShLfCu45XQ04pGVpLHYIDA7FQqcabChWeoGdm2pwxUTEYGD8QAdoA0Ia913pmy7og
        EfOg8PHx4YMK3R8l2E+1iPjnJDw9PI+zSrr7d+cdUClV8KXPw93lyPWQI4gCBPcIhtVq7aicEmDB2cZz
        c3XjcyEbTLp27coTUCgUoJcTu/+RYL+W/oQb8eAE5G7ypm6qbvc2FGupgiqKpgRsnh40gPrzvfH3JFn1
        rNVs17PWkysODancB91fJxKIWCKYUBP3v6xEziI9ZXyKTgOvig2arIXeMjf4kDNvL28ejDYr1Co1DaQK
        /h1WpUgkgpimInJzDxbcw8OD3d8mxhBPEWxu7ElIiftF7fOmajaxncyC0ZjFnctcXXmbWXKstSwwve34
        Mxexy18q/wMWXEjgKpFPjCdiCBnx76JEnpRKpcdp0uXO2Lryiigoq5g9l0qkEIlFcOrCj9p9kD07hqz6
        XUQmwf43PPgE/ItsxHrqwiWpi5RvMolIwisWzvg/wf4pscDjCB3R6Z9rF+IxIo9YTtQRTcQxgh2zfcQG
        4nUindATzOYh6tLldz24vdk9vVWyAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="resource.Image1" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABuBJREFUWEet
        lwtUk2UYx0W2scG4bXMwcGOMyYTBkHHzkqLGuJScBDEVFXVmSqAnRKwILSnRrFOg6VG8QiJqaqIYXhBv
        pUjZRc9JSlP0dLU8oeJd/Pe8L192SkuL/c/5ne/bd/Zc3/f99qzLwxQfH2/JyMh4ZcKECdsmTZr0SVZW
        VmN6evrGAQMGTImMjJQLX3O8IiIiwhISEg5QUOTn56OwsBBFRUUoKChAdnY2xowZA5vNdjEkJCRFMHGc
        yOnIpKSkWyNHjoTdbkdOTg7y8vI4ubm5mDhxIkaMGIEhQ4agX79+dwwGg0Uw7bz8/Pzi+/fvj8TERKSm
        pmJoWhqo5UgjMoYPx2iqPDMzE2n0nJJEnz59YDQaqwXzzsvQXeM8KqH3osLssZg97RksKHweq958FTvX
        lOLwhndRNX8mhqWmICUlBX379oVOp2O0CuaO0Q/byubi1EHg20bg3GfA+S+As0eBbw4Ax7fjl/pymHoE
        gboFlUoFjUYDT09PrWDeebU2rNyKr/dTAkco8DGghZI4Qwmc3At8tgX4dD0mDn0cCqUS7u7u8PLyYtc+
        gnnn1dqw6hiaG4BThzq6wGjeB3yxDTi2CWiqwvRRKZDJXCGRSOgqg6ur60DBvPO6crDyLE7u6QjKEjnx
        IfD5Vqp+M/BJNXC0EmOT+2JovBWheg1cXFwYcYJ559W6o/QnfLqBgq2ndm/8k6Z1uHukAncPLUfFS+OA
        vWWoL5sBkUgMsVjsJZh3Xr99UPL93UMr0H54DdBYSbyH9iOVuPvxarQfLMedhsW4vftt3NoxHzXzcuDs
        7LxXMHWMLr7/6pd3Gt5F+/6lRDkP2n5gGe7sW4Lbexbi1q63cXPHPNysLUFcqP6azEUSKpg6RhfWvrDx
        1s63cHP3OxSQUYbbu0o7AtctwA0KfKNmDkrGDv4lQO0VL5g5TueX5eTc2PYarlMgXim1+nrtPNzYPhfX
        a4pxbfMsXNk0Gw0vpw0TTByr06V2Veu6gpss0NUts3GVrtfoem1zEdo2vsifXVg5FfXzJ+9f9EaxRDBz
        rFoW2Re0Vc9A2/qCe1ypmo629wvxc8V0XFhqx6EtazA2a9wGXx9fZ8HMcarOTfb6dfmzbZcrctBBLrV9
        Fs4tmYwzJeloqXoFi5cshdZfC4W34oRcLjcLpo6ROTioaM7oxy9fWp2NS1V5OF+eiy9fz8CZBU/j++VT
        sXdnLcJCw2AINMBP48e40sPYY6ZerxcLLv6f1N3UcnK2JtoaDVtiMo58UIHaGSk4XJyBlrJxaF49C6e/
        Oo5EWxLCzeEwGozw9/NHUFAQRmeOhinYdM7D3WOo4O6/iRxp9Dr9KVZZlDUK9gl2fLRlK6ptsajLGoya
        0hL8dOFXTJs6DZERkbCEWRAdFY242DjExMQgzBxGiSVC4+vLXk5LBbePLn2Avj4iPAI6rQ69LL0wZUo2
        agpeQH2gCZVqf8y12VC+YiUie1kRFRnFO8CWwBpp5Z2g7kGr1fJBxc3NDeTymQ7PjyClUhkc2jMUBr0B
        5hAzWCLFc4pxsrERdRod1potaGlupkpjeXDWgRBTCMyhZqpYw5eBNiMs4RYYg4ygvcAS+I54tBNCR+m5
        nsE9QV3gsGQKXy5CU/5MfGwKx+7xdpQtXAQrVc+CMwYNHIQAXQB03XVQKpR8Kbw8+WwAk8kEJycnlkQq
        4UR0Fa4Plo/aZwlrYyAFZxWxKlJo3jtgDEOtNggLw8IRG9cbEZYI9I7tjeSkZL5ULAF2ClhQth9kUhlb
        f94BNiuQ68UdER6ShLfCu45XQ04pGVpLHYIDA7FQqcabChWeoGdm2pwxUTEYGD8QAdoA0Ia913pmy7og
        EfOg8PHx4YMK3R8l2E+1iPjnJDw9PI+zSrr7d+cdUClV8KXPw93lyPWQI4gCBPcIhtVq7aicEmDB2cZz
        c3XjcyEbTLp27coTUCgUoJcTu/+RYL+W/oQb8eAE5G7ypm6qbvc2FGupgiqKpgRsnh40gPrzvfH3JFn1
        rNVs17PWkysODancB91fJxKIWCKYUBP3v6xEziI9ZXyKTgOvig2arIXeMjf4kDNvL28ejDYr1Co1DaQK
        /h1WpUgkgpimInJzDxbcw8OD3d8mxhBPEWxu7ElIiftF7fOmajaxncyC0ZjFnctcXXmbWXKstSwwve34
        Mxexy18q/wMWXEjgKpFPjCdiCBnx76JEnpRKpcdp0uXO2Lryiigoq5g9l0qkEIlFcOrCj9p9kD07hqz6
        XUQmwf43PPgE/ItsxHrqwiWpi5RvMolIwisWzvg/wf4pscDjCB3R6Z9rF+IxIo9YTtQRTcQxgh2zfcQG
        4nUindATzOYh6tLldz24vdk9vVWyAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="resource.Image2" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABuBJREFUWEet
        lwtUk2UYx0W2scG4bXMwcGOMyYTBkHHzkqLGuJScBDEVFXVmSqAnRKwILSnRrFOg6VG8QiJqaqIYXhBv
        pUjZRc9JSlP0dLU8oeJd/Pe8L192SkuL/c/5ne/bd/Zc3/f99qzLwxQfH2/JyMh4ZcKECdsmTZr0SVZW
        VmN6evrGAQMGTImMjJQLX3O8IiIiwhISEg5QUOTn56OwsBBFRUUoKChAdnY2xowZA5vNdjEkJCRFMHGc
        yOnIpKSkWyNHjoTdbkdOTg7y8vI4ubm5mDhxIkaMGIEhQ4agX79+dwwGg0Uw7bz8/Pzi+/fvj8TERKSm
        pmJoWhqo5UgjMoYPx2iqPDMzE2n0nJJEnz59YDQaqwXzzsvQXeM8KqH3osLssZg97RksKHweq958FTvX
        lOLwhndRNX8mhqWmICUlBX379oVOp2O0CuaO0Q/byubi1EHg20bg3GfA+S+As0eBbw4Ax7fjl/pymHoE
        gboFlUoFjUYDT09PrWDeebU2rNyKr/dTAkco8DGghZI4Qwmc3At8tgX4dD0mDn0cCqUS7u7u8PLyYtc+
        gnnn1dqw6hiaG4BThzq6wGjeB3yxDTi2CWiqwvRRKZDJXCGRSOgqg6ur60DBvPO6crDyLE7u6QjKEjnx
        IfD5Vqp+M/BJNXC0EmOT+2JovBWheg1cXFwYcYJ559W6o/QnfLqBgq2ndm/8k6Z1uHukAncPLUfFS+OA
        vWWoL5sBkUgMsVjsJZh3Xr99UPL93UMr0H54DdBYSbyH9iOVuPvxarQfLMedhsW4vftt3NoxHzXzcuDs
        7LxXMHWMLr7/6pd3Gt5F+/6lRDkP2n5gGe7sW4Lbexbi1q63cXPHPNysLUFcqP6azEUSKpg6RhfWvrDx
        1s63cHP3OxSQUYbbu0o7AtctwA0KfKNmDkrGDv4lQO0VL5g5TueX5eTc2PYarlMgXim1+nrtPNzYPhfX
        a4pxbfMsXNk0Gw0vpw0TTByr06V2Veu6gpss0NUts3GVrtfoem1zEdo2vsifXVg5FfXzJ+9f9EaxRDBz
        rFoW2Re0Vc9A2/qCe1ypmo629wvxc8V0XFhqx6EtazA2a9wGXx9fZ8HMcarOTfb6dfmzbZcrctBBLrV9
        Fs4tmYwzJeloqXoFi5cshdZfC4W34oRcLjcLpo6ROTioaM7oxy9fWp2NS1V5OF+eiy9fz8CZBU/j++VT
        sXdnLcJCw2AINMBP48e40sPYY6ZerxcLLv6f1N3UcnK2JtoaDVtiMo58UIHaGSk4XJyBlrJxaF49C6e/
        Oo5EWxLCzeEwGozw9/NHUFAQRmeOhinYdM7D3WOo4O6/iRxp9Dr9KVZZlDUK9gl2fLRlK6ptsajLGoya
        0hL8dOFXTJs6DZERkbCEWRAdFY242DjExMQgzBxGiSVC4+vLXk5LBbePLn2Avj4iPAI6rQ69LL0wZUo2
        agpeQH2gCZVqf8y12VC+YiUie1kRFRnFO8CWwBpp5Z2g7kGr1fJBxc3NDeTymQ7PjyClUhkc2jMUBr0B
        5hAzWCLFc4pxsrERdRod1potaGlupkpjeXDWgRBTCMyhZqpYw5eBNiMs4RYYg4ygvcAS+I54tBNCR+m5
        nsE9QV3gsGQKXy5CU/5MfGwKx+7xdpQtXAQrVc+CMwYNHIQAXQB03XVQKpR8Kbw8+WwAk8kEJycnlkQq
        4UR0Fa4Plo/aZwlrYyAFZxWxKlJo3jtgDEOtNggLw8IRG9cbEZYI9I7tjeSkZL5ULAF2ClhQth9kUhlb
        f94BNiuQ68UdER6ShLfCu45XQ04pGVpLHYIDA7FQqcabChWeoGdm2pwxUTEYGD8QAdoA0Ia913pmy7og
        EfOg8PHx4YMK3R8l2E+1iPjnJDw9PI+zSrr7d+cdUClV8KXPw93lyPWQI4gCBPcIhtVq7aicEmDB2cZz
        c3XjcyEbTLp27coTUCgUoJcTu/+RYL+W/oQb8eAE5G7ypm6qbvc2FGupgiqKpgRsnh40gPrzvfH3JFn1
        rNVs17PWkysODancB91fJxKIWCKYUBP3v6xEziI9ZXyKTgOvig2arIXeMjf4kDNvL28ejDYr1Co1DaQK
        /h1WpUgkgpimInJzDxbcw8OD3d8mxhBPEWxu7ElIiftF7fOmajaxncyC0ZjFnctcXXmbWXKstSwwve34
        Mxexy18q/wMWXEjgKpFPjCdiCBnx76JEnpRKpcdp0uXO2Lryiigoq5g9l0qkEIlFcOrCj9p9kD07hqz6
        XUQmwf43PPgE/ItsxHrqwiWpi5RvMolIwisWzvg/wf4pscDjCB3R6Z9rF+IxIo9YTtQRTcQxgh2zfcQG
        4nUindATzOYh6tLldz24vdk9vVWyAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="resource.Image3" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image4" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="resource.Image5" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB6RJREFUWEfN
        V1lMXNcZpg9VX/xQqUpe+hapaVWpraoqjapKtVr1MVEfmsppYteJ7di1DSbYxnFI3AZbTRsnMa0x+3JZ
        h4HZ953ZN5ZhYJiBmcEwLDZgwAvYYCexvv7nzB2WgLGqWmp/6dM999zz/9/3/efcO5q8/8soOHHkRcJe
        hncOHfjN4YNvvFpUcHTfhfPvHqTr0cKTR47lcOr4oWOnThx+p6Q4/2BR/pHXae3v3j7w+m/zjx/m+Qw0
        /qlYevf45G8f7lHKGuyzM1HcXkg8EyzOD6M/bMalj84rzxSVf0Ok2jmahIqSL9cy+OrRFB5/Mf1MwGp9
        sToBp82Io8esznMfxPtFuo0or6veI1Up2twB76RcLcXsjcEdi/2nYORfPpzEowfj8LlsOHmyD8Ul8TWR
        diOuVFa81qlWlQbC3p5A0IqbM4PPpAvcPXX04f3r6wLOvh9/JNJuRFnltT80S1rrDSaFoaG5FlOT/bi7
        NAqnQwG7tQs2cyesJiksRinMhg6Y9BKYGfi4fX1sMRJoHVvLciJ9Vu5+bWUMXqcV+fl9OHM+DpF2Iyob
        6r7boZR/7A54Jh1OPe4sxPHfnIVc69neM/eryyl4ui1cwOn34ij9e3LrYbxWV/OnLrXyos/vsLjceiRH
        /HSC43DYZF/rADkkp8wtc807YdjoBn9OXcpeO9DfY8LachoP7ibhcZhRUNCHouJhXPpH8lsidTYq6mt/
        1dbVoTeYVP6KmnJMjvfgIbWNtY/t4VfkZnM3Hq1msLpCzkSwZzlsdr62QuT3krh/ZwQBtwn5xYbwny94
        LCVVnm+K1NmgLfhJVV31d+xmqbA4F8PKnVEs3IySc3JjyjkTnZNjG3XDYZVlYZHxOXYWNtDG0RPQc/Ll
        pTi6Y534nu5lx8v+V1Qi7fZwmDuE5cVhrNxOUOIoVu+luAvmhnVjmeaW6MMydyO6gZkBzJNYtmUP+Pox
        2vNs23Pk9xZiUIzVIy/wHH42sHf7IcyF3SQR7t4awj0SMTfdT87Ija6V0AYjwWHppNOsRMinI+hF6OB3
        a+Cyy+kstMOgaRHRjJBXw8nvzA9Cnq57ugCboU24PRcFF7EwzNWzbiwvJUhQBJmxIFIJD0aGXRiJ5eDk
        92OjXkyPh/nWrdB61sm7Ivnt2QHIkjVPF2DVtwpLNyNgImbGQzBqW6BXN8FEV7ddhrBPi2iPGUP91i0Y
        pPe9L2iAj7pjM0qgUwrQKhvhdymwROSLN/rROVr9dAEWbbOwMNNHCRGeyIQskqBM2o/EoAOxSI7Qsomc
        xpvuU9QNJp65ZmYY+a3pXkgTlcjzP7+7AJNGEOanenjCZNoHnaoRBuqAh9z3BfXk3oRor5lIGSxZ0H20
        14QBejYQNiLgYl1oh1pWR+9+F681PxmGJlKP520vpH8Z+XVGpNseRrUgzGWCPOEWCZnNhMlRNxEbEQkZ
        EKE2hzxqjqA7BxW/7yeBDJGQHjESmEl6wMzMZUKYnQgi0C2DpuAX6tSFl+pEuq1hsNt+bLdprJOUyBKu
        J5zQKxvgsknR69dQcR0vPhyxIE5bER/Igt0zsGdsTZ9fS11QwKxthtMiwc3xAG5c98HnoN+GAz/A49Mv
        bd+Civqaiwq99qqks9l7+fNPMJ328qSRAQt6fWr4SL3T0gF6S2Ch19Kio8NJB01PB81MB5Tds2dsjb9b
        jh6vCgMhHcbJxMyYD9MpD7w2+jTvf4KAaqHhPYvLKe9SySa66evGEtIxG7pNbQh7FIiG9UhEzEjFHBiL
        d3OwTk2lvLxTY7RNySE74v1m2iYtwm4FEUrhtUsxnXRjatQFt6WdBHwfj4t2EtDU2NLS0fbtLkmNIO2o
        x+SoExOJbgScXbDqmqFT1EMlrYa8vRIyQu76dag6a6CntTZ9CycfCuswOeJEJuGAy9S6iwChfo/eZv2r
        2WZMB5wKTMQdSA1aEHLJEOvR09iKDBVineFIezAjYn0u5cY45SWj9GaEtAiS+LBbRrXsGB+mbhqa0ffm
        izsLKK+tkXaqFb8n98rKqjKkqMhoxASjsg6ytnJ0tlwVwcZPwlVImwl0ZTk6eS0CDimuD1kxRmYcOgHh
        /T+cf1z08/si7UbUNje+qzEblW3S1qHq6itID5pJgBFDIQ1PZg7Gh+3cDevOdmSfsTVsbSpK34kgvZo+
        BdJRE1IDRtg0Dbh27u19IuXWqGpseItdpU1lQsApRzJiIHI1OSqDpPEKJEIZOgjsKhH+SWNCkwgas7ks
        smvaWQ7Bpm3gtUb79bCq6nCx5NTOAnLRKXwujPRqMdKnw0ivjieyAsxBasDE3WRh3oTsXEp0ypAjHQqq
        qI4WiR4NzIoalL5fsLuAjoZPhXhIhXhYzZNyYkYZmBgOAyfYAppnzzm4eCIViVmtYapplFXhL+dO7C6g
        8rMP/xULKBALKnnSZjEcTNATsL6G1udIh6kOqzfkl0MjuYrjh/64V6TaOc7mH/qRSVa5MuiT8aSsGAUv
        xAvmRInC1seEHOE6aUDOa7BaQVsrjr21T/vma6/u/teMRcHR/S+Uns8/cfmj04WXS88Ufnrx7AYuFRd+
        tgPY/JZ1lMdyL5eeLvz4QiH9d31jd+f/u8jL+zdZvMccKX7nZgAAAABJRU5ErkJggg==
</value>
  </data>
</root>