﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="resource.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAANdEVYdFRpdGxlAFJlcG9ydDsMcqUZAAAIXUlEQVRY
        R8WWeVSU5xXGRx0kQhQXFDXuYjRG25ymzbGe9iQmJga1RqMxmmisVZM0tbglKgiKC7LvIJAhhC24RBAR
        gUQsQdHIUpRl2BFk2JF9mRm2p/e+zBCmAex/fc/58c3HO+d7nru89xsJgP8rQ/9z0DoiS5cc9EuTmF94
        IPmH98+Szz3uSfa5Ja/40j/l5l6XJPVe56S+kyEZxW7ReXs3WcdKNljESNYdvS5ZczhS8taB7zVP6V9D
        auncDLFYeL9GmNYoZpd9YtDVu6V3a1q7UdbUjSv3FTjs96BYu0+M/i+0/xdLR1Pn5tdr8MPGaJBusYlv
        rm/ramlU9aGqow9ZtSq8cyiij/b0ieeGYCwhJfg5uppDicsvTt0qDzdOygkzxrcXPsGnjtF43zoWGyxj
        YPbVdSga1ahr70V5Sw9y67uRUqVGdF4Llm8PwNKtX2PxZl/8ftc3MD9lgyhLQ1w5ZpgfsN9gJz1ajxCZ
        GMnAqOxQ46Tah9ZAlwK7z8fhakY9Eh8rkVqhQna1GvIaNbLo+qBciR8K23Exqw0+ac1wut8Ex+RGONxt
        hMWNcry01Rd4/Cbkl+cj7KAB13AcoZOFoQyMzgyaTOKlQGc6LsVcw3vHruAj+9v4KjgLbvFlCEmpR3hG
        M2QpzXBOasSJuHp8erEcH/vnYb3dfaw8FIPF22Qwd7BDb8FSoPA3CDswjkWMCC7FsAY4PWP+HTAJUOUD
        LdeQEB8OF58QfHDAC6s+k+GVHTKYbvTBLDN3zFjjiulvu8BktTPde8J0iz9e3R2E9YdDcOjcN7gaZoWO
        rGXoy38RwfuFgSkEl2FEA9JUPzKgzASaQhESEkLbgFqtxsOHDxEZGYmIiAhER0cjNjYWt2/fRnJyMtLS
        0pCTk4OioiKUKxSoq6uDt7c3lNlL0JP3IgI/f45FTAhuyFEDugMf+hcb0LvvPZHSnwI0XEBwcDBt/7JU
        KpUQSkhIQGJiohBPJfHMzEzk5xfgcVkZqqqr8bShER4eHuiSLxYZCNgnDMwk+KSMaED/jpsR0J4E1Dkg
        KCiItn+9lEoliktKkJaeLsRz8/Lp/jEUlZWora9HU3OLMMDRo2AR/PcIA3MIPpbDGuAOHZfoRAZa44Ea
        awQGBtK27upj6A/ToVSRaJUQf6KoQFVNHZ42NqO5rQPu7u7ozV1EGVgE3136LDKfeKYBg1v240UDouog
        AgICaLt/hd6pQmBiBWQJ5fD9oQzecaXwjH0Mz5sl8IwphFtkNlyvZaGppR2tHSq4urqiR25KJkzhvVMY
        WETwURzWAE+68XFnn6cG/A59ir2QyWS0rVkUca+GbqKrl+ihBtWgIjrUvWjr7EI73Tg7O6M7eyG6cxbC
        8+OxLLKEMCBGNGAUc8oQfQ0y9JZtg7+/P233p5sj944vhXtMCRyjimAXWYDTl+Ww/i4bJ0KzYBGSCcuQ
        R2jvohbqBhwcHKDOXAB11gK4b9djkWWEITGsAR4Sk6NOkIE6aqDi9fDz8+uvOdHDkQ+KmiNWklAn0cGi
        aqCNrgL6bGdnh86M+VA+nA/XD4WB3xLPE8Ma4CExNeK4IXpr7NGV/wZ8fX1F9Jx2rrnLjSKcjyjAyYty
        HP32Ecy/TsPfLzzAPq972OtxF3uIZhW1EGFra4v29LnoSJ8H5w+Egd8RzzRgcuVLA/RUWEOd8xp8fHyE
        gR6KXBs9Rz4QtSZaFmThJqJR2c+ZM2fQ+mAO2lLnwnGzMPAHYjwxpAGeATylZoYfHIeuJ0doGC4XBrSp
        5453iirE6Su5OBr0CPv9Uynie9jh9BO207tiq+0tbDkbj6edENjY2KD53my0/DwH9puEgRXEBNYazgBP
        qdlh5uOgLvmC0mcKLy+vgehF3bnmmshbNZE3aSLWCtd30Ayjq7W1NRruvIDG5Fmw2ygMrCT4hTSigblB
        X+hDWbgbrSlzBgzQ6YIPnQCXG8U4Sxk4Rt3+T79UHXGtcC1dGSsrK9QnzsTTpBdgu0EY+DPBBkYPZ4Cn
        1ILAz/TRmb+DUjdbjNOB6DW110Yvak7iDSxO1JFoTTtQTVS1AZaWlqhJmIG6f83Eub9IWeR1YiIxogFT
        2R59dMg/RBOljscpDxw2oCS0x62F4IYbLF7L4iRcSddKulpYWKAqfjqqf5yOs+uFgVXEiAZ4TC72/5s+
        2rI3U/1+MaA982yAz7moPRugDAhxQkROVJC4ohU4fvw4FLHTUBlngjPrhIG3iEnEsAZ4TL50YddYtDza
        gPqfZuoY0B497bETtdcYqNEY4MgrSLxcY+DJjalQ3JyG02tFD7xNPNPAy947x6I5w0zUThgg8cEGRP0J
        HQPa6EmYo9caKL1mjCfRU2FjJjKwhphMDGuA5/Ryz4/GoilttWigSwHmcHNzEy8WJycnMV55wvGQ4XPO
        R427nRuOa86iWnzsdqAkcgpKo4xx6t3/zQBnYJn7Nj00pLyO2sSlqKD6lcdMQ+l1Y/Gwou8no+DSJOSF
        T0RumBHkoROQE2qEnBBmgrjPDZuIvIsTUXB5kvh+ScQUrYHVxIgl4FOwyHajXsVt13movbsC1YmvouLW
        K3gStwylN5agOMoUhRHzkHd5FnLDZ0AeZoLsYGNiErKCjeg6vt8ImWOTbLb46hQcWSWto2drJ+GwBsS7
        4K8rx1hZrZXmOb6vB4dNUtjTFLPbKMX596Q0UAg60+foWPHROrNOT3T46bVS2DBmegKO+CSzRirE1708
        2oWevZAY+geJxoT4SUZMJ5YSrxF/IniAvEG8SfBR4lS+Q3BNmXcJM2KthnWD4O/9kWBxfhGN0dHUuenP
        ApvglxL3A6eLB4cWrp8WbqbB8G/+oeDvsjCXl3/wDEQPQPIf3YRAJzgyiboAAAAASUVORK5CYII=
</value>
  </data>
  <data name="resource.Image1" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAANdEVYdFRpdGxlAFJlcG9ydDsMcqUZAAAIXUlEQVRY
        R8WWeVSU5xXGRx0kQhQXFDXuYjRG25ymzbGe9iQmJga1RqMxmmisVZM0tbglKgiKC7LvIJAhhC24RBAR
        gUQsQdHIUpRl2BFk2JF9mRm2p/e+zBCmAex/fc/58c3HO+d7nru89xsJgP8rQ/9z0DoiS5cc9EuTmF94
        IPmH98+Szz3uSfa5Ja/40j/l5l6XJPVe56S+kyEZxW7ReXs3WcdKNljESNYdvS5ZczhS8taB7zVP6V9D
        auncDLFYeL9GmNYoZpd9YtDVu6V3a1q7UdbUjSv3FTjs96BYu0+M/i+0/xdLR1Pn5tdr8MPGaJBusYlv
        rm/ramlU9aGqow9ZtSq8cyiij/b0ieeGYCwhJfg5uppDicsvTt0qDzdOygkzxrcXPsGnjtF43zoWGyxj
        YPbVdSga1ahr70V5Sw9y67uRUqVGdF4Llm8PwNKtX2PxZl/8ftc3MD9lgyhLQ1w5ZpgfsN9gJz1ajxCZ
        GMnAqOxQ46Tah9ZAlwK7z8fhakY9Eh8rkVqhQna1GvIaNbLo+qBciR8K23Exqw0+ac1wut8Ex+RGONxt
        hMWNcry01Rd4/Cbkl+cj7KAB13AcoZOFoQyMzgyaTOKlQGc6LsVcw3vHruAj+9v4KjgLbvFlCEmpR3hG
        M2QpzXBOasSJuHp8erEcH/vnYb3dfaw8FIPF22Qwd7BDb8FSoPA3CDswjkWMCC7FsAY4PWP+HTAJUOUD
        LdeQEB8OF58QfHDAC6s+k+GVHTKYbvTBLDN3zFjjiulvu8BktTPde8J0iz9e3R2E9YdDcOjcN7gaZoWO
        rGXoy38RwfuFgSkEl2FEA9JUPzKgzASaQhESEkLbgFqtxsOHDxEZGYmIiAhER0cjNjYWt2/fRnJyMtLS
        0pCTk4OioiKUKxSoq6uDt7c3lNlL0JP3IgI/f45FTAhuyFEDugMf+hcb0LvvPZHSnwI0XEBwcDBt/7JU
        KpUQSkhIQGJiohBPJfHMzEzk5xfgcVkZqqqr8bShER4eHuiSLxYZCNgnDMwk+KSMaED/jpsR0J4E1Dkg
        KCiItn+9lEoliktKkJaeLsRz8/Lp/jEUlZWora9HU3OLMMDRo2AR/PcIA3MIPpbDGuAOHZfoRAZa44Ea
        awQGBtK27upj6A/ToVSRaJUQf6KoQFVNHZ42NqO5rQPu7u7ozV1EGVgE3136LDKfeKYBg1v240UDouog
        AgICaLt/hd6pQmBiBWQJ5fD9oQzecaXwjH0Mz5sl8IwphFtkNlyvZaGppR2tHSq4urqiR25KJkzhvVMY
        WETwURzWAE+68XFnn6cG/A59ir2QyWS0rVkUca+GbqKrl+ihBtWgIjrUvWjr7EI73Tg7O6M7eyG6cxbC
        8+OxLLKEMCBGNGAUc8oQfQ0y9JZtg7+/P233p5sj944vhXtMCRyjimAXWYDTl+Ww/i4bJ0KzYBGSCcuQ
        R2jvohbqBhwcHKDOXAB11gK4b9djkWWEITGsAR4Sk6NOkIE6aqDi9fDz8+uvOdHDkQ+KmiNWklAn0cGi
        aqCNrgL6bGdnh86M+VA+nA/XD4WB3xLPE8Ma4CExNeK4IXpr7NGV/wZ8fX1F9Jx2rrnLjSKcjyjAyYty
        HP32Ecy/TsPfLzzAPq972OtxF3uIZhW1EGFra4v29LnoSJ8H5w+Egd8RzzRgcuVLA/RUWEOd8xp8fHyE
        gR6KXBs9Rz4QtSZaFmThJqJR2c+ZM2fQ+mAO2lLnwnGzMPAHYjwxpAGeATylZoYfHIeuJ0doGC4XBrSp
        5453iirE6Su5OBr0CPv9Uynie9jh9BO207tiq+0tbDkbj6edENjY2KD53my0/DwH9puEgRXEBNYazgBP
        qdlh5uOgLvmC0mcKLy+vgehF3bnmmshbNZE3aSLWCtd30Ayjq7W1NRruvIDG5Fmw2ygMrCT4hTSigblB
        X+hDWbgbrSlzBgzQ6YIPnQCXG8U4Sxk4Rt3+T79UHXGtcC1dGSsrK9QnzsTTpBdgu0EY+DPBBkYPZ4Cn
        1ILAz/TRmb+DUjdbjNOB6DW110Yvak7iDSxO1JFoTTtQTVS1AZaWlqhJmIG6f83Eub9IWeR1YiIxogFT
        2R59dMg/RBOljscpDxw2oCS0x62F4IYbLF7L4iRcSddKulpYWKAqfjqqf5yOs+uFgVXEiAZ4TC72/5s+
        2rI3U/1+MaA982yAz7moPRugDAhxQkROVJC4ohU4fvw4FLHTUBlngjPrhIG3iEnEsAZ4TL50YddYtDza
        gPqfZuoY0B497bETtdcYqNEY4MgrSLxcY+DJjalQ3JyG02tFD7xNPNPAy947x6I5w0zUThgg8cEGRP0J
        HQPa6EmYo9caKL1mjCfRU2FjJjKwhphMDGuA5/Ryz4/GoilttWigSwHmcHNzEy8WJycnMV55wvGQ4XPO
        R427nRuOa86iWnzsdqAkcgpKo4xx6t3/zQBnYJn7Nj00pLyO2sSlqKD6lcdMQ+l1Y/Gwou8no+DSJOSF
        T0RumBHkoROQE2qEnBBmgrjPDZuIvIsTUXB5kvh+ScQUrYHVxIgl4FOwyHajXsVt13movbsC1YmvouLW
        K3gStwylN5agOMoUhRHzkHd5FnLDZ0AeZoLsYGNiErKCjeg6vt8ImWOTbLb46hQcWSWto2drJ+GwBsS7
        4K8rx1hZrZXmOb6vB4dNUtjTFLPbKMX596Q0UAg60+foWPHROrNOT3T46bVS2DBmegKO+CSzRirE1708
        2oWevZAY+geJxoT4SUZMJ5YSrxF/IniAvEG8SfBR4lS+Q3BNmXcJM2KthnWD4O/9kWBxfhGN0dHUuenP
        ApvglxL3A6eLB4cWrp8WbqbB8G/+oeDvsjCXl3/wDEQPQPIf3YRAJzgyiboAAAAASUVORK5CYII=
</value>
  </data>
  <data name="resource.Image2" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAANdEVYdFRpdGxlAFJlcG9ydDsMcqUZAAAIXUlEQVRY
        R8WWeVSU5xXGRx0kQhQXFDXuYjRG25ymzbGe9iQmJga1RqMxmmisVZM0tbglKgiKC7LvIJAhhC24RBAR
        gUQsQdHIUpRl2BFk2JF9mRm2p/e+zBCmAex/fc/58c3HO+d7nru89xsJgP8rQ/9z0DoiS5cc9EuTmF94
        IPmH98+Szz3uSfa5Ja/40j/l5l6XJPVe56S+kyEZxW7ReXs3WcdKNljESNYdvS5ZczhS8taB7zVP6V9D
        auncDLFYeL9GmNYoZpd9YtDVu6V3a1q7UdbUjSv3FTjs96BYu0+M/i+0/xdLR1Pn5tdr8MPGaJBusYlv
        rm/ramlU9aGqow9ZtSq8cyiij/b0ieeGYCwhJfg5uppDicsvTt0qDzdOygkzxrcXPsGnjtF43zoWGyxj
        YPbVdSga1ahr70V5Sw9y67uRUqVGdF4Llm8PwNKtX2PxZl/8ftc3MD9lgyhLQ1w5ZpgfsN9gJz1ajxCZ
        GMnAqOxQ46Tah9ZAlwK7z8fhakY9Eh8rkVqhQna1GvIaNbLo+qBciR8K23Exqw0+ac1wut8Ex+RGONxt
        hMWNcry01Rd4/Cbkl+cj7KAB13AcoZOFoQyMzgyaTOKlQGc6LsVcw3vHruAj+9v4KjgLbvFlCEmpR3hG
        M2QpzXBOasSJuHp8erEcH/vnYb3dfaw8FIPF22Qwd7BDb8FSoPA3CDswjkWMCC7FsAY4PWP+HTAJUOUD
        LdeQEB8OF58QfHDAC6s+k+GVHTKYbvTBLDN3zFjjiulvu8BktTPde8J0iz9e3R2E9YdDcOjcN7gaZoWO
        rGXoy38RwfuFgSkEl2FEA9JUPzKgzASaQhESEkLbgFqtxsOHDxEZGYmIiAhER0cjNjYWt2/fRnJyMtLS
        0pCTk4OioiKUKxSoq6uDt7c3lNlL0JP3IgI/f45FTAhuyFEDugMf+hcb0LvvPZHSnwI0XEBwcDBt/7JU
        KpUQSkhIQGJiohBPJfHMzEzk5xfgcVkZqqqr8bShER4eHuiSLxYZCNgnDMwk+KSMaED/jpsR0J4E1Dkg
        KCiItn+9lEoliktKkJaeLsRz8/Lp/jEUlZWora9HU3OLMMDRo2AR/PcIA3MIPpbDGuAOHZfoRAZa44Ea
        awQGBtK27upj6A/ToVSRaJUQf6KoQFVNHZ42NqO5rQPu7u7ozV1EGVgE3136LDKfeKYBg1v240UDouog
        AgICaLt/hd6pQmBiBWQJ5fD9oQzecaXwjH0Mz5sl8IwphFtkNlyvZaGppR2tHSq4urqiR25KJkzhvVMY
        WETwURzWAE+68XFnn6cG/A59ir2QyWS0rVkUca+GbqKrl+ihBtWgIjrUvWjr7EI73Tg7O6M7eyG6cxbC
        8+OxLLKEMCBGNGAUc8oQfQ0y9JZtg7+/P233p5sj944vhXtMCRyjimAXWYDTl+Ww/i4bJ0KzYBGSCcuQ
        R2jvohbqBhwcHKDOXAB11gK4b9djkWWEITGsAR4Sk6NOkIE6aqDi9fDz8+uvOdHDkQ+KmiNWklAn0cGi
        aqCNrgL6bGdnh86M+VA+nA/XD4WB3xLPE8Ma4CExNeK4IXpr7NGV/wZ8fX1F9Jx2rrnLjSKcjyjAyYty
        HP32Ecy/TsPfLzzAPq972OtxF3uIZhW1EGFra4v29LnoSJ8H5w+Egd8RzzRgcuVLA/RUWEOd8xp8fHyE
        gR6KXBs9Rz4QtSZaFmThJqJR2c+ZM2fQ+mAO2lLnwnGzMPAHYjwxpAGeATylZoYfHIeuJ0doGC4XBrSp
        5453iirE6Su5OBr0CPv9Uynie9jh9BO207tiq+0tbDkbj6edENjY2KD53my0/DwH9puEgRXEBNYazgBP
        qdlh5uOgLvmC0mcKLy+vgehF3bnmmshbNZE3aSLWCtd30Ayjq7W1NRruvIDG5Fmw2ygMrCT4hTSigblB
        X+hDWbgbrSlzBgzQ6YIPnQCXG8U4Sxk4Rt3+T79UHXGtcC1dGSsrK9QnzsTTpBdgu0EY+DPBBkYPZ4Cn
        1ILAz/TRmb+DUjdbjNOB6DW110Yvak7iDSxO1JFoTTtQTVS1AZaWlqhJmIG6f83Eub9IWeR1YiIxogFT
        2R59dMg/RBOljscpDxw2oCS0x62F4IYbLF7L4iRcSddKulpYWKAqfjqqf5yOs+uFgVXEiAZ4TC72/5s+
        2rI3U/1+MaA982yAz7moPRugDAhxQkROVJC4ohU4fvw4FLHTUBlngjPrhIG3iEnEsAZ4TL50YddYtDza
        gPqfZuoY0B497bETtdcYqNEY4MgrSLxcY+DJjalQ3JyG02tFD7xNPNPAy947x6I5w0zUThgg8cEGRP0J
        HQPa6EmYo9caKL1mjCfRU2FjJjKwhphMDGuA5/Ryz4/GoilttWigSwHmcHNzEy8WJycnMV55wvGQ4XPO
        R427nRuOa86iWnzsdqAkcgpKo4xx6t3/zQBnYJn7Nj00pLyO2sSlqKD6lcdMQ+l1Y/Gwou8no+DSJOSF
        T0RumBHkoROQE2qEnBBmgrjPDZuIvIsTUXB5kvh+ScQUrYHVxIgl4FOwyHajXsVt13movbsC1YmvouLW
        K3gStwylN5agOMoUhRHzkHd5FnLDZ0AeZoLsYGNiErKCjeg6vt8ImWOTbLb46hQcWSWto2drJ+GwBsS7
        4K8rx1hZrZXmOb6vB4dNUtjTFLPbKMX596Q0UAg60+foWPHROrNOT3T46bVS2DBmegKO+CSzRirE1708
        2oWevZAY+geJxoT4SUZMJ5YSrxF/IniAvEG8SfBR4lS+Q3BNmXcJM2KthnWD4O/9kWBxfhGN0dHUuenP
        ApvglxL3A6eLB4cWrp8WbqbB8G/+oeDvsjCXl3/wDEQPQPIf3YRAJzgyiboAAAAASUVORK5CYII=
</value>
  </data>
  <data name="resource.Image3" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB9BJREFUWEe1
        V2lQFAcancXdTdw1CgSygILKqikTUVcjhkLEY0HRAhlkjAeFgK5yqKWoKCCnHHKNFA4DIsfAMhwDEoHC
        AyMCYoGcM5zhUCScJeGUSwrm7TeT9keqsolZ2Ff1qrtq5n3vfV9/Pd3D+hBc3rFax9fkSxsuW8c90GSN
        62X9pUdOb1BWZz7+/+C4joqCva7mWU8Tnfao4wYQnDCE8IQB4q23gHdQBz5G2ji1QTndau0nnzKS+cMR
        HdWlp3Q1a7zNNiCY8xV4VvqIsdmGO9Zfg394E8LM18HXeBWuGmrBbr1SC2fVX/7KSOeOA6sWazruWtfj
        aroJPgd1EcjRRdDBzQhhr0cYMYS9Af77PofHbm1cMtCEwxY1cFYt9GTkc4ep1kcqh1d/nH9yoxIcNynD
        xWApAky/wG07AyQ7GeGmpQ4ub1sGF8MVOLNVA5zlLFhoL6xj5PMDMc/BcapCgBmJCNKmbEib8zBTl4l3
        lUmYKOKhLOwEjmovAFuDhT0qLOzT+iOM1BU+YuRzR3eGZ/TkiwRMi9Mx0/g+wF1MlQsw9oSL0YcBcNZX
        h/GnLOxUYmG3hgJ2qLLWMPK5ozfT99FEWRymqlIwLcmQm09Xp2C8OBJvH4diJO86/GkRtyuyoLeEBX1V
        FgxUWF8x8rmjW+RVNfbsNibKEjBZkYRJCiMbvaz7kQcBGLrnCVfj1di7TAF6NIGtNAl9ZdZ6Rj539KS6
        fj/yKASj+WF4mx+Ct9+FYCQ/GCP3/cncCwOZrgi23IiuhHNIubAfurJJKLIUGfnc0RHr+P1A5lUM3vPA
        UI4PhnN8MZTtjcGsa/hRdAV9wgvoTnBCR9QJ5LpzsGUJq4KRzg9eRtq9kJm8SbuIfjL8UeSC/nQXvEm9
        iN6kc+iKdSBzOzmdti3HNiXWLkY6P2jmHkvtjLWXd9kjY+JZdAnOoCveER13TuE1Gb+8ZQWBre6ssTrr
        FCObP0j8Lc6182zQLusy+gR1elJ+fM23xSvecbRyj8gZZLb2JCOZX1R67FNvCubMtpFJW/hRtIYfQxuX
        jtzDaAm2RHPIIdT6meOgjuo7+rrBT6p5RuW1vbcbA8zREMBG0w06BrJR72eGBn9z1N84BLHbLljbu2PB
        wkX/ZiTziyCLjVqVbrultV7GkHjKaIR6fzbE1y3ofA8e+p3E8bQhKKtrIuJJe2JkyZtFjHR+sPLva9zC
        7dnSGtedEHuZgJ7NqPLYi6ZACzRyj+F8Yi2yXkmhrKGFpNoxxJT2Nd8p7dnByOcGbp7Yaf2WbTP36/oQ
        7ueL/PPbUe61D+08K3TGnsYN4VMynUJBH/CZljbiKvqRXDWAwi4pEqtGcvkl/SuZUr8P8aWdfxJW9uck
        lb3CP804SK/owhlhA2po8TpjT6E33Q0holLcKp/AMzIveAOoLluB5IoepFb1I+81kE+MqZicCi8asGLK
        fjiiCpp5NmevYNXaL2FEAaIK23E0Ywp1jwSoz72N82kv4fxoHKV9syjqBb6jEIuUVJBR24977VKktwKJ
        lcPIawOiKmelYU8HtjKlfxt+ac8/sbrgNc22O4/stgkYW1rDxCkUpnGDMHXmYz//B7CFE8hsnMRz6vwp
        BUip7oXaitUQVA8i8xWZlvQgo3EGGU2AQALcLB4tYMr/NvxEJebLVq6BoPQlslrfwTk0Fjp7bGF4PhEH
        AguxyzkJzjlDKO0n8x7qnhiYlIdNO/fjWxq7QDIG4YteCOqBeDIX1gK8F1LQFD7sPSE0u9LLwTsUGfVD
        SKoZgIH5EVi788COqIPpjRLscc9AZsOovPMnZP6wZRhmNK1jLoG42w6E3W/AvTYpYsU0iSogmQLwK4Cg
        /D5nxuLX4R4pSogubIKITOIr+vCN/SUkVg/AJFQMs5tiXKEZy7p+3A1k1/Ujo3kc6w2M4JNSBEHtBPgF
        bUik7m9XA5FkLKAg0ZWAU8SD1EXKGgsYm/8OW9egAn5hC0Qt04gpakHYt2XIos44PAnMbjVB2DCBvE4g
        Q9KPtIa3yOmiBVRUQVzVCMKfvKbQo/Lu+WTKKwfiaigMTeJyfFk5lVciLiT+Qeb1i3CJefjYMzoTac0z
        cI8QgFf4A1JbAOf0FjiktCOLrnOKZASJNYO4S+ehD+qxYt1mJDdKEZjdiDixFFHUvcxcxlg6lwXwzGxq
        o/LLiX8jLib+mfhzaGtrJ6poLG+0ueDZZ3v2KpLrJhH1vAdxklmEFw/RMo0juWEG/GddENEtllY/jos+
        wdhtaYeEOimCHnTIRy8zlo1fdrzDBPDOaushC9nr2udELaJsGr88iY07TFXsAxPy9nBs8PW+w+DSxvHL
        3yGeFiq8oBOZtGQ5HWQQHQthcjL09PTgKiij7w3Jjd/zfYAYCuAhamin0tuJ/yCqERWIv44DDt6cgJym
        1sjSYUQ8n0TEs1H6dRtDfB2Q1AAcOWaNa9fcoaaqCvuwXISXvPtZADnLphD0uG96q5ltJJXUIf6+h5Xh
        IXsF9hl/C8fw3LyQ/O4p3vNhxNL9fYcWzSutBht3WcLkXz7gFk/I73fZ8kWUTiG0cBi+2S+HTe2vpy5c
        rCx7VVvyU8X/HR8rqmktNba9stfyItfDxjtB6HAzt8gtuVriliJuvhRXWn867F7xoUu30rZ/4+StuXaz
        OV3itaT7gD+sLNZ/ACDbZDFNzZRtAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="resource.Image4" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB9BJREFUWEe1
        V2lQFAcancXdTdw1CgSygILKqikTUVcjhkLEY0HRAhlkjAeFgK5yqKWoKCCnHHKNFA4DIsfAMhwDEoHC
        AyMCYoGcM5zhUCScJeGUSwrm7TeT9keqsolZ2Ff1qrtq5n3vfV9/Pd3D+hBc3rFax9fkSxsuW8c90GSN
        62X9pUdOb1BWZz7+/+C4joqCva7mWU8Tnfao4wYQnDCE8IQB4q23gHdQBz5G2ji1QTndau0nnzKS+cMR
        HdWlp3Q1a7zNNiCY8xV4VvqIsdmGO9Zfg394E8LM18HXeBWuGmrBbr1SC2fVX/7KSOeOA6sWazruWtfj
        aroJPgd1EcjRRdDBzQhhr0cYMYS9Af77PofHbm1cMtCEwxY1cFYt9GTkc4ep1kcqh1d/nH9yoxIcNynD
        xWApAky/wG07AyQ7GeGmpQ4ub1sGF8MVOLNVA5zlLFhoL6xj5PMDMc/BcapCgBmJCNKmbEib8zBTl4l3
        lUmYKOKhLOwEjmovAFuDhT0qLOzT+iOM1BU+YuRzR3eGZ/TkiwRMi9Mx0/g+wF1MlQsw9oSL0YcBcNZX
        h/GnLOxUYmG3hgJ2qLLWMPK5ozfT99FEWRymqlIwLcmQm09Xp2C8OBJvH4diJO86/GkRtyuyoLeEBX1V
        FgxUWF8x8rmjW+RVNfbsNibKEjBZkYRJCiMbvaz7kQcBGLrnCVfj1di7TAF6NIGtNAl9ZdZ6Rj539KS6
        fj/yKASj+WF4mx+Ct9+FYCQ/GCP3/cncCwOZrgi23IiuhHNIubAfurJJKLIUGfnc0RHr+P1A5lUM3vPA
        UI4PhnN8MZTtjcGsa/hRdAV9wgvoTnBCR9QJ5LpzsGUJq4KRzg9eRtq9kJm8SbuIfjL8UeSC/nQXvEm9
        iN6kc+iKdSBzOzmdti3HNiXWLkY6P2jmHkvtjLWXd9kjY+JZdAnOoCveER13TuE1Gb+8ZQWBre6ssTrr
        FCObP0j8Lc6182zQLusy+gR1elJ+fM23xSvecbRyj8gZZLb2JCOZX1R67FNvCubMtpFJW/hRtIYfQxuX
        jtzDaAm2RHPIIdT6meOgjuo7+rrBT6p5RuW1vbcbA8zREMBG0w06BrJR72eGBn9z1N84BLHbLljbu2PB
        wkX/ZiTziyCLjVqVbrultV7GkHjKaIR6fzbE1y3ofA8e+p3E8bQhKKtrIuJJe2JkyZtFjHR+sPLva9zC
        7dnSGtedEHuZgJ7NqPLYi6ZACzRyj+F8Yi2yXkmhrKGFpNoxxJT2Nd8p7dnByOcGbp7Yaf2WbTP36/oQ
        7ueL/PPbUe61D+08K3TGnsYN4VMynUJBH/CZljbiKvqRXDWAwi4pEqtGcvkl/SuZUr8P8aWdfxJW9uck
        lb3CP804SK/owhlhA2po8TpjT6E33Q0holLcKp/AMzIveAOoLluB5IoepFb1I+81kE+MqZicCi8asGLK
        fjiiCpp5NmevYNXaL2FEAaIK23E0Ywp1jwSoz72N82kv4fxoHKV9syjqBb6jEIuUVJBR24977VKktwKJ
        lcPIawOiKmelYU8HtjKlfxt+ac8/sbrgNc22O4/stgkYW1rDxCkUpnGDMHXmYz//B7CFE8hsnMRz6vwp
        BUip7oXaitUQVA8i8xWZlvQgo3EGGU2AQALcLB4tYMr/NvxEJebLVq6BoPQlslrfwTk0Fjp7bGF4PhEH
        AguxyzkJzjlDKO0n8x7qnhiYlIdNO/fjWxq7QDIG4YteCOqBeDIX1gK8F1LQFD7sPSE0u9LLwTsUGfVD
        SKoZgIH5EVi788COqIPpjRLscc9AZsOovPMnZP6wZRhmNK1jLoG42w6E3W/AvTYpYsU0iSogmQLwK4Cg
        /D5nxuLX4R4pSogubIKITOIr+vCN/SUkVg/AJFQMs5tiXKEZy7p+3A1k1/Ujo3kc6w2M4JNSBEHtBPgF
        bUik7m9XA5FkLKAg0ZWAU8SD1EXKGgsYm/8OW9egAn5hC0Qt04gpakHYt2XIos44PAnMbjVB2DCBvE4g
        Q9KPtIa3yOmiBVRUQVzVCMKfvKbQo/Lu+WTKKwfiaigMTeJyfFk5lVciLiT+Qeb1i3CJefjYMzoTac0z
        cI8QgFf4A1JbAOf0FjiktCOLrnOKZASJNYO4S+ehD+qxYt1mJDdKEZjdiDixFFHUvcxcxlg6lwXwzGxq
        o/LLiX8jLib+mfhzaGtrJ6poLG+0ueDZZ3v2KpLrJhH1vAdxklmEFw/RMo0juWEG/GddENEtllY/jos+
        wdhtaYeEOimCHnTIRy8zlo1fdrzDBPDOaushC9nr2udELaJsGr88iY07TFXsAxPy9nBs8PW+w+DSxvHL
        3yGeFiq8oBOZtGQ5HWQQHQthcjL09PTgKiij7w3Jjd/zfYAYCuAhamin0tuJ/yCqERWIv44DDt6cgJym
        1sjSYUQ8n0TEs1H6dRtDfB2Q1AAcOWaNa9fcoaaqCvuwXISXvPtZADnLphD0uG96q5ltJJXUIf6+h5Xh
        IXsF9hl/C8fw3LyQ/O4p3vNhxNL9fYcWzSutBht3WcLkXz7gFk/I73fZ8kWUTiG0cBi+2S+HTe2vpy5c
        rCx7VVvyU8X/HR8rqmktNba9stfyItfDxjtB6HAzt8gtuVriliJuvhRXWn867F7xoUu30rZ/4+StuXaz
        OV3itaT7gD+sLNZ/ACDbZDFNzZRtAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="resource.Image5" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB9BJREFUWEe1
        V2lQFAcancXdTdw1CgSygILKqikTUVcjhkLEY0HRAhlkjAeFgK5yqKWoKCCnHHKNFA4DIsfAMhwDEoHC
        AyMCYoGcM5zhUCScJeGUSwrm7TeT9keqsolZ2Ff1qrtq5n3vfV9/Pd3D+hBc3rFax9fkSxsuW8c90GSN
        62X9pUdOb1BWZz7+/+C4joqCva7mWU8Tnfao4wYQnDCE8IQB4q23gHdQBz5G2ji1QTndau0nnzKS+cMR
        HdWlp3Q1a7zNNiCY8xV4VvqIsdmGO9Zfg394E8LM18HXeBWuGmrBbr1SC2fVX/7KSOeOA6sWazruWtfj
        aroJPgd1EcjRRdDBzQhhr0cYMYS9Af77PofHbm1cMtCEwxY1cFYt9GTkc4ep1kcqh1d/nH9yoxIcNynD
        xWApAky/wG07AyQ7GeGmpQ4ub1sGF8MVOLNVA5zlLFhoL6xj5PMDMc/BcapCgBmJCNKmbEib8zBTl4l3
        lUmYKOKhLOwEjmovAFuDhT0qLOzT+iOM1BU+YuRzR3eGZ/TkiwRMi9Mx0/g+wF1MlQsw9oSL0YcBcNZX
        h/GnLOxUYmG3hgJ2qLLWMPK5ozfT99FEWRymqlIwLcmQm09Xp2C8OBJvH4diJO86/GkRtyuyoLeEBX1V
        FgxUWF8x8rmjW+RVNfbsNibKEjBZkYRJCiMbvaz7kQcBGLrnCVfj1di7TAF6NIGtNAl9ZdZ6Rj539KS6
        fj/yKASj+WF4mx+Ct9+FYCQ/GCP3/cncCwOZrgi23IiuhHNIubAfurJJKLIUGfnc0RHr+P1A5lUM3vPA
        UI4PhnN8MZTtjcGsa/hRdAV9wgvoTnBCR9QJ5LpzsGUJq4KRzg9eRtq9kJm8SbuIfjL8UeSC/nQXvEm9
        iN6kc+iKdSBzOzmdti3HNiXWLkY6P2jmHkvtjLWXd9kjY+JZdAnOoCveER13TuE1Gb+8ZQWBre6ssTrr
        FCObP0j8Lc6182zQLusy+gR1elJ+fM23xSvecbRyj8gZZLb2JCOZX1R67FNvCubMtpFJW/hRtIYfQxuX
        jtzDaAm2RHPIIdT6meOgjuo7+rrBT6p5RuW1vbcbA8zREMBG0w06BrJR72eGBn9z1N84BLHbLljbu2PB
        wkX/ZiTziyCLjVqVbrultV7GkHjKaIR6fzbE1y3ofA8e+p3E8bQhKKtrIuJJe2JkyZtFjHR+sPLva9zC
        7dnSGtedEHuZgJ7NqPLYi6ZACzRyj+F8Yi2yXkmhrKGFpNoxxJT2Nd8p7dnByOcGbp7Yaf2WbTP36/oQ
        7ueL/PPbUe61D+08K3TGnsYN4VMynUJBH/CZljbiKvqRXDWAwi4pEqtGcvkl/SuZUr8P8aWdfxJW9uck
        lb3CP804SK/owhlhA2po8TpjT6E33Q0holLcKp/AMzIveAOoLluB5IoepFb1I+81kE+MqZicCi8asGLK
        fjiiCpp5NmevYNXaL2FEAaIK23E0Ywp1jwSoz72N82kv4fxoHKV9syjqBb6jEIuUVJBR24977VKktwKJ
        lcPIawOiKmelYU8HtjKlfxt+ac8/sbrgNc22O4/stgkYW1rDxCkUpnGDMHXmYz//B7CFE8hsnMRz6vwp
        BUip7oXaitUQVA8i8xWZlvQgo3EGGU2AQALcLB4tYMr/NvxEJebLVq6BoPQlslrfwTk0Fjp7bGF4PhEH
        AguxyzkJzjlDKO0n8x7qnhiYlIdNO/fjWxq7QDIG4YteCOqBeDIX1gK8F1LQFD7sPSE0u9LLwTsUGfVD
        SKoZgIH5EVi788COqIPpjRLscc9AZsOovPMnZP6wZRhmNK1jLoG42w6E3W/AvTYpYsU0iSogmQLwK4Cg
        /D5nxuLX4R4pSogubIKITOIr+vCN/SUkVg/AJFQMs5tiXKEZy7p+3A1k1/Ujo3kc6w2M4JNSBEHtBPgF
        bUik7m9XA5FkLKAg0ZWAU8SD1EXKGgsYm/8OW9egAn5hC0Qt04gpakHYt2XIos44PAnMbjVB2DCBvE4g
        Q9KPtIa3yOmiBVRUQVzVCMKfvKbQo/Lu+WTKKwfiaigMTeJyfFk5lVciLiT+Qeb1i3CJefjYMzoTac0z
        cI8QgFf4A1JbAOf0FjiktCOLrnOKZASJNYO4S+ehD+qxYt1mJDdKEZjdiDixFFHUvcxcxlg6lwXwzGxq
        o/LLiX8jLib+mfhzaGtrJ6poLG+0ueDZZ3v2KpLrJhH1vAdxklmEFw/RMo0juWEG/GddENEtllY/jos+
        wdhtaYeEOimCHnTIRy8zlo1fdrzDBPDOaushC9nr2udELaJsGr88iY07TFXsAxPy9nBs8PW+w+DSxvHL
        3yGeFiq8oBOZtGQ5HWQQHQthcjL09PTgKiij7w3Jjd/zfYAYCuAhamin0tuJ/yCqERWIv44DDt6cgJym
        1sjSYUQ8n0TEs1H6dRtDfB2Q1AAcOWaNa9fcoaaqCvuwXISXvPtZADnLphD0uG96q5ltJJXUIf6+h5Xh
        IXsF9hl/C8fw3LyQ/O4p3vNhxNL9fYcWzSutBht3WcLkXz7gFk/I73fZ8kWUTiG0cBi+2S+HTe2vpy5c
        rCx7VVvyU8X/HR8rqmktNba9stfyItfDxjtB6HAzt8gtuVriliJuvhRXWn867F7xoUu30rZ/4+StuXaz
        OV3itaT7gD+sLNZ/ACDbZDFNzZRtAAAAAElFTkSuQmCC
</value>
  </data>
</root>