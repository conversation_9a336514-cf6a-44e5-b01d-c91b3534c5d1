﻿
namespace SMART_CLINIC_SOFTWARE_2020.Main_Form
{
    partial class frmSTOCK_MAIN
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraEditors.TileItemElement tileItemElement100 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement101 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement102 = new DevExpress.XtraEditors.TileItemElement();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmSTOCK_MAIN));
            DevExpress.XtraEditors.TileItemFrame tileItemFrame25 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement103 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement104 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement105 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame26 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement106 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement107 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement108 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame27 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement109 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement110 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement111 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement112 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement113 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement114 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame28 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement115 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement116 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement117 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame29 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement118 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement119 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement120 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame30 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement121 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement122 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement123 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame31 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement124 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement125 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement126 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement127 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement128 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement129 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame32 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement130 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement131 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement132 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame33 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement133 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement134 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement135 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame34 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement136 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement137 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement138 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement139 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement140 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement141 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame35 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement142 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement143 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement144 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame36 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement145 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement146 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement147 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame37 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement148 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement149 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement150 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame38 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement151 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement152 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement153 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement154 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement155 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement156 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame39 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement157 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement158 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement159 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame40 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement160 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement161 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement162 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement163 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement164 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement165 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame41 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement166 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement167 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement168 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame42 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement169 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement170 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement171 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement172 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement173 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement174 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame43 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement175 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement176 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement177 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame44 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement178 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement179 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement180 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement181 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement182 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement183 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame45 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement184 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement185 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement186 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame46 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement187 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement188 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement189 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement190 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement191 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement192 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame47 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement193 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement194 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement195 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame48 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement196 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement197 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement198 = new DevExpress.XtraEditors.TileItemElement();
            this.tileItem1 = new DevExpress.XtraEditors.TileItem();
            this.tileItem2 = new DevExpress.XtraEditors.TileItem();
            this.tileGroup1 = new DevExpress.XtraEditors.TileGroup();
            this.tileGroup2 = new DevExpress.XtraEditors.TileGroup();
            this.btnDOC_LIST = new DevExpress.XtraEditors.TileItem();
            this.btnEDIT_DOC = new DevExpress.XtraEditors.TileItem();
            this.tileControl1 = new DevExpress.XtraEditors.TileControl();
            this.tileGroup3 = new DevExpress.XtraEditors.TileGroup();
            this.btnSTOCK = new DevExpress.XtraEditors.TileItem();
            this.btnSTOCK_ADD = new DevExpress.XtraEditors.TileItem();
            this.tileGroup4 = new DevExpress.XtraEditors.TileGroup();
            this.btnSTOCK_PULL = new DevExpress.XtraEditors.TileItem();
            this.btnADD_REPORT = new DevExpress.XtraEditors.TileItem();
            this.btnPULL_REPORT = new DevExpress.XtraEditors.TileItem();
            this.SuspendLayout();
            // 
            // tileItem1
            // 
            tileItemElement100.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement100.Appearance.Hovered.Options.UseFont = true;
            tileItemElement100.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement100.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement100.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement100.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement100.Appearance.Normal.Options.UseFont = true;
            tileItemElement100.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement100.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement100.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement100.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement100.Appearance.Selected.Options.UseFont = true;
            tileItemElement100.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement100.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement100.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement100.MaxWidth = 160;
            tileItemElement100.Text = "قائمة الاطباء";
            tileItemElement100.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement100.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement101.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement101.Appearance.Hovered.Options.UseFont = true;
            tileItemElement101.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement101.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement101.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement101.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement101.Appearance.Normal.Options.UseFont = true;
            tileItemElement101.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement101.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement101.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement101.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement101.Appearance.Selected.Options.UseFont = true;
            tileItemElement101.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement101.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement101.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement101.MaxWidth = 160;
            tileItemElement101.Text = "";
            tileItemElement101.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement101.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement102.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image")));
            tileItemElement102.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement102.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement102.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement102.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement102.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement102.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            this.tileItem1.Elements.Add(tileItemElement100);
            this.tileItem1.Elements.Add(tileItemElement101);
            this.tileItem1.Elements.Add(tileItemElement102);
            tileItemFrame25.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemElement103.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement103.Appearance.Hovered.Options.UseFont = true;
            tileItemElement103.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement103.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement103.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement103.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement103.Appearance.Normal.Options.UseFont = true;
            tileItemElement103.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement103.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement103.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement103.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement103.Appearance.Selected.Options.UseFont = true;
            tileItemElement103.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement103.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement103.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement103.MaxWidth = 160;
            tileItemElement103.Text = "قائمة الاطباء";
            tileItemElement103.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement103.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement104.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement104.Appearance.Hovered.Options.UseFont = true;
            tileItemElement104.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement104.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement104.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement104.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement104.Appearance.Normal.Options.UseFont = true;
            tileItemElement104.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement104.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement104.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement104.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement104.Appearance.Selected.Options.UseFont = true;
            tileItemElement104.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement104.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement104.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement104.MaxWidth = 160;
            tileItemElement104.Text = "";
            tileItemElement104.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement104.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement105.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image1")));
            tileItemElement105.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement105.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement105.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement105.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement105.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement105.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame25.Elements.Add(tileItemElement103);
            tileItemFrame25.Elements.Add(tileItemElement104);
            tileItemFrame25.Elements.Add(tileItemElement105);
            tileItemFrame26.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemElement106.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement106.Appearance.Hovered.Options.UseFont = true;
            tileItemElement106.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement106.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement106.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement106.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement106.Appearance.Normal.Options.UseFont = true;
            tileItemElement106.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement106.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement106.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement106.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement106.Appearance.Selected.Options.UseFont = true;
            tileItemElement106.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement106.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement106.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement106.MaxWidth = 160;
            tileItemElement106.Text = "قائمة الاطباء";
            tileItemElement106.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement106.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement107.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement107.Appearance.Hovered.Options.UseFont = true;
            tileItemElement107.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement107.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement107.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement107.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement107.Appearance.Normal.Options.UseFont = true;
            tileItemElement107.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement107.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement107.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement107.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement107.Appearance.Selected.Options.UseFont = true;
            tileItemElement107.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement107.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement107.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement107.MaxWidth = 160;
            tileItemElement107.Text = "";
            tileItemElement107.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement107.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement108.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image2")));
            tileItemElement108.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement108.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement108.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement108.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement108.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement108.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame26.Elements.Add(tileItemElement106);
            tileItemFrame26.Elements.Add(tileItemElement107);
            tileItemFrame26.Elements.Add(tileItemElement108);
            tileItemFrame27.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollLeft;
            tileItemElement109.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement109.Appearance.Hovered.Options.UseFont = true;
            tileItemElement109.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement109.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement109.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement109.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement109.Appearance.Normal.Options.UseFont = true;
            tileItemElement109.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement109.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement109.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement109.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement109.Appearance.Selected.Options.UseFont = true;
            tileItemElement109.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement109.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement109.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement109.MaxWidth = 160;
            tileItemElement109.Text = "قائمة الاطباء";
            tileItemElement109.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement109.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement110.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement110.Appearance.Hovered.Options.UseFont = true;
            tileItemElement110.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement110.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement110.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement110.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement110.Appearance.Normal.Options.UseFont = true;
            tileItemElement110.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement110.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement110.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement110.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement110.Appearance.Selected.Options.UseFont = true;
            tileItemElement110.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement110.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement110.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement110.MaxWidth = 160;
            tileItemElement110.Text = "";
            tileItemElement110.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement110.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement111.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image3")));
            tileItemElement111.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement111.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement111.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement111.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement111.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement111.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame27.Elements.Add(tileItemElement109);
            tileItemFrame27.Elements.Add(tileItemElement110);
            tileItemFrame27.Elements.Add(tileItemElement111);
            this.tileItem1.Frames.Add(tileItemFrame25);
            this.tileItem1.Frames.Add(tileItemFrame26);
            this.tileItem1.Frames.Add(tileItemFrame27);
            this.tileItem1.Id = 0;
            this.tileItem1.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tileItem1.Name = "tileItem1";
            // 
            // tileItem2
            // 
            tileItemElement112.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement112.Appearance.Hovered.Options.UseFont = true;
            tileItemElement112.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement112.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement112.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement112.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement112.Appearance.Normal.Options.UseFont = true;
            tileItemElement112.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement112.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement112.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement112.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement112.Appearance.Pressed.Options.UseFont = true;
            tileItemElement112.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement112.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement112.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement112.Appearance.Selected.Options.UseFont = true;
            tileItemElement112.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement112.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement112.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement112.MaxWidth = 160;
            tileItemElement112.Text = "أضافة _ تعديل الأطباء";
            tileItemElement112.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement112.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement113.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement113.Appearance.Hovered.Options.UseFont = true;
            tileItemElement113.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement113.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement113.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement113.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement113.Appearance.Normal.Options.UseFont = true;
            tileItemElement113.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement113.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement113.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement113.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement113.Appearance.Selected.Options.UseFont = true;
            tileItemElement113.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement113.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement113.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement113.MaxWidth = 160;
            tileItemElement113.Text = "";
            tileItemElement113.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement113.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement114.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image4")));
            tileItemElement114.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement114.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement114.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement114.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.tileItem2.Elements.Add(tileItemElement112);
            this.tileItem2.Elements.Add(tileItemElement113);
            this.tileItem2.Elements.Add(tileItemElement114);
            tileItemFrame28.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollRight;
            tileItemElement115.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement115.Appearance.Hovered.Options.UseFont = true;
            tileItemElement115.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement115.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement115.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement115.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement115.Appearance.Normal.Options.UseFont = true;
            tileItemElement115.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement115.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement115.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement115.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement115.Appearance.Pressed.Options.UseFont = true;
            tileItemElement115.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement115.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement115.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement115.Appearance.Selected.Options.UseFont = true;
            tileItemElement115.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement115.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement115.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement115.MaxWidth = 160;
            tileItemElement115.Text = "أضافة _ تعديل الأطباء";
            tileItemElement115.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement115.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement116.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement116.Appearance.Hovered.Options.UseFont = true;
            tileItemElement116.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement116.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement116.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement116.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement116.Appearance.Normal.Options.UseFont = true;
            tileItemElement116.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement116.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement116.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement116.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement116.Appearance.Selected.Options.UseFont = true;
            tileItemElement116.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement116.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement116.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement116.MaxWidth = 160;
            tileItemElement116.Text = "";
            tileItemElement116.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement116.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement117.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image5")));
            tileItemElement117.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement117.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement117.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement117.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame28.Elements.Add(tileItemElement115);
            tileItemFrame28.Elements.Add(tileItemElement116);
            tileItemFrame28.Elements.Add(tileItemElement117);
            tileItemFrame29.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.Fade;
            tileItemElement118.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement118.Appearance.Hovered.Options.UseFont = true;
            tileItemElement118.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement118.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement118.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement118.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement118.Appearance.Normal.Options.UseFont = true;
            tileItemElement118.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement118.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement118.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement118.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement118.Appearance.Pressed.Options.UseFont = true;
            tileItemElement118.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement118.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement118.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement118.Appearance.Selected.Options.UseFont = true;
            tileItemElement118.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement118.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement118.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement118.MaxWidth = 160;
            tileItemElement118.Text = "أضافة _ تعديل الأطباء";
            tileItemElement118.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement118.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement119.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement119.Appearance.Hovered.Options.UseFont = true;
            tileItemElement119.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement119.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement119.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement119.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement119.Appearance.Normal.Options.UseFont = true;
            tileItemElement119.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement119.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement119.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement119.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement119.Appearance.Selected.Options.UseFont = true;
            tileItemElement119.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement119.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement119.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement119.MaxWidth = 160;
            tileItemElement119.Text = "";
            tileItemElement119.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement119.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement120.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image6")));
            tileItemElement120.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement120.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement120.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement120.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame29.Elements.Add(tileItemElement118);
            tileItemFrame29.Elements.Add(tileItemElement119);
            tileItemFrame29.Elements.Add(tileItemElement120);
            tileItemFrame30.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.SegmentedFade;
            tileItemElement121.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement121.Appearance.Hovered.Options.UseFont = true;
            tileItemElement121.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement121.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement121.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement121.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement121.Appearance.Normal.Options.UseFont = true;
            tileItemElement121.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement121.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement121.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement121.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement121.Appearance.Pressed.Options.UseFont = true;
            tileItemElement121.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement121.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement121.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement121.Appearance.Selected.Options.UseFont = true;
            tileItemElement121.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement121.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement121.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement121.MaxWidth = 160;
            tileItemElement121.Text = "أضافة _ تعديل الأطباء";
            tileItemElement121.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement121.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement122.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement122.Appearance.Hovered.Options.UseFont = true;
            tileItemElement122.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement122.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement122.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement122.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement122.Appearance.Normal.Options.UseFont = true;
            tileItemElement122.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement122.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement122.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement122.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement122.Appearance.Selected.Options.UseFont = true;
            tileItemElement122.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement122.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement122.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement122.MaxWidth = 160;
            tileItemElement122.Text = "";
            tileItemElement122.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement122.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement123.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image7")));
            tileItemElement123.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement123.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement123.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement123.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame30.Elements.Add(tileItemElement121);
            tileItemFrame30.Elements.Add(tileItemElement122);
            tileItemFrame30.Elements.Add(tileItemElement123);
            tileItemElement124.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement124.Appearance.Hovered.Options.UseFont = true;
            tileItemElement124.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement124.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement124.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement124.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement124.Appearance.Normal.Options.UseFont = true;
            tileItemElement124.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement124.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement124.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement124.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement124.Appearance.Pressed.Options.UseFont = true;
            tileItemElement124.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement124.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement124.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement124.Appearance.Selected.Options.UseFont = true;
            tileItemElement124.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement124.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement124.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement124.MaxWidth = 160;
            tileItemElement124.Text = "أضافة _ تعديل الأطباء";
            tileItemElement124.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement124.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement125.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement125.Appearance.Hovered.Options.UseFont = true;
            tileItemElement125.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement125.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement125.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement125.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement125.Appearance.Normal.Options.UseFont = true;
            tileItemElement125.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement125.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement125.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement125.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement125.Appearance.Selected.Options.UseFont = true;
            tileItemElement125.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement125.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement125.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement125.MaxWidth = 160;
            tileItemElement125.Text = "";
            tileItemElement125.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement125.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement126.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image8")));
            tileItemElement126.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement126.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement126.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement126.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame31.Elements.Add(tileItemElement124);
            tileItemFrame31.Elements.Add(tileItemElement125);
            tileItemFrame31.Elements.Add(tileItemElement126);
            this.tileItem2.Frames.Add(tileItemFrame28);
            this.tileItem2.Frames.Add(tileItemFrame29);
            this.tileItem2.Frames.Add(tileItemFrame30);
            this.tileItem2.Frames.Add(tileItemFrame31);
            this.tileItem2.Id = 2;
            this.tileItem2.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tileItem2.Name = "tileItem2";
            // 
            // tileGroup1
            // 
            this.tileGroup1.Name = "tileGroup1";
            // 
            // tileGroup2
            // 
            this.tileGroup2.Name = "tileGroup2";
            // 
            // btnDOC_LIST
            // 
            tileItemElement127.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement127.Appearance.Hovered.Options.UseFont = true;
            tileItemElement127.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement127.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement127.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement127.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement127.Appearance.Normal.Options.UseFont = true;
            tileItemElement127.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement127.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement127.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement127.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement127.Appearance.Selected.Options.UseFont = true;
            tileItemElement127.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement127.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement127.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement127.MaxWidth = 160;
            tileItemElement127.Text = "قائمة الاطباء";
            tileItemElement127.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement127.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement128.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement128.Appearance.Hovered.Options.UseFont = true;
            tileItemElement128.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement128.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement128.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement128.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement128.Appearance.Normal.Options.UseFont = true;
            tileItemElement128.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement128.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement128.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement128.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement128.Appearance.Selected.Options.UseFont = true;
            tileItemElement128.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement128.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement128.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement128.MaxWidth = 160;
            tileItemElement128.Text = "";
            tileItemElement128.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement128.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement129.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image9")));
            tileItemElement129.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement129.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement129.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement129.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement129.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement129.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            this.btnDOC_LIST.Elements.Add(tileItemElement127);
            this.btnDOC_LIST.Elements.Add(tileItemElement128);
            this.btnDOC_LIST.Elements.Add(tileItemElement129);
            tileItemFrame32.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemElement130.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement130.Appearance.Hovered.Options.UseFont = true;
            tileItemElement130.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement130.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement130.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement130.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement130.Appearance.Normal.Options.UseFont = true;
            tileItemElement130.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement130.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement130.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement130.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement130.Appearance.Selected.Options.UseFont = true;
            tileItemElement130.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement130.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement130.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement130.MaxWidth = 160;
            tileItemElement130.Text = "قائمة الاطباء";
            tileItemElement130.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement130.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement131.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement131.Appearance.Hovered.Options.UseFont = true;
            tileItemElement131.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement131.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement131.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement131.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement131.Appearance.Normal.Options.UseFont = true;
            tileItemElement131.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement131.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement131.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement131.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement131.Appearance.Selected.Options.UseFont = true;
            tileItemElement131.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement131.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement131.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement131.MaxWidth = 160;
            tileItemElement131.Text = "";
            tileItemElement131.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement131.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement132.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image10")));
            tileItemElement132.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement132.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement132.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement132.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement132.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement132.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame32.Elements.Add(tileItemElement130);
            tileItemFrame32.Elements.Add(tileItemElement131);
            tileItemFrame32.Elements.Add(tileItemElement132);
            tileItemFrame33.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemElement133.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement133.Appearance.Hovered.Options.UseFont = true;
            tileItemElement133.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement133.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement133.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement133.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement133.Appearance.Normal.Options.UseFont = true;
            tileItemElement133.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement133.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement133.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement133.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement133.Appearance.Selected.Options.UseFont = true;
            tileItemElement133.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement133.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement133.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement133.MaxWidth = 160;
            tileItemElement133.Text = "قائمة الاطباء";
            tileItemElement133.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement133.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement134.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement134.Appearance.Hovered.Options.UseFont = true;
            tileItemElement134.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement134.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement134.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement134.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement134.Appearance.Normal.Options.UseFont = true;
            tileItemElement134.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement134.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement134.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement134.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement134.Appearance.Selected.Options.UseFont = true;
            tileItemElement134.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement134.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement134.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement134.MaxWidth = 160;
            tileItemElement134.Text = "";
            tileItemElement134.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement134.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement135.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image11")));
            tileItemElement135.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement135.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement135.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement135.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement135.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement135.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame33.Elements.Add(tileItemElement133);
            tileItemFrame33.Elements.Add(tileItemElement134);
            tileItemFrame33.Elements.Add(tileItemElement135);
            tileItemFrame34.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollLeft;
            tileItemElement136.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement136.Appearance.Hovered.Options.UseFont = true;
            tileItemElement136.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement136.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement136.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement136.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement136.Appearance.Normal.Options.UseFont = true;
            tileItemElement136.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement136.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement136.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement136.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI Light", 17F);
            tileItemElement136.Appearance.Selected.Options.UseFont = true;
            tileItemElement136.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement136.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement136.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement136.MaxWidth = 160;
            tileItemElement136.Text = "قائمة الاطباء";
            tileItemElement136.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement136.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement137.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement137.Appearance.Hovered.Options.UseFont = true;
            tileItemElement137.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement137.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement137.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement137.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement137.Appearance.Normal.Options.UseFont = true;
            tileItemElement137.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement137.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement137.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement137.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement137.Appearance.Selected.Options.UseFont = true;
            tileItemElement137.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement137.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement137.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement137.MaxWidth = 160;
            tileItemElement137.Text = "";
            tileItemElement137.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement137.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement138.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image12")));
            tileItemElement138.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement138.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement138.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement138.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement138.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement138.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame34.Elements.Add(tileItemElement136);
            tileItemFrame34.Elements.Add(tileItemElement137);
            tileItemFrame34.Elements.Add(tileItemElement138);
            this.btnDOC_LIST.Frames.Add(tileItemFrame32);
            this.btnDOC_LIST.Frames.Add(tileItemFrame33);
            this.btnDOC_LIST.Frames.Add(tileItemFrame34);
            this.btnDOC_LIST.Id = 0;
            this.btnDOC_LIST.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnDOC_LIST.Name = "btnDOC_LIST";
            // 
            // btnEDIT_DOC
            // 
            tileItemElement139.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement139.Appearance.Hovered.Options.UseFont = true;
            tileItemElement139.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement139.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement139.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement139.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement139.Appearance.Normal.Options.UseFont = true;
            tileItemElement139.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement139.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement139.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement139.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement139.Appearance.Pressed.Options.UseFont = true;
            tileItemElement139.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement139.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement139.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement139.Appearance.Selected.Options.UseFont = true;
            tileItemElement139.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement139.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement139.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement139.MaxWidth = 160;
            tileItemElement139.Text = "أضافة _ تعديل الأطباء";
            tileItemElement139.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement139.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement140.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement140.Appearance.Hovered.Options.UseFont = true;
            tileItemElement140.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement140.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement140.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement140.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement140.Appearance.Normal.Options.UseFont = true;
            tileItemElement140.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement140.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement140.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement140.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement140.Appearance.Selected.Options.UseFont = true;
            tileItemElement140.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement140.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement140.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement140.MaxWidth = 160;
            tileItemElement140.Text = "";
            tileItemElement140.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement140.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement141.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image13")));
            tileItemElement141.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement141.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement141.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement141.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.btnEDIT_DOC.Elements.Add(tileItemElement139);
            this.btnEDIT_DOC.Elements.Add(tileItemElement140);
            this.btnEDIT_DOC.Elements.Add(tileItemElement141);
            tileItemFrame35.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollRight;
            tileItemElement142.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement142.Appearance.Hovered.Options.UseFont = true;
            tileItemElement142.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement142.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement142.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement142.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement142.Appearance.Normal.Options.UseFont = true;
            tileItemElement142.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement142.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement142.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement142.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement142.Appearance.Pressed.Options.UseFont = true;
            tileItemElement142.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement142.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement142.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement142.Appearance.Selected.Options.UseFont = true;
            tileItemElement142.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement142.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement142.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement142.MaxWidth = 160;
            tileItemElement142.Text = "أضافة _ تعديل الأطباء";
            tileItemElement142.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement142.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement143.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement143.Appearance.Hovered.Options.UseFont = true;
            tileItemElement143.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement143.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement143.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement143.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement143.Appearance.Normal.Options.UseFont = true;
            tileItemElement143.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement143.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement143.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement143.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement143.Appearance.Selected.Options.UseFont = true;
            tileItemElement143.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement143.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement143.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement143.MaxWidth = 160;
            tileItemElement143.Text = "";
            tileItemElement143.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement143.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement144.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image14")));
            tileItemElement144.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement144.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement144.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement144.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame35.Elements.Add(tileItemElement142);
            tileItemFrame35.Elements.Add(tileItemElement143);
            tileItemFrame35.Elements.Add(tileItemElement144);
            tileItemFrame36.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.Fade;
            tileItemElement145.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement145.Appearance.Hovered.Options.UseFont = true;
            tileItemElement145.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement145.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement145.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement145.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement145.Appearance.Normal.Options.UseFont = true;
            tileItemElement145.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement145.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement145.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement145.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement145.Appearance.Pressed.Options.UseFont = true;
            tileItemElement145.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement145.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement145.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement145.Appearance.Selected.Options.UseFont = true;
            tileItemElement145.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement145.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement145.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement145.MaxWidth = 160;
            tileItemElement145.Text = "أضافة _ تعديل الأطباء";
            tileItemElement145.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement145.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement146.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement146.Appearance.Hovered.Options.UseFont = true;
            tileItemElement146.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement146.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement146.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement146.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement146.Appearance.Normal.Options.UseFont = true;
            tileItemElement146.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement146.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement146.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement146.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement146.Appearance.Selected.Options.UseFont = true;
            tileItemElement146.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement146.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement146.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement146.MaxWidth = 160;
            tileItemElement146.Text = "";
            tileItemElement146.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement146.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement147.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image15")));
            tileItemElement147.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement147.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement147.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement147.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame36.Elements.Add(tileItemElement145);
            tileItemFrame36.Elements.Add(tileItemElement146);
            tileItemFrame36.Elements.Add(tileItemElement147);
            tileItemFrame37.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.SegmentedFade;
            tileItemElement148.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement148.Appearance.Hovered.Options.UseFont = true;
            tileItemElement148.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement148.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement148.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement148.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement148.Appearance.Normal.Options.UseFont = true;
            tileItemElement148.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement148.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement148.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement148.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement148.Appearance.Pressed.Options.UseFont = true;
            tileItemElement148.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement148.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement148.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement148.Appearance.Selected.Options.UseFont = true;
            tileItemElement148.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement148.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement148.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement148.MaxWidth = 160;
            tileItemElement148.Text = "أضافة _ تعديل الأطباء";
            tileItemElement148.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement148.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement149.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement149.Appearance.Hovered.Options.UseFont = true;
            tileItemElement149.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement149.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement149.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement149.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement149.Appearance.Normal.Options.UseFont = true;
            tileItemElement149.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement149.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement149.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement149.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement149.Appearance.Selected.Options.UseFont = true;
            tileItemElement149.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement149.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement149.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement149.MaxWidth = 160;
            tileItemElement149.Text = "";
            tileItemElement149.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement149.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement150.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image16")));
            tileItemElement150.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement150.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement150.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement150.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame37.Elements.Add(tileItemElement148);
            tileItemFrame37.Elements.Add(tileItemElement149);
            tileItemFrame37.Elements.Add(tileItemElement150);
            tileItemElement151.Appearance.Hovered.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement151.Appearance.Hovered.Options.UseFont = true;
            tileItemElement151.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement151.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement151.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement151.Appearance.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement151.Appearance.Normal.Options.UseFont = true;
            tileItemElement151.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement151.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement151.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement151.Appearance.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement151.Appearance.Pressed.Options.UseFont = true;
            tileItemElement151.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement151.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement151.Appearance.Selected.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement151.Appearance.Selected.Options.UseFont = true;
            tileItemElement151.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement151.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement151.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement151.MaxWidth = 160;
            tileItemElement151.Text = "أضافة _ تعديل الأطباء";
            tileItemElement151.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement151.TextLocation = new System.Drawing.Point(75, 0);
            tileItemElement152.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement152.Appearance.Hovered.Options.UseFont = true;
            tileItemElement152.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement152.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement152.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement152.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement152.Appearance.Normal.Options.UseFont = true;
            tileItemElement152.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement152.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement152.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement152.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement152.Appearance.Selected.Options.UseFont = true;
            tileItemElement152.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement152.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement152.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement152.MaxWidth = 160;
            tileItemElement152.Text = "";
            tileItemElement152.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement152.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement153.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image17")));
            tileItemElement153.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement153.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement153.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement153.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame38.Elements.Add(tileItemElement151);
            tileItemFrame38.Elements.Add(tileItemElement152);
            tileItemFrame38.Elements.Add(tileItemElement153);
            this.btnEDIT_DOC.Frames.Add(tileItemFrame35);
            this.btnEDIT_DOC.Frames.Add(tileItemFrame36);
            this.btnEDIT_DOC.Frames.Add(tileItemFrame37);
            this.btnEDIT_DOC.Frames.Add(tileItemFrame38);
            this.btnEDIT_DOC.Id = 2;
            this.btnEDIT_DOC.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnEDIT_DOC.Name = "btnEDIT_DOC";
            // 
            // tileControl1
            // 
            this.tileControl1.AllowItemHover = true;
            this.tileControl1.AllowSelectedItem = true;
            this.tileControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Style3D;
            this.tileControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tileControl1.Groups.Add(this.tileGroup3);
            this.tileControl1.Groups.Add(this.tileGroup4);
            this.tileControl1.IndentBetweenGroups = 10;
            this.tileControl1.Location = new System.Drawing.Point(0, 0);
            this.tileControl1.MaxId = 6;
            this.tileControl1.Name = "tileControl1";
            this.tileControl1.Size = new System.Drawing.Size(806, 382);
            this.tileControl1.TabIndex = 3;
            this.tileControl1.Text = "tileControl1";
            // 
            // tileGroup3
            // 
            this.tileGroup3.Items.Add(this.btnSTOCK);
            this.tileGroup3.Items.Add(this.btnSTOCK_ADD);
            this.tileGroup3.Name = "tileGroup3";
            // 
            // btnSTOCK
            // 
            this.btnSTOCK.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.btnSTOCK.AppearanceItem.Normal.Options.UseBackColor = true;
            tileItemElement154.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement154.Appearance.Hovered.Options.UseFont = true;
            tileItemElement154.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement154.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement154.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement154.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement154.Appearance.Normal.Options.UseFont = true;
            tileItemElement154.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement154.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement154.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement154.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement154.Appearance.Pressed.Options.UseFont = true;
            tileItemElement154.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement154.Appearance.Selected.Options.UseFont = true;
            tileItemElement154.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement154.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement154.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement154.MaxWidth = 160;
            tileItemElement154.Text = "تعديل_الخزنة";
            tileItemElement154.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement154.TextLocation = new System.Drawing.Point(70, 0);
            tileItemElement155.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement155.Appearance.Hovered.Options.UseFont = true;
            tileItemElement155.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement155.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement155.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement155.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement155.Appearance.Normal.Options.UseFont = true;
            tileItemElement155.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement155.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement155.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement155.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement155.Appearance.Pressed.Options.UseFont = true;
            tileItemElement155.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement155.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement155.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement155.Appearance.Selected.Options.UseFont = true;
            tileItemElement155.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement155.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement155.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement155.MaxWidth = 160;
            tileItemElement155.Text = "أضافة";
            tileItemElement155.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement155.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement156.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image18")));
            tileItemElement156.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement156.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement156.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement156.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement156.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement156.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            this.btnSTOCK.Elements.Add(tileItemElement154);
            this.btnSTOCK.Elements.Add(tileItemElement155);
            this.btnSTOCK.Elements.Add(tileItemElement156);
            tileItemFrame39.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame39.Appearance.BackColor = System.Drawing.Color.Teal;
            tileItemFrame39.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame39.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame39.Appearance.Options.UseBackColor = true;
            tileItemFrame39.Appearance.Options.UseBorderColor = true;
            tileItemElement157.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement157.Appearance.Hovered.Options.UseFont = true;
            tileItemElement157.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement157.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement157.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement157.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement157.Appearance.Normal.Options.UseFont = true;
            tileItemElement157.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement157.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement157.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement157.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement157.Appearance.Pressed.Options.UseFont = true;
            tileItemElement157.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement157.Appearance.Selected.Options.UseFont = true;
            tileItemElement157.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement157.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement157.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement157.MaxWidth = 160;
            tileItemElement157.Text = "تعديل_الخزنة";
            tileItemElement157.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement157.TextLocation = new System.Drawing.Point(70, 0);
            tileItemElement158.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement158.Appearance.Hovered.Options.UseFont = true;
            tileItemElement158.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement158.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement158.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement158.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement158.Appearance.Normal.Options.UseFont = true;
            tileItemElement158.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement158.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement158.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement158.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement158.Appearance.Pressed.Options.UseFont = true;
            tileItemElement158.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement158.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement158.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement158.Appearance.Selected.Options.UseFont = true;
            tileItemElement158.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement158.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement158.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement158.MaxWidth = 160;
            tileItemElement158.Text = "أضافة";
            tileItemElement158.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement158.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement159.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image19")));
            tileItemElement159.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement159.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement159.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement159.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement159.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement159.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame39.Elements.Add(tileItemElement157);
            tileItemFrame39.Elements.Add(tileItemElement158);
            tileItemFrame39.Elements.Add(tileItemElement159);
            tileItemFrame40.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame40.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame40.Appearance.BackColor2 = System.Drawing.Color.Teal;
            tileItemFrame40.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame40.Appearance.Options.UseBackColor = true;
            tileItemFrame40.Appearance.Options.UseBorderColor = true;
            tileItemElement160.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement160.Appearance.Hovered.Options.UseFont = true;
            tileItemElement160.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement160.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement160.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement160.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement160.Appearance.Normal.Options.UseFont = true;
            tileItemElement160.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement160.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement160.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement160.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement160.Appearance.Pressed.Options.UseFont = true;
            tileItemElement160.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement160.Appearance.Selected.Options.UseFont = true;
            tileItemElement160.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement160.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement160.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement160.MaxWidth = 160;
            tileItemElement160.Text = "تعديل_الخزنة";
            tileItemElement160.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement160.TextLocation = new System.Drawing.Point(70, 0);
            tileItemElement161.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement161.Appearance.Hovered.Options.UseFont = true;
            tileItemElement161.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement161.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement161.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement161.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement161.Appearance.Normal.Options.UseFont = true;
            tileItemElement161.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement161.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement161.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement161.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement161.Appearance.Pressed.Options.UseFont = true;
            tileItemElement161.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement161.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement161.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement161.Appearance.Selected.Options.UseFont = true;
            tileItemElement161.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement161.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement161.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement161.MaxWidth = 160;
            tileItemElement161.Text = "أضافة";
            tileItemElement161.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement161.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement162.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image20")));
            tileItemElement162.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement162.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement162.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement162.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement162.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement162.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame40.Elements.Add(tileItemElement160);
            tileItemFrame40.Elements.Add(tileItemElement161);
            tileItemFrame40.Elements.Add(tileItemElement162);
            this.btnSTOCK.Frames.Add(tileItemFrame39);
            this.btnSTOCK.Frames.Add(tileItemFrame40);
            this.btnSTOCK.Id = 0;
            this.btnSTOCK.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnSTOCK.Name = "btnSTOCK";
            this.btnSTOCK.Tag = "frmSTOCK";
            this.btnSTOCK.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnSTOCK_ItemClick);
            // 
            // btnSTOCK_ADD
            // 
            this.btnSTOCK_ADD.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.btnSTOCK_ADD.AppearanceItem.Normal.Options.UseBackColor = true;
            this.btnSTOCK_ADD.CurrentFrameIndex = 1;
            tileItemElement163.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement163.Appearance.Hovered.Options.UseFont = true;
            tileItemElement163.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement163.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement163.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement163.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement163.Appearance.Normal.Options.UseFont = true;
            tileItemElement163.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement163.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement163.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement163.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement163.Appearance.Pressed.Options.UseFont = true;
            tileItemElement163.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement163.Appearance.Selected.Options.UseFont = true;
            tileItemElement163.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement163.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement163.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement163.MaxWidth = 160;
            tileItemElement163.Text = "ايداع في الخزنة";
            tileItemElement163.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement163.TextLocation = new System.Drawing.Point(75, 15);
            tileItemElement164.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement164.Appearance.Hovered.Options.UseFont = true;
            tileItemElement164.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement164.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement164.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement164.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement164.Appearance.Normal.Options.UseFont = true;
            tileItemElement164.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement164.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement164.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement164.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement164.Appearance.Selected.Options.UseFont = true;
            tileItemElement164.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement164.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement164.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement164.MaxWidth = 160;
            tileItemElement164.Text = "";
            tileItemElement164.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement164.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement165.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image21")));
            tileItemElement165.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement165.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement165.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement165.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.btnSTOCK_ADD.Elements.Add(tileItemElement163);
            this.btnSTOCK_ADD.Elements.Add(tileItemElement164);
            this.btnSTOCK_ADD.Elements.Add(tileItemElement165);
            tileItemFrame41.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame41.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame41.Appearance.BackColor2 = System.Drawing.Color.Teal;
            tileItemFrame41.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame41.Appearance.Options.UseBackColor = true;
            tileItemFrame41.Appearance.Options.UseBorderColor = true;
            tileItemElement166.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement166.Appearance.Hovered.Options.UseFont = true;
            tileItemElement166.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement166.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement166.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement166.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement166.Appearance.Normal.Options.UseFont = true;
            tileItemElement166.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement166.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement166.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement166.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement166.Appearance.Pressed.Options.UseFont = true;
            tileItemElement166.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement166.Appearance.Selected.Options.UseFont = true;
            tileItemElement166.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement166.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement166.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement166.MaxWidth = 160;
            tileItemElement166.Text = "ايداع في الخزنة";
            tileItemElement166.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement166.TextLocation = new System.Drawing.Point(75, 15);
            tileItemElement167.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement167.Appearance.Hovered.Options.UseFont = true;
            tileItemElement167.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement167.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement167.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement167.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement167.Appearance.Normal.Options.UseFont = true;
            tileItemElement167.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement167.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement167.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement167.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement167.Appearance.Selected.Options.UseFont = true;
            tileItemElement167.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement167.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement167.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement167.MaxWidth = 160;
            tileItemElement167.Text = "";
            tileItemElement167.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement167.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement168.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image22")));
            tileItemElement168.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement168.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement168.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement168.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame41.Elements.Add(tileItemElement166);
            tileItemFrame41.Elements.Add(tileItemElement167);
            tileItemFrame41.Elements.Add(tileItemElement168);
            tileItemFrame42.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame42.Appearance.BackColor = System.Drawing.Color.Teal;
            tileItemFrame42.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame42.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame42.Appearance.Options.UseBackColor = true;
            tileItemFrame42.Appearance.Options.UseBorderColor = true;
            tileItemElement169.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement169.Appearance.Hovered.Options.UseFont = true;
            tileItemElement169.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement169.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement169.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement169.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement169.Appearance.Normal.Options.UseFont = true;
            tileItemElement169.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement169.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement169.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement169.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement169.Appearance.Pressed.Options.UseFont = true;
            tileItemElement169.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement169.Appearance.Selected.Options.UseFont = true;
            tileItemElement169.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement169.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement169.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement169.MaxWidth = 160;
            tileItemElement169.Text = "ايداع في الخزنة";
            tileItemElement169.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement169.TextLocation = new System.Drawing.Point(75, 15);
            tileItemElement170.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement170.Appearance.Hovered.Options.UseFont = true;
            tileItemElement170.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement170.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement170.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement170.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement170.Appearance.Normal.Options.UseFont = true;
            tileItemElement170.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement170.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement170.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement170.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement170.Appearance.Selected.Options.UseFont = true;
            tileItemElement170.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement170.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement170.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement170.MaxWidth = 160;
            tileItemElement170.Text = "";
            tileItemElement170.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement170.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement171.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image23")));
            tileItemElement171.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement171.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement171.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement171.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame42.Elements.Add(tileItemElement169);
            tileItemFrame42.Elements.Add(tileItemElement170);
            tileItemFrame42.Elements.Add(tileItemElement171);
            this.btnSTOCK_ADD.Frames.Add(tileItemFrame41);
            this.btnSTOCK_ADD.Frames.Add(tileItemFrame42);
            this.btnSTOCK_ADD.Id = 3;
            this.btnSTOCK_ADD.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnSTOCK_ADD.Name = "btnSTOCK_ADD";
            this.btnSTOCK_ADD.Tag = "frmStock_AddMoney";
            this.btnSTOCK_ADD.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnSTOCK_ADD_ItemClick);
            // 
            // tileGroup4
            // 
            this.tileGroup4.Items.Add(this.btnSTOCK_PULL);
            this.tileGroup4.Items.Add(this.btnADD_REPORT);
            this.tileGroup4.Items.Add(this.btnPULL_REPORT);
            this.tileGroup4.Name = "tileGroup4";
            // 
            // btnSTOCK_PULL
            // 
            this.btnSTOCK_PULL.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSTOCK_PULL.AppearanceItem.Hovered.Options.UseFont = true;
            this.btnSTOCK_PULL.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.btnSTOCK_PULL.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.btnSTOCK_PULL.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.btnSTOCK_PULL.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSTOCK_PULL.AppearanceItem.Normal.Options.UseBackColor = true;
            this.btnSTOCK_PULL.AppearanceItem.Normal.Options.UseFont = true;
            this.btnSTOCK_PULL.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.btnSTOCK_PULL.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.btnSTOCK_PULL.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSTOCK_PULL.AppearanceItem.Pressed.Options.UseFont = true;
            this.btnSTOCK_PULL.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.btnSTOCK_PULL.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.btnSTOCK_PULL.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSTOCK_PULL.AppearanceItem.Selected.Options.UseFont = true;
            this.btnSTOCK_PULL.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.btnSTOCK_PULL.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.btnSTOCK_PULL.CurrentFrameIndex = 1;
            tileItemElement172.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement172.Appearance.Hovered.Options.UseFont = true;
            tileItemElement172.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement172.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement172.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement172.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement172.Appearance.Normal.Options.UseFont = true;
            tileItemElement172.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement172.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement172.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement172.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement172.Appearance.Pressed.Options.UseFont = true;
            tileItemElement172.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement172.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement172.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement172.Appearance.Selected.Options.UseFont = true;
            tileItemElement172.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement172.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement172.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement172.MaxWidth = 160;
            tileItemElement172.Text = "";
            tileItemElement172.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement172.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement173.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement173.Appearance.Hovered.Options.UseFont = true;
            tileItemElement173.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement173.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement173.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement173.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement173.Appearance.Normal.Options.UseFont = true;
            tileItemElement173.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement173.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement173.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement173.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement173.Appearance.Pressed.Options.UseFont = true;
            tileItemElement173.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement173.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement173.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement173.Appearance.Selected.Options.UseFont = true;
            tileItemElement173.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement173.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement173.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement173.MaxWidth = 160;
            tileItemElement173.Text = "سحب من الخزنة";
            tileItemElement173.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement173.TextLocation = new System.Drawing.Point(75, 20);
            tileItemElement174.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image24")));
            tileItemElement174.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement174.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement174.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement174.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.btnSTOCK_PULL.Elements.Add(tileItemElement172);
            this.btnSTOCK_PULL.Elements.Add(tileItemElement173);
            this.btnSTOCK_PULL.Elements.Add(tileItemElement174);
            tileItemFrame43.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame43.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame43.Appearance.BackColor2 = System.Drawing.Color.Teal;
            tileItemFrame43.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame43.Appearance.Options.UseBackColor = true;
            tileItemFrame43.Appearance.Options.UseBorderColor = true;
            tileItemElement175.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement175.Appearance.Hovered.Options.UseFont = true;
            tileItemElement175.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement175.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement175.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement175.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement175.Appearance.Normal.Options.UseFont = true;
            tileItemElement175.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement175.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement175.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement175.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement175.Appearance.Pressed.Options.UseFont = true;
            tileItemElement175.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement175.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement175.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement175.Appearance.Selected.Options.UseFont = true;
            tileItemElement175.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement175.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement175.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement175.MaxWidth = 160;
            tileItemElement175.Text = "";
            tileItemElement175.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement175.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement176.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement176.Appearance.Hovered.Options.UseFont = true;
            tileItemElement176.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement176.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement176.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement176.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement176.Appearance.Normal.Options.UseFont = true;
            tileItemElement176.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement176.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement176.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement176.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement176.Appearance.Pressed.Options.UseFont = true;
            tileItemElement176.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement176.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement176.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement176.Appearance.Selected.Options.UseFont = true;
            tileItemElement176.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement176.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement176.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement176.MaxWidth = 160;
            tileItemElement176.Text = "سحب من الخزنة";
            tileItemElement176.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement176.TextLocation = new System.Drawing.Point(75, 20);
            tileItemElement177.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image25")));
            tileItemElement177.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement177.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement177.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement177.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame43.Elements.Add(tileItemElement175);
            tileItemFrame43.Elements.Add(tileItemElement176);
            tileItemFrame43.Elements.Add(tileItemElement177);
            tileItemFrame44.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame44.Appearance.BackColor = System.Drawing.Color.Teal;
            tileItemFrame44.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame44.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame44.Appearance.Options.UseBackColor = true;
            tileItemFrame44.Appearance.Options.UseBorderColor = true;
            tileItemElement178.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement178.Appearance.Hovered.Options.UseFont = true;
            tileItemElement178.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement178.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement178.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement178.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement178.Appearance.Normal.Options.UseFont = true;
            tileItemElement178.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement178.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement178.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement178.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement178.Appearance.Pressed.Options.UseFont = true;
            tileItemElement178.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement178.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement178.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement178.Appearance.Selected.Options.UseFont = true;
            tileItemElement178.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement178.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement178.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement178.MaxWidth = 160;
            tileItemElement178.Text = "";
            tileItemElement178.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement178.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement179.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement179.Appearance.Hovered.Options.UseFont = true;
            tileItemElement179.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement179.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement179.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement179.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement179.Appearance.Normal.Options.UseFont = true;
            tileItemElement179.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement179.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement179.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement179.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement179.Appearance.Pressed.Options.UseFont = true;
            tileItemElement179.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement179.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement179.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement179.Appearance.Selected.Options.UseFont = true;
            tileItemElement179.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement179.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement179.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement179.MaxWidth = 160;
            tileItemElement179.Text = "سحب من الخزنة";
            tileItemElement179.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement179.TextLocation = new System.Drawing.Point(75, 20);
            tileItemElement180.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image26")));
            tileItemElement180.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement180.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement180.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement180.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame44.Elements.Add(tileItemElement178);
            tileItemFrame44.Elements.Add(tileItemElement179);
            tileItemFrame44.Elements.Add(tileItemElement180);
            this.btnSTOCK_PULL.Frames.Add(tileItemFrame43);
            this.btnSTOCK_PULL.Frames.Add(tileItemFrame44);
            this.btnSTOCK_PULL.Id = 2;
            this.btnSTOCK_PULL.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnSTOCK_PULL.Name = "btnSTOCK_PULL";
            this.btnSTOCK_PULL.Tag = "Frm_Stock_PullMoney";
            this.btnSTOCK_PULL.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnSTOCK_PULL_ItemClick);
            // 
            // btnADD_REPORT
            // 
            this.btnADD_REPORT.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.btnADD_REPORT.AppearanceItem.Normal.Options.UseBackColor = true;
            tileItemElement181.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement181.Appearance.Hovered.Options.UseFont = true;
            tileItemElement181.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement181.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement181.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement181.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement181.Appearance.Normal.Options.UseFont = true;
            tileItemElement181.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement181.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement181.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement181.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement181.Appearance.Pressed.Options.UseFont = true;
            tileItemElement181.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement181.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement181.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement181.Appearance.Selected.Options.UseFont = true;
            tileItemElement181.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement181.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement181.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement181.MaxWidth = 160;
            tileItemElement181.Text = "تقارير الايداع";
            tileItemElement181.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement181.TextLocation = new System.Drawing.Point(75, 20);
            tileItemElement182.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement182.Appearance.Hovered.Options.UseFont = true;
            tileItemElement182.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement182.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement182.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement182.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement182.Appearance.Normal.Options.UseFont = true;
            tileItemElement182.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement182.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement182.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement182.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement182.Appearance.Pressed.Options.UseFont = true;
            tileItemElement182.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement182.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement182.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement182.Appearance.Selected.Options.UseFont = true;
            tileItemElement182.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement182.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement182.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement182.MaxWidth = 160;
            tileItemElement182.Text = "";
            tileItemElement182.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement182.TextLocation = new System.Drawing.Point(75, 35);
            tileItemElement183.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image27")));
            tileItemElement183.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement183.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement183.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement183.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.btnADD_REPORT.Elements.Add(tileItemElement181);
            this.btnADD_REPORT.Elements.Add(tileItemElement182);
            this.btnADD_REPORT.Elements.Add(tileItemElement183);
            tileItemFrame45.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame45.Appearance.BackColor = System.Drawing.Color.Teal;
            tileItemFrame45.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame45.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame45.Appearance.Options.UseBackColor = true;
            tileItemFrame45.Appearance.Options.UseBorderColor = true;
            tileItemElement184.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement184.Appearance.Hovered.Options.UseFont = true;
            tileItemElement184.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement184.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement184.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement184.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement184.Appearance.Normal.Options.UseFont = true;
            tileItemElement184.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement184.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement184.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement184.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement184.Appearance.Pressed.Options.UseFont = true;
            tileItemElement184.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement184.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement184.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement184.Appearance.Selected.Options.UseFont = true;
            tileItemElement184.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement184.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement184.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement184.MaxWidth = 160;
            tileItemElement184.Text = "تقارير الايداع";
            tileItemElement184.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement184.TextLocation = new System.Drawing.Point(75, 20);
            tileItemElement185.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement185.Appearance.Hovered.Options.UseFont = true;
            tileItemElement185.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement185.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement185.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement185.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement185.Appearance.Normal.Options.UseFont = true;
            tileItemElement185.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement185.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement185.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement185.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement185.Appearance.Pressed.Options.UseFont = true;
            tileItemElement185.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement185.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement185.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement185.Appearance.Selected.Options.UseFont = true;
            tileItemElement185.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement185.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement185.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement185.MaxWidth = 160;
            tileItemElement185.Text = "";
            tileItemElement185.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement185.TextLocation = new System.Drawing.Point(75, 35);
            tileItemElement186.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image28")));
            tileItemElement186.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement186.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement186.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement186.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame45.Elements.Add(tileItemElement184);
            tileItemFrame45.Elements.Add(tileItemElement185);
            tileItemFrame45.Elements.Add(tileItemElement186);
            tileItemFrame46.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame46.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame46.Appearance.BackColor2 = System.Drawing.Color.Teal;
            tileItemFrame46.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame46.Appearance.Options.UseBackColor = true;
            tileItemFrame46.Appearance.Options.UseBorderColor = true;
            tileItemElement187.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement187.Appearance.Hovered.Options.UseFont = true;
            tileItemElement187.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement187.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement187.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement187.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement187.Appearance.Normal.Options.UseFont = true;
            tileItemElement187.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement187.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement187.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement187.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement187.Appearance.Pressed.Options.UseFont = true;
            tileItemElement187.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement187.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement187.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement187.Appearance.Selected.Options.UseFont = true;
            tileItemElement187.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement187.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement187.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement187.MaxWidth = 160;
            tileItemElement187.Text = "تقارير الايداع";
            tileItemElement187.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement187.TextLocation = new System.Drawing.Point(75, 20);
            tileItemElement188.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement188.Appearance.Hovered.Options.UseFont = true;
            tileItemElement188.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement188.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement188.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement188.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement188.Appearance.Normal.Options.UseFont = true;
            tileItemElement188.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement188.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement188.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement188.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement188.Appearance.Pressed.Options.UseFont = true;
            tileItemElement188.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement188.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement188.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 11.25F);
            tileItemElement188.Appearance.Selected.Options.UseFont = true;
            tileItemElement188.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement188.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement188.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement188.MaxWidth = 160;
            tileItemElement188.Text = "";
            tileItemElement188.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement188.TextLocation = new System.Drawing.Point(75, 35);
            tileItemElement189.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image29")));
            tileItemElement189.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement189.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement189.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement189.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame46.Elements.Add(tileItemElement187);
            tileItemFrame46.Elements.Add(tileItemElement188);
            tileItemFrame46.Elements.Add(tileItemElement189);
            this.btnADD_REPORT.Frames.Add(tileItemFrame45);
            this.btnADD_REPORT.Frames.Add(tileItemFrame46);
            this.btnADD_REPORT.Id = 4;
            this.btnADD_REPORT.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnADD_REPORT.Name = "btnADD_REPORT";
            this.btnADD_REPORT.Tag = "frm_Stock_AddMoney_List";
            this.btnADD_REPORT.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnADD_REPORT_ItemClick);
            // 
            // btnPULL_REPORT
            // 
            tileItemElement190.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement190.Appearance.Hovered.Options.UseFont = true;
            tileItemElement190.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement190.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement190.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement190.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement190.Appearance.Normal.Options.UseFont = true;
            tileItemElement190.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement190.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement190.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement190.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement190.Appearance.Pressed.Options.UseFont = true;
            tileItemElement190.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement190.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement190.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement190.Appearance.Selected.Options.UseFont = true;
            tileItemElement190.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement190.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement190.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement190.MaxWidth = 160;
            tileItemElement190.Text = "تقارير السحب";
            tileItemElement190.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement190.TextLocation = new System.Drawing.Point(75, 20);
            tileItemElement191.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement191.Appearance.Hovered.Options.UseFont = true;
            tileItemElement191.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement191.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement191.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement191.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement191.Appearance.Normal.Options.UseFont = true;
            tileItemElement191.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement191.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement191.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement191.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement191.Appearance.Selected.Options.UseFont = true;
            tileItemElement191.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement191.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement191.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement191.MaxWidth = 160;
            tileItemElement191.Text = "";
            tileItemElement191.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement191.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement192.Appearance.Disabled.BackColor = System.Drawing.Color.Black;
            tileItemElement192.Appearance.Disabled.BackColor2 = System.Drawing.Color.Teal;
            tileItemElement192.Appearance.Disabled.BorderColor = System.Drawing.Color.Red;
            tileItemElement192.Appearance.Disabled.Options.UseBackColor = true;
            tileItemElement192.Appearance.Disabled.Options.UseBorderColor = true;
            tileItemElement192.Appearance.Normal.BackColor = System.Drawing.Color.Black;
            tileItemElement192.Appearance.Normal.BackColor2 = System.Drawing.Color.Teal;
            tileItemElement192.Appearance.Normal.BorderColor = System.Drawing.Color.Red;
            tileItemElement192.Appearance.Normal.Options.UseBackColor = true;
            tileItemElement192.Appearance.Normal.Options.UseBorderColor = true;
            tileItemElement192.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image30")));
            tileItemElement192.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement192.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement192.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement192.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.btnPULL_REPORT.Elements.Add(tileItemElement190);
            this.btnPULL_REPORT.Elements.Add(tileItemElement191);
            this.btnPULL_REPORT.Elements.Add(tileItemElement192);
            tileItemFrame47.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollLeft;
            tileItemFrame47.Appearance.BackColor = System.Drawing.Color.Teal;
            tileItemFrame47.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame47.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame47.Appearance.Options.UseBackColor = true;
            tileItemFrame47.Appearance.Options.UseBorderColor = true;
            tileItemElement193.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement193.Appearance.Hovered.Options.UseFont = true;
            tileItemElement193.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement193.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement193.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement193.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement193.Appearance.Normal.Options.UseFont = true;
            tileItemElement193.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement193.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement193.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement193.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement193.Appearance.Pressed.Options.UseFont = true;
            tileItemElement193.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement193.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement193.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement193.Appearance.Selected.Options.UseFont = true;
            tileItemElement193.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement193.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement193.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement193.MaxWidth = 160;
            tileItemElement193.Text = "تقارير السحب";
            tileItemElement193.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement193.TextLocation = new System.Drawing.Point(75, 20);
            tileItemElement194.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement194.Appearance.Hovered.Options.UseFont = true;
            tileItemElement194.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement194.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement194.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement194.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement194.Appearance.Normal.Options.UseFont = true;
            tileItemElement194.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement194.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement194.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement194.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement194.Appearance.Selected.Options.UseFont = true;
            tileItemElement194.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement194.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement194.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement194.MaxWidth = 160;
            tileItemElement194.Text = "";
            tileItemElement194.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement194.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement195.Appearance.Disabled.BackColor = System.Drawing.Color.Black;
            tileItemElement195.Appearance.Disabled.BackColor2 = System.Drawing.Color.Teal;
            tileItemElement195.Appearance.Disabled.BorderColor = System.Drawing.Color.Red;
            tileItemElement195.Appearance.Disabled.Options.UseBackColor = true;
            tileItemElement195.Appearance.Disabled.Options.UseBorderColor = true;
            tileItemElement195.Appearance.Normal.BackColor = System.Drawing.Color.Black;
            tileItemElement195.Appearance.Normal.BackColor2 = System.Drawing.Color.Teal;
            tileItemElement195.Appearance.Normal.BorderColor = System.Drawing.Color.Red;
            tileItemElement195.Appearance.Normal.Options.UseBackColor = true;
            tileItemElement195.Appearance.Normal.Options.UseBorderColor = true;
            tileItemElement195.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image31")));
            tileItemElement195.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement195.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement195.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement195.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame47.Elements.Add(tileItemElement193);
            tileItemFrame47.Elements.Add(tileItemElement194);
            tileItemFrame47.Elements.Add(tileItemElement195);
            tileItemFrame48.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame48.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame48.Appearance.BackColor2 = System.Drawing.Color.Teal;
            tileItemFrame48.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame48.Appearance.Options.UseBackColor = true;
            tileItemFrame48.Appearance.Options.UseBorderColor = true;
            tileItemElement196.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement196.Appearance.Hovered.Options.UseFont = true;
            tileItemElement196.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement196.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement196.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement196.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement196.Appearance.Normal.Options.UseFont = true;
            tileItemElement196.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement196.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement196.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement196.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement196.Appearance.Pressed.Options.UseFont = true;
            tileItemElement196.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement196.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement196.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement196.Appearance.Selected.Options.UseFont = true;
            tileItemElement196.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement196.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement196.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement196.MaxWidth = 160;
            tileItemElement196.Text = "تقارير السحب";
            tileItemElement196.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement196.TextLocation = new System.Drawing.Point(75, 20);
            tileItemElement197.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement197.Appearance.Hovered.Options.UseFont = true;
            tileItemElement197.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement197.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement197.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement197.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement197.Appearance.Normal.Options.UseFont = true;
            tileItemElement197.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement197.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement197.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement197.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement197.Appearance.Selected.Options.UseFont = true;
            tileItemElement197.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement197.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement197.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement197.MaxWidth = 160;
            tileItemElement197.Text = "";
            tileItemElement197.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement197.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement198.Appearance.Disabled.BackColor = System.Drawing.Color.Black;
            tileItemElement198.Appearance.Disabled.BackColor2 = System.Drawing.Color.Teal;
            tileItemElement198.Appearance.Disabled.BorderColor = System.Drawing.Color.Red;
            tileItemElement198.Appearance.Disabled.Options.UseBackColor = true;
            tileItemElement198.Appearance.Disabled.Options.UseBorderColor = true;
            tileItemElement198.Appearance.Normal.BackColor = System.Drawing.Color.Black;
            tileItemElement198.Appearance.Normal.BackColor2 = System.Drawing.Color.Teal;
            tileItemElement198.Appearance.Normal.BorderColor = System.Drawing.Color.Red;
            tileItemElement198.Appearance.Normal.Options.UseBackColor = true;
            tileItemElement198.Appearance.Normal.Options.UseBorderColor = true;
            tileItemElement198.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image32")));
            tileItemElement198.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement198.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement198.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement198.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame48.Elements.Add(tileItemElement196);
            tileItemFrame48.Elements.Add(tileItemElement197);
            tileItemFrame48.Elements.Add(tileItemElement198);
            this.btnPULL_REPORT.Frames.Add(tileItemFrame47);
            this.btnPULL_REPORT.Frames.Add(tileItemFrame48);
            this.btnPULL_REPORT.Id = 5;
            this.btnPULL_REPORT.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnPULL_REPORT.Name = "btnPULL_REPORT";
            this.btnPULL_REPORT.Tag = "Frm_Stock_PullMoney_List";
            this.btnPULL_REPORT.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnPULL_REPORT_ItemClick);
            // 
            // frmSTOCK_MAIN
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(806, 382);
            this.Controls.Add(this.tileControl1);
            this.Name = "frmSTOCK_MAIN";
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "frmSTOCK_MAIN";
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.TileItem tileItem1;
        private DevExpress.XtraEditors.TileItem tileItem2;
        private DevExpress.XtraEditors.TileGroup tileGroup1;
        private DevExpress.XtraEditors.TileGroup tileGroup2;
        private DevExpress.XtraEditors.TileItem btnDOC_LIST;
        private DevExpress.XtraEditors.TileItem btnEDIT_DOC;
        private DevExpress.XtraEditors.TileControl tileControl1;
        private DevExpress.XtraEditors.TileGroup tileGroup3;
        private DevExpress.XtraEditors.TileItem btnSTOCK;
        private DevExpress.XtraEditors.TileItem btnSTOCK_ADD;
        private DevExpress.XtraEditors.TileGroup tileGroup4;
        private DevExpress.XtraEditors.TileItem btnSTOCK_PULL;
        private DevExpress.XtraEditors.TileItem btnADD_REPORT;
        private DevExpress.XtraEditors.TileItem btnPULL_REPORT;
    }
}