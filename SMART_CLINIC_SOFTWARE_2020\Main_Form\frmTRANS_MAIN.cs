﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.LIST_FORM;

namespace SMART_CLINIC_SOFTWARE_2020.Main_Form
{
    public partial class frmTRANS_MAIN : DevExpress.XtraEditors.XtraForm
    {
        public frmTRANS_MAIN()
        {
            InitializeComponent();
        }

        private void btnTRANS_LIST_ItemClick(object sender, TileItemEventArgs e)
        {
            frmMAIN.GETNEWUSER.OPEN_FORM(((DevExpress.XtraEditors.TileItem)sender).Tag.ToString());
            this.Close();
        }

        private void btnTRANSACTION_ItemClick(object sender, TileItemEventArgs e)
        {
            frmMAIN.GETNEWUSER.OPEN_FORM(((DevExpress.XtraEditors.TileItem)sender).Tag.ToString());
            this.Close();
        }

        private void frmTRANS_MAIN_Load(object sender, EventArgs e)
        {
            if (Classes.clsUSER_PER.PER_DT.Columns.Contains(tileLIST.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][tileLIST.Tag.ToString()]) == 0)
            {
                tileLIST.Visible = false;
            }
            else
            {
                tileLIST.Visible = true;
            }

            if (Classes.clsUSER_PER.PER_DT.Columns.Contains(tileDATA.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][tileDATA.Tag.ToString()]) == 0)
            {
                tileDATA.Visible = false;
            }
            else
            {
                tileDATA.Visible = true;
            }
        }
    }
}