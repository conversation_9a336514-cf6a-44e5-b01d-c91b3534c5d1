﻿namespace SMART_CLINIC_SOFTWARE_2020.Main_Form
{
    partial class frmTRANS_MAIN
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraEditors.TileItemElement tileItemElement1 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement2 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement3 = new DevExpress.XtraEditors.TileItemElement();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmTRANS_MAIN));
            DevExpress.XtraEditors.TileItemFrame tileItemFrame1 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement4 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement5 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement6 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame2 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement7 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement8 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement9 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement10 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement11 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement12 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame3 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement13 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement14 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement15 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemFrame tileItemFrame4 = new DevExpress.XtraEditors.TileItemFrame();
            DevExpress.XtraEditors.TileItemElement tileItemElement16 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement17 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement18 = new DevExpress.XtraEditors.TileItemElement();
            this.tileControl1 = new DevExpress.XtraEditors.TileControl();
            this.tileLIST = new DevExpress.XtraEditors.TileGroup();
            this.btnTRANS_LIST = new DevExpress.XtraEditors.TileItem();
            this.tileDATA = new DevExpress.XtraEditors.TileGroup();
            this.btnTRANSACTION = new DevExpress.XtraEditors.TileItem();
            this.SuspendLayout();
            // 
            // tileControl1
            // 
            this.tileControl1.AllowItemHover = true;
            this.tileControl1.AllowSelectedItem = true;
            this.tileControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Style3D;
            this.tileControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tileControl1.Groups.Add(this.tileLIST);
            this.tileControl1.Groups.Add(this.tileDATA);
            this.tileControl1.IndentBetweenGroups = 10;
            this.tileControl1.IndentBetweenItems = 5;
            this.tileControl1.Location = new System.Drawing.Point(0, 0);
            this.tileControl1.MaxId = 3;
            this.tileControl1.Name = "tileControl1";
            this.tileControl1.Size = new System.Drawing.Size(806, 382);
            this.tileControl1.TabIndex = 3;
            this.tileControl1.Text = "tileControl1";
            // 
            // tileLIST
            // 
            this.tileLIST.Items.Add(this.btnTRANS_LIST);
            this.tileLIST.Name = "tileLIST";
            this.tileLIST.Tag = "قائمة_الحسابات";
            // 
            // btnTRANS_LIST
            // 
            this.btnTRANS_LIST.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.btnTRANS_LIST.AppearanceItem.Normal.Options.UseBackColor = true;
            tileItemElement1.AnimateTransition = DevExpress.Utils.DefaultBoolean.False;
            tileItemElement1.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Hovered.Options.UseFont = true;
            tileItemElement1.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Normal.Options.UseFont = true;
            tileItemElement1.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Pressed.Options.UseFont = true;
            tileItemElement1.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement1.Appearance.Selected.Options.UseFont = true;
            tileItemElement1.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement1.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement1.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.MaxWidth = 160;
            tileItemElement1.Text = "قائمة الحسابات";
            tileItemElement1.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement1.TextLocation = new System.Drawing.Point(75, -23);
            tileItemElement2.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Hovered.Options.UseFont = true;
            tileItemElement2.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Normal.Options.UseFont = true;
            tileItemElement2.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement2.Appearance.Selected.Options.UseFont = true;
            tileItemElement2.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement2.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement2.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement2.MaxWidth = 160;
            tileItemElement2.Text = "";
            tileItemElement2.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement2.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement3.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image")));
            tileItemElement3.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement3.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement3.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement3.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement3.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement3.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            this.btnTRANS_LIST.Elements.Add(tileItemElement1);
            this.btnTRANS_LIST.Elements.Add(tileItemElement2);
            this.btnTRANS_LIST.Elements.Add(tileItemElement3);
            tileItemFrame1.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame1.Appearance.BackColor = System.Drawing.Color.Teal;
            tileItemFrame1.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame1.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame1.Appearance.Options.UseBackColor = true;
            tileItemFrame1.Appearance.Options.UseBorderColor = true;
            tileItemElement4.AnimateTransition = DevExpress.Utils.DefaultBoolean.False;
            tileItemElement4.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Hovered.Options.UseFont = true;
            tileItemElement4.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Normal.Options.UseFont = true;
            tileItemElement4.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Pressed.Options.UseFont = true;
            tileItemElement4.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement4.Appearance.Selected.Options.UseFont = true;
            tileItemElement4.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement4.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement4.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.MaxWidth = 160;
            tileItemElement4.Text = "قائمة الحسابات";
            tileItemElement4.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement4.TextLocation = new System.Drawing.Point(75, -23);
            tileItemElement5.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Hovered.Options.UseFont = true;
            tileItemElement5.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Normal.Options.UseFont = true;
            tileItemElement5.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement5.Appearance.Selected.Options.UseFont = true;
            tileItemElement5.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement5.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement5.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement5.MaxWidth = 160;
            tileItemElement5.Text = "";
            tileItemElement5.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement5.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement6.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image1")));
            tileItemElement6.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement6.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement6.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement6.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement6.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement6.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame1.Elements.Add(tileItemElement4);
            tileItemFrame1.Elements.Add(tileItemElement5);
            tileItemFrame1.Elements.Add(tileItemElement6);
            tileItemFrame2.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame2.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame2.Appearance.BackColor2 = System.Drawing.Color.Teal;
            tileItemFrame2.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame2.Appearance.Options.UseBackColor = true;
            tileItemFrame2.Appearance.Options.UseBorderColor = true;
            tileItemElement7.AnimateTransition = DevExpress.Utils.DefaultBoolean.False;
            tileItemElement7.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Hovered.Options.UseFont = true;
            tileItemElement7.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Normal.Options.UseFont = true;
            tileItemElement7.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Pressed.Options.UseFont = true;
            tileItemElement7.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement7.Appearance.Selected.Options.UseFont = true;
            tileItemElement7.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement7.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement7.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.MaxWidth = 160;
            tileItemElement7.Text = "قائمة الحسابات";
            tileItemElement7.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleLeft;
            tileItemElement7.TextLocation = new System.Drawing.Point(75, -23);
            tileItemElement8.Appearance.Hovered.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Hovered.Options.UseFont = true;
            tileItemElement8.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Hovered.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Normal.Options.UseFont = true;
            tileItemElement8.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Normal.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.Appearance.Selected.Font = new System.Drawing.Font("Segoe UI", 9F);
            tileItemElement8.Appearance.Selected.Options.UseFont = true;
            tileItemElement8.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement8.Appearance.Selected.TextOptions.Trimming = DevExpress.Utils.Trimming.EllipsisCharacter;
            tileItemElement8.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            tileItemElement8.MaxWidth = 160;
            tileItemElement8.Text = "";
            tileItemElement8.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement8.TextLocation = new System.Drawing.Point(75, 27);
            tileItemElement9.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image2")));
            tileItemElement9.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement9.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement9.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement9.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemElement9.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement9.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomLeft;
            tileItemFrame2.Elements.Add(tileItemElement7);
            tileItemFrame2.Elements.Add(tileItemElement8);
            tileItemFrame2.Elements.Add(tileItemElement9);
            this.btnTRANS_LIST.Frames.Add(tileItemFrame1);
            this.btnTRANS_LIST.Frames.Add(tileItemFrame2);
            this.btnTRANS_LIST.Id = 0;
            this.btnTRANS_LIST.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnTRANS_LIST.Name = "btnTRANS_LIST";
            this.btnTRANS_LIST.Tag = "frmTRANSACTION_LIST";
            this.btnTRANS_LIST.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnTRANS_LIST_ItemClick);
            // 
            // tileDATA
            // 
            this.tileDATA.Items.Add(this.btnTRANSACTION);
            this.tileDATA.Name = "tileDATA";
            this.tileDATA.Tag = "بيانات_الحسابات";
            // 
            // btnTRANSACTION
            // 
            this.btnTRANSACTION.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.btnTRANSACTION.AppearanceItem.Normal.Options.UseBackColor = true;
            tileItemElement10.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Hovered.Options.UseFont = true;
            tileItemElement10.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Normal.Options.UseFont = true;
            tileItemElement10.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Pressed.Options.UseFont = true;
            tileItemElement10.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement10.Appearance.Selected.Options.UseFont = true;
            tileItemElement10.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement10.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter;
            tileItemElement10.MaxWidth = 160;
            tileItemElement10.Text = "الحسابات";
            tileItemElement10.TextLocation = new System.Drawing.Point(75, 38);
            tileItemElement11.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement11.Appearance.Hovered.Options.UseFont = true;
            tileItemElement11.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement11.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement11.Appearance.Normal.Options.UseFont = true;
            tileItemElement11.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement11.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement11.Appearance.Pressed.Options.UseFont = true;
            tileItemElement11.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement11.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement11.Appearance.Selected.Options.UseFont = true;
            tileItemElement11.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement11.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement11.MaxWidth = 160;
            tileItemElement11.Text = "أضافة _ تعديل ";
            tileItemElement11.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement11.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement12.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image3")));
            tileItemElement12.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement12.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement12.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement12.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            this.btnTRANSACTION.Elements.Add(tileItemElement10);
            this.btnTRANSACTION.Elements.Add(tileItemElement11);
            this.btnTRANSACTION.Elements.Add(tileItemElement12);
            tileItemFrame3.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollTop;
            tileItemFrame3.Appearance.BackColor = System.Drawing.Color.Black;
            tileItemFrame3.Appearance.BackColor2 = System.Drawing.Color.Teal;
            tileItemFrame3.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame3.Appearance.Options.UseBackColor = true;
            tileItemFrame3.Appearance.Options.UseBorderColor = true;
            tileItemElement13.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Hovered.Options.UseFont = true;
            tileItemElement13.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Normal.Options.UseFont = true;
            tileItemElement13.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Pressed.Options.UseFont = true;
            tileItemElement13.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement13.Appearance.Selected.Options.UseFont = true;
            tileItemElement13.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement13.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement13.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter;
            tileItemElement13.MaxWidth = 160;
            tileItemElement13.Text = "الحسابات";
            tileItemElement13.TextLocation = new System.Drawing.Point(75, 38);
            tileItemElement14.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement14.Appearance.Hovered.Options.UseFont = true;
            tileItemElement14.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement14.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement14.Appearance.Normal.Options.UseFont = true;
            tileItemElement14.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement14.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement14.Appearance.Pressed.Options.UseFont = true;
            tileItemElement14.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement14.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement14.Appearance.Selected.Options.UseFont = true;
            tileItemElement14.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement14.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement14.MaxWidth = 160;
            tileItemElement14.Text = "أضافة _ تعديل ";
            tileItemElement14.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement14.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement15.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image4")));
            tileItemElement15.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement15.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement15.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement15.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame3.Elements.Add(tileItemElement13);
            tileItemFrame3.Elements.Add(tileItemElement14);
            tileItemFrame3.Elements.Add(tileItemElement15);
            tileItemFrame4.Animation = DevExpress.XtraEditors.TileItemContentAnimationType.ScrollDown;
            tileItemFrame4.Appearance.BackColor = System.Drawing.Color.Teal;
            tileItemFrame4.Appearance.BackColor2 = System.Drawing.Color.Black;
            tileItemFrame4.Appearance.BorderColor = System.Drawing.Color.Red;
            tileItemFrame4.Appearance.Options.UseBackColor = true;
            tileItemFrame4.Appearance.Options.UseBorderColor = true;
            tileItemElement16.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Hovered.Options.UseFont = true;
            tileItemElement16.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Normal.Options.UseFont = true;
            tileItemElement16.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Pressed.Options.UseFont = true;
            tileItemElement16.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement16.Appearance.Selected.Options.UseFont = true;
            tileItemElement16.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement16.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement16.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter;
            tileItemElement16.MaxWidth = 160;
            tileItemElement16.Text = "الحسابات";
            tileItemElement16.TextLocation = new System.Drawing.Point(75, 38);
            tileItemElement17.Appearance.Hovered.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement17.Appearance.Hovered.Options.UseFont = true;
            tileItemElement17.Appearance.Hovered.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement17.Appearance.Normal.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement17.Appearance.Normal.Options.UseFont = true;
            tileItemElement17.Appearance.Normal.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement17.Appearance.Pressed.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement17.Appearance.Pressed.Options.UseFont = true;
            tileItemElement17.Appearance.Pressed.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement17.Appearance.Selected.Font = new System.Drawing.Font("Droid Arabic Kufi", 14F);
            tileItemElement17.Appearance.Selected.Options.UseFont = true;
            tileItemElement17.Appearance.Selected.Options.UseTextOptions = true;
            tileItemElement17.Appearance.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement17.MaxWidth = 160;
            tileItemElement17.Text = "أضافة _ تعديل ";
            tileItemElement17.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement17.TextLocation = new System.Drawing.Point(75, 7);
            tileItemElement18.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image5")));
            tileItemElement18.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.Manual;
            tileItemElement18.ImageOptions.ImageLocation = new System.Drawing.Point(4, 8);
            tileItemElement18.ImageOptions.ImageScaleMode = DevExpress.XtraEditors.TileItemImageScaleMode.ZoomOutside;
            tileItemElement18.ImageOptions.ImageSize = new System.Drawing.Size(64, 64);
            tileItemFrame4.Elements.Add(tileItemElement16);
            tileItemFrame4.Elements.Add(tileItemElement17);
            tileItemFrame4.Elements.Add(tileItemElement18);
            this.btnTRANSACTION.Frames.Add(tileItemFrame3);
            this.btnTRANSACTION.Frames.Add(tileItemFrame4);
            this.btnTRANSACTION.Id = 2;
            this.btnTRANSACTION.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.btnTRANSACTION.Name = "btnTRANSACTION";
            this.btnTRANSACTION.Tag = "frmTRANSACTION";
            this.btnTRANSACTION.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.btnTRANSACTION_ItemClick);
            // 
            // frmTRANS_MAIN
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(806, 382);
            this.Controls.Add(this.tileControl1);
            this.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "frmTRANS_MAIN";
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "frmTRANS_MAIN";
            this.Load += new System.EventHandler(this.frmTRANS_MAIN_Load);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.TileControl tileControl1;
        private DevExpress.XtraEditors.TileGroup tileLIST;
        private DevExpress.XtraEditors.TileItem btnTRANS_LIST;
        private DevExpress.XtraEditors.TileGroup tileDATA;
        private DevExpress.XtraEditors.TileItem btnTRANSACTION;
    }
}