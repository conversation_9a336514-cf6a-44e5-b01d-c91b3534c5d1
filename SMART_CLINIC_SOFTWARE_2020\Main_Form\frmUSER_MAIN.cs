﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.LIST_FORM;

namespace SMART_CLINIC_SOFTWARE_2020.Main_Form
{
    public partial class frmUSER_MAIN : DevExpress.XtraEditors.XtraForm
    {
        public frmUSER_MAIN()
        {
            InitializeComponent();
        }

        private void btnUSER_LIST_ItemClick(object sender, TileItemEventArgs e)
        {
            frmUSER_LIST frmUSER_LIST = new frmUSER_LIST();
            frmUSER_LIST.ShowDialog();
        }

        private void btnUSER_EDIT_ItemClick(object sender, TileItemEventArgs e)
        {
            frmMAIN.GETNEWUSER.OPEN_FORM(((DevExpress.XtraEditors.TileItem)sender).Tag.ToString());
            this.Close();
        }

        private void btnUSER_TYPE_ItemClick(object sender, TileItemEventArgs e)
        {
            frmMAIN.GETNEWUSER.OPEN_FORM(((DevExpress.XtraEditors.TileItem)sender).Tag.ToString());
            this.Close();
        }

        private void btnUSER_PER_ItemClick(object sender, TileItemEventArgs e)
        {
            frmMAIN.GETNEWUSER.OPEN_FORM(((DevExpress.XtraEditors.TileItem)sender).Tag.ToString());
            this.Close();
        }

        private void frmUSER_MAIN_Load(object sender, EventArgs e)
        {
           
        }
    }
}