﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmAPO : DevExpress.XtraEditors.XtraForm
    {
        public frmAPO()
        {
            InitializeComponent();
        }
        Classes.clsAPO NclsAPO = new Classes.clsAPO();
        Classes.clsCUST NclsCUST = new Classes.clsCUST();
        Classes.clsDOCTORS NclsDOC = new Classes.clsDOCTORS();

        public void Clear_Date()
        {
            gridControl1.DataSource = NclsAPO.APO_LIST_CUST_DOC(cmbCUST_NAME.Text,cmbDOC_NAME.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["APO_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["APO_NOTE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["DOC_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["CUST_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["APO_NAME"]);
            gridView1.Columns["APO_CODE"].Caption = "كود الموعد";
            gridView1.Columns["APO_DATE"].Caption = "تاريخ الموعد";
            gridView1.Columns["APO_TIME"].Caption = "وقت الموعد";
            gridView1.Columns["VIS_ID"].Caption = "رقم الزيارة";
            gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
            gridView1.Columns["DOC_NAME"].Caption = "اسم الطبيب";
            gridView1.BestFitColumns();

            txtAPO_CODE.Text = Classes.clsAPO.APO_DATATABLE.maxAPO_CODE().Rows[0]["APO_CODE"].ToString();
            dtpAPO_DATE.Value = DateTime.Now;
            txtAPO_TIME.Text = string.Format(DateTime.Now.ToShortTimeString() , "hh:MM");
            txtAPO_NAME.Text = "";
            txtAPO_NOTE.Text = ""; 

        }

        private void frmAPO_Load(object sender, EventArgs e)
        {
            if (Classes.clsCUST.CUST_ID != 0)
            {
                cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData(Classes.clsCUST.CUST_ID.ToString(), Classes.clsCUST.CUST_FULL_NAME);
                cmbCUST_ID.DisplayMember = "CUST_ID";
                cmbCUST_ID.ValueMember = "CUST_ID";
                cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
                cmbCUST_NAME.ValueMember = "CUST_NAME";
                cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
            }
            else
            {
                cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
                cmbCUST_ID.DisplayMember = "CUST_ID";
                cmbCUST_ID.ValueMember = "CUST_ID";
                cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
                cmbCUST_NAME.ValueMember = "CUST_NAME";
            }
            if (Classes.clsDOCTORS.DOC_ID != 0)
            {
                cmbDOC_ID.DataSource = NclsDOC.DOC_LIST();
                cmbDOC_ID.ValueMember = "DOC_ID";
                cmbDOC_NAME.DataSource = cmbDOC_ID.DataSource;
                cmbDOC_NAME.ValueMember = "DOC_NAME";
                cmbDOC_ID.Text = Classes.clsDOCTORS.DOC_ID.ToString();
            }
            else
            {
                cmbDOC_ID.DataSource = NclsDOC.DOC_LIST();
                cmbDOC_ID.ValueMember = "DOC_ID";
                cmbDOC_NAME.DataSource = cmbDOC_ID.DataSource;
                cmbDOC_NAME.ValueMember = "DOC_NAME";
            }

            foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }

                Clear_Date();
        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmCUST_LIST frmCUST_LIST = new LIST_FORM.frmCUST_LIST();
            frmCUST_LIST.ShowDialog();
            cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();

        }

        private void lblDOC_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmDOC_LIST frmDOC_LIST = new LIST_FORM.frmDOC_LIST();
            frmDOC_LIST.ShowDialog();
            cmbDOC_ID.Text = Classes.clsDOCTORS.DOC_ID.ToString();
            cmbDOC_NAME.Text = Classes.clsDOCTORS.DOC_NAME;
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
        }

       

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtAPO_CODE.Text != "" && cmbCUST_ID.Text != "" && cmbDOC_ID.Text != "" && txtAPO_TIME.Text != " : ")
                {
                    Classes.clsAPO.APO_DATATABLE.InsertAPO(Convert.ToInt64(txtAPO_CODE.Text), Convert.ToDateTime(string.Format(dtpAPO_DATE.Value.ToString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtAPO_TIME.Text.ToString(), "HH:MI")), txtAPO_NAME.Text, txtAPO_NOTE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Convert.ToInt64(cmbCUST_ID.Text), Convert.ToInt64(cmbDOC_ID.Text), Classes.clsVISIT.VIS_ID);
                    var result = MessageBox.Show("هل تريد طباعة الموعد" + "\n" + "طباعة Yes الغاء No", "طباعة الموعد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (result == DialogResult.Yes)
                    {
                        if (cmbCUST_ID.Text != "" && txtAPO_CODE.Text != "")
                        {
                            frmAPO_VIS_REPORT printapo = new frmAPO_VIS_REPORT();
                            frmAPO_VIS_REPORT.VIS_ID = Convert.ToInt64(Classes.clsVISIT.VIS_ID);
                            frmAPO_VIS_REPORT.CUST_ID = Convert.ToInt64(cmbCUST_ID.Text);
                            frmAPO_VIS_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                            frmAPO_VIS_REPORT.APO_CODE = Convert.ToInt64(txtAPO_CODE.Text);
                            printapo.ShowDialog();
                        }
                        Clear_Date();
                    }
                    else
                    {
                        Clear_Date();
                    }
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if(ex.Message.Contains("APO_DATE_INDEX"))
                {
                    MessageBox.Show("لا يمكن ادخال اكثر من موعد في نفس الوقت", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
            
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtAPO_CODE.Text != "")
                {
                    Classes.clsAPO.APO_DATATABLE.UpdateAPO(Convert.ToInt64(txtAPO_CODE.Text), Convert.ToDateTime(string.Format(dtpAPO_DATE.Value.ToString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtAPO_TIME.Text.ToString(), "HH:MI")), txtAPO_NAME.Text, txtAPO_NOTE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Convert.ToInt64(cmbCUST_ID.Text), Convert.ToInt64(cmbDOC_ID.Text), Classes.clsVISIT.VIS_ID, Classes.clsAPO.APO_ID, Classes.clsAPO.APO_ID);
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("APO_DATE_INDEX"))
                {
                    MessageBox.Show("لا يمكن ادخال اكثر من موعد في نفس الوقت", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
           
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtAPO_CODE.Text != "")
                {
                    Classes.clsAPO.APO_DATATABLE.DeleteAPO(Classes.clsAPO.APO_ID);
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
            
        }

        private void dtpAPO_DATE_ValueChanged(object sender, EventArgs e)
        {
            lblAPO_COUNT.Text = Classes.clsAPO.APO_DATATABLE.APO_COUNTbyAPO_DATE(string.Format(dtpAPO_DATE.Value.ToString(), "MM/dd/yyyy")).Rows[0]["APO_COUNT"].ToString();
            
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                NclsAPO.Select_APO(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["APO_CODE"]).ToString()));
                txtAPO_CODE.Text = Classes.clsAPO.APO_CODE.ToString();
                dtpAPO_DATE.Value = Convert.ToDateTime(string.Format(Classes.clsAPO.APO_DATE, "dd/MM/yyyy"));
                txtAPO_TIME.Text = string.Format(Classes.clsAPO.APO_TIME, "hh:MI");
                txtAPO_NAME.Text = Classes.clsAPO.APO_NAME;
                txtAPO_NOTE.Text = Classes.clsAPO.APO_NOTE;
                cmbDOC_ID.Text = Classes.clsAPO.DOC_ID.ToString();
                cmbCUST_ID.Text = Classes.clsAPO.CUST_ID.ToString();
                txtAPO_NAME.Focus();
            }
        }
    }
}