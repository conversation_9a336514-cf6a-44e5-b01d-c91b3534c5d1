﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmCARD : DevExpress.XtraEditors.XtraForm
    {
        public frmCARD()
        {
            InitializeComponent();
        }
        Classes.clsCARD NclsCARD = new Classes.clsCARD();
        Classes.clsCOMPANY NclsCOMPANY = new Classes.clsCOMPANY();

        public void Clear_Date()
        {

            try
            {
                gridControl1.DataSource = NclsCARD.CARD_List();
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                //if (gridView1.RowCount > 0)
                //{
                    gridView1.Columns.Remove(gridView1.Columns["CARD_ID"]);
                    gridView1.Columns.Remove(gridView1.Columns["COM_ID"]);
                    gridView1.Columns.Remove(gridView1.Columns["CARD_NOTE"]);
                    gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                    gridView1.Columns["CARD_CODE"].Caption = "كود البطاقة";
                    gridView1.Columns["CARD_NAME"].Caption = "اسم البطاقة";
                    gridView1.Columns["CARD_DATE"].Caption = "تاريخ الصلاحية";
                    gridView1.Columns["CARD_STATE"].Caption = "حالة البطاقة";
                    gridView1.Columns["CARD_PER"].Caption = "نسبة التامين";
                    //gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
                    gridView1.BestFitColumns();
                //}

                txtCARDCode.Text = Classes.clsCARD.CARD_DATATABLE.maxCORD_CODE().Rows[0]["CARD_CODE"].ToString();
                txtCARDName.Text = "";
                dtpCARDDATE.Value = DateTime.Now;
                txtCARDPERCNT.Text = "0";
                txtCARDNOTE.Text = "";
                //cmbCARDPERCNT.Text = "";

                cmbCOM_ID.DataSource = NclsCOMPANY.COMPANY_LIST();
                cmbCOM_ID.ValueMember = "COM_ID";
                cmbCOM_NAME.DataSource = cmbCOM_ID.DataSource;
                cmbCOM_NAME.ValueMember = "COM_NAME";
                txtCARDName.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void frmCARD_Load(object sender, EventArgs e)
        {

            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                Clear_Date();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
            
        }

        private void labelControl3_Click(object sender, EventArgs e)
        {

            try
            {
                LIST_FORM.frmCOM_LIST frmCOM_LIST = new LIST_FORM.frmCOM_LIST();
                frmCOM_LIST.ShowDialog();
                cmbCOM_ID.Text = Classes.clsCOMPANY.COM_ID.ToString();
                cmbCOM_NAME.Text = Classes.clsCOMPANY.COM_NAME;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {

            try
            {
                if (txtCARDCode.Text != "" && txtCARDName.Text != "" && cmbCOM_ID.Text != "")
                {
                    Classes.clsCARD.CARD_DATATABLE.InsertCARD(Convert.ToInt64(txtCARDCode.Text), txtCARDName.Text, Convert.ToDateTime(string.Format(dtpCARDDATE.Value.ToString(), "yyyy/MM/dd")), cmbCARDPERCNT.Text, Convert.ToInt16(txtCARDPERCNT.Text), txtCARDNOTE.Text, Convert.ToInt64(cmbCOM_ID.Text), Convert.ToInt64(Classes.clsCLINIC.CLI_ID));
                    Clear_Date();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("CARD_NAME_INDEX"))
                {
                    MessageBox.Show("البطاقة موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }

        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {

            try
            {
                if (gridView1.RowCount > 0 && txtCARDCode.Text != "" && txtCARDName.Text != "" && cmbCOM_ID.Text != "")
                {
                    Classes.clsCARD.CARD_DATATABLE.UpdateCARD(Convert.ToInt64(txtCARDCode.Text), txtCARDName.Text, Convert.ToDateTime(string.Format(dtpCARDDATE.Value.ToString(), "yyyy/MM/dd")), cmbCARDPERCNT.Text, Convert.ToInt16(txtCARDPERCNT.Text), txtCARDNOTE.Text, Convert.ToInt64(cmbCOM_ID.Text), Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsCARD.CARD_ID, Classes.clsCARD.CARD_ID);
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("CARD_NAME_INDEX"))
                {
                    MessageBox.Show("البطاقة موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }

        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {

            try
            {
                if (gridView1.RowCount > 0 && txtCARDCode.Text != "" && txtCARDName.Text != "" && cmbCOM_ID.Text != "")
                {
                    Classes.clsCARD.CARD_DATATABLE.DeleteCARD(Classes.clsCARD.CARD_ID);
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
            
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();

        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsCARD.Select_CARD(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CARD_NAME"]).ToString());
                    txtCARDCode.Text = Classes.clsCARD.CARD_CODE.ToString();
                    txtCARDName.Text = Classes.clsCARD.CARD_NAME;
                    dtpCARDDATE.Value = Convert.ToDateTime(string.Format(Classes.clsCARD.CARD_DATE, "yyyy/MM/dd"));
                    cmbCARDPERCNT.Text = Classes.clsCARD.CARD_STATE;
                    txtCARDPERCNT.Text = Classes.clsCARD.CARD_PER.ToString();
                    txtCARDNOTE.Text = Classes.clsCARD.CARD_NOTE;
                    cmbCOM_ID.Text = Classes.clsCARD.COM_ID.ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}