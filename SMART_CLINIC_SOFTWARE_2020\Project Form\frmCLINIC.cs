﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmCLINIC : DevExpress.XtraEditors.XtraForm
    {
        public frmCLINIC()
        {
            InitializeComponent();
        }

        Classes.clsCLINIC NclsClinic = new Classes.clsCLINIC();

        public void clear_data()
        {

            try
            {
                gridControl1.DataSource = NclsClinic.CLINIC_LIST();
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["CLI_NOTE"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_CONNECTION"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_SERVER"]);
                gridView1.Columns["CLI_CODE"].Caption = "كود العيادة";
                gridView1.Columns["CLI_NAME"].Caption = "اسم العيادة";
                gridView1.Columns["CLI_LOC"].Caption = "عنوان العيادة";
                gridView1.BestFitColumns();
                gridView1.OptionsSelection.MultiSelect = true;
                txtCLICode.Text = Classes.clsCLINIC.CLINIC_DATATABLE.MAX_CLI_CODE().Rows[0]["CLI_CODE"].ToString();
                txtCLIName.Text = "";
                txtCLILOC.Text = "";
                txtCLINOTE.Text = "";
                txtCli_Connection.Text = "";
                txtCLI_SERVER.Text = "";
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

        }
        private void frmCLINIC_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                clear_data();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                clear_data();
            }
           
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            clear_data();
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {

            try
            {
                if (txtCLICode.Text != "" && txtCLIName.Text != "" && txtCLI_SERVER.Text != "" && txtCli_Connection.Text != "")
                {
                    Classes.clsCLINIC.CLINIC_DATATABLE.InsertCLINIC(Convert.ToInt64(txtCLICode.Text), Convert.ToInt64(txtCLICode.Text), txtCLIName.Text, txtCLILOC.Text, txtCLINOTE.Text, txtCli_Connection.Text, txtCLI_SERVER.Text);
                    clear_data();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    clear_data();

                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("CLI_NAME_INDEX"))
                {
                    MessageBox.Show("اسم العيادة موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    clear_data();
                    return;
                }
                MessageBox.Show(ex.Message);
                clear_data();
            }
           
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {

            try
            {
                if (gridView1.RowCount > 0 && txtCLICode.Text != "" && txtCLIName.Text != "" && txtCLI_SERVER.Text != "" && txtCli_Connection.Text != "")
                {
                    Classes.clsCLINIC.CLINIC_DATATABLE.UpdateCLINIC(Convert.ToInt64(txtCLICode.Text), txtCLIName.Text, txtCLILOC.Text, txtCLINOTE.Text, Classes.clsCLINIC.CLI_ID, Classes.clsCLINIC.CLI_ID, txtCli_Connection.Text, txtCLI_SERVER.Text);
                    clear_data();
                }
                else
                {
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("CLI_NAME_INDEX"))
                {
                    MessageBox.Show("اسم العيادة موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    clear_data();
                    return;
                }
                MessageBox.Show(ex.Message);
                clear_data();
            }
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {

            try
            {
                if (gridView1.RowCount > 0 && txtCLICode.Text != "" && txtCLIName.Text != "")
                {
                    Classes.clsCLINIC.CLINIC_DATATABLE.DeleteCLINIC(Classes.clsCLINIC.CLI_ID);
                    clear_data();
                }
                else
                {
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                clear_data();
            }
           
        }

        private void txtSEARCH_EditValueChanged(object sender, EventArgs e)
        {
            clear_data();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsClinic.SELECT_CLINIC(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CLI_NAME"]).ToString());
                    txtCLICode.Text = Classes.clsCLINIC.CLI_CODE.ToString();
                    txtCLIName.Text = Classes.clsCLINIC.CLI_NAME.ToString();
                    txtCLILOC.Text = Classes.clsCLINIC.CLI_LOC.ToString();
                    txtCLINOTE.Text = Classes.clsCLINIC.CLI_NOTE.ToString();
                    txtCli_Connection.Text = Classes.clsCLINIC.CLI_CONNECTION.ToString();
                    txtCLI_SERVER.Text = Classes.clsCLINIC.CLI_SERVER.ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}