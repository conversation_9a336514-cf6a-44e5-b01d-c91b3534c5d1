﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Net.NetworkInformation;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmCLINIC_PANEL : DevExpress.XtraEditors.XtraForm
    {
        public frmCLINIC_PANEL()
        {
            InitializeComponent();
        }
        DataTable dt = new DataTable();
        string CONNSTRING = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE;
        Classes.clsCLINIC NclsClinic = new Classes.clsCLINIC();

        private Button AddButton(int i, int Startpos, int Endpos, DataTable dt)
        {
            Button btn = new Button();
            try
            {
                string hostname = dt.Rows[i]["CLI_SERVER"].ToString() == "." ? Environment.MachineName.ToString() : dt.Rows[i]["CLI_SERVER"].ToString();
                int timeout = 10000;
                Ping ping = new Ping();
                if (hostname != "" && hostname != null)
                {
                    btn.Name = dt.Rows[i][0].ToString();
                    btn.Text = dt.Rows[i]["CLI_NAME"].ToString();
                    btn.Width = 180;
                    btn.Height = 100;
                    btn.Tag = dt.Rows[i]["CLI_CONNECTION"].ToString(); 
                    btn.Font = new Font("Droid Arabic Kufi", 13, FontStyle.Regular);
                    btn.BackColor = Color.LightBlue;
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.Location = new Point(Startpos, Endpos);
                    btn.Margin = new Padding(3);
                }
                else
                {
                    btn.Name = dt.Rows[i][0].ToString();
                    btn.Text = dt.Rows[i]["CLI_NAME"].ToString();
                    btn.Width = 180;
                    btn.Height = 100;
                    btn.Tag = dt.Rows[i]["CLI_CONNECTION"].ToString();
                    btn.Font = new Font("Droid Arabic Kufi", 13, FontStyle.Regular);
                    btn.BackColor = Color.OrangeRed;
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.Location = new Point(Startpos, Endpos);
                    btn.Margin = new Padding(3);
                    btn.Enabled = false;
                }
               
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            return btn;
        }

        private void Button3_Click(System.Object sender, System.EventArgs e)
        {
            try
            {
                SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID = ((Button)sender).Name;
                SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_NAME = ((Button)sender).Text;
                SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                Classes.clsCLINIC clinic = new Classes.clsCLINIC();
                clinic.SELECT_CLINIC(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_NAME);
                if (((Button)sender).Tag.ToString() != "")
                {
                    //CONNSTRING = ((Button)sender).Tag.ToString();
                    Classes.clsDATABASE_CONNECTION config_database = new Classes.clsDATABASE_CONNECTION();
                    config_database.DATABASE_CONFIG(((Button)sender).Tag.ToString());
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default["ClinicDataBase_2020ConnectionString"] = ((Button)sender).Tag.ToString();
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();

                    frmLOGIN frmlogin = new frmLOGIN();
                    frmlogin.Show();
                    this.Hide();
                }
                else
                {
                    return;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }
        public void LOAD_CLINIC()
        {
            try
            {
                dt = Classes.clsCLINIC.CLINIC_DATATABLE.GetData();
                if(dt.Rows.Count == 0)
                {
                    Classes.clsDATABASE_CONNECTION config_database = new Classes.clsDATABASE_CONNECTION();
                    config_database.DATABASE_CONFIG(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE);
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default["ClinicDataBase_2020ConnectionString"] = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                    Classes.clsCLINIC.CLINIC_DATATABLE.InsertCLINIC(1, 1, "عيادة طب الاسنان", "الرئيسية", "", SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE, Environment.MachineName.ToString());

                }
                int Startpos = 1150;
                int Endpos = 50;
                btnLIST.Controls.Clear();
                for (var i = 0; i <= dt.Rows.Count - 1; i++)
                {
                    Button btn = AddButton(i, Startpos, Endpos, dt);
                    btn.Click += Button3_Click;
                    btnLIST.Controls.Add(btn);
                    Startpos = Startpos - 190;
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
        private void frmCLINIC_PANEL_Load(object sender, EventArgs e)
        {
            LOAD_CLINIC();
        }

        private void btnREFRESH_Click(object sender, EventArgs e)
        {
            LOAD_CLINIC();
        }

        private void btnEXITE_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void btnMINIMIZE_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

      
    }
}