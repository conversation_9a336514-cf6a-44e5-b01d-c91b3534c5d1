﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmCOMPANY : DevExpress.XtraEditors.XtraForm
    {
        public frmCOMPANY()
        {
            InitializeComponent();
        }

        Classes.clsCOMPANY NclsCom = new Classes.clsCOMPANY();


        public void clear_data()
        {
            DataTable dt = new DataTable();
            try
            {
                dt = Classes.clsCOMPANY.COMPANY_DATATABLE.GetData();
                gridControl1.DataSource = dt;
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["COM_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns["COM_CODE"].Caption = "كود الشركة";
                gridView1.Columns["COM_NAME"].Caption = "اسم الشركة";
                gridView1.Columns["COM_ADDRESS"].Caption = "عنوان الشركة";
                gridView1.Columns["COM_MOBILE"].Caption = "رقم الهاتف";
                gridView1.Columns["COM_STATE"].Caption = "الحالة";

                txtCOMCode.Text = Classes.clsCOMPANY.COMPANY_DATATABLE.maxCOM_CODE().Rows[0]["COM_CODE"].ToString();
                txtCOMName.Text = "";
                txtCOMADDRESS.Text = "";
                txtCOM_MOBILE.Text = "";
                cmbCOM_STATE.Text = "";
                txtCOMName.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }
        private void frmCOMPANY_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                clear_data();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            clear_data();
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtCOMCode.Text != "" && txtCOMName.Text != "")
                {
                    Classes.clsCOMPANY.COMPANY_DATATABLE.InsertCOMPANY(Convert.ToInt64(txtCOMCode.Text), txtCOMName.Text, txtCOMADDRESS.Text, txtCOM_MOBILE.Text, cmbCOM_STATE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID));
                    clear_data();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    clear_data();

                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("COM_NAME_INDEX"))
                {
                    MessageBox.Show("اسم الشركة موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    clear_data();
                    return;
                }
                MessageBox.Show(ex.Message);
                clear_data();
            }

        }


        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtCOMCode.Text != "" && txtCOMName.Text != "")
                {
                    Classes.clsCOMPANY.COMPANY_DATATABLE.UpdateCOMPANY(Convert.ToInt64(txtCOMCode.Text), txtCOMName.Text, txtCOMADDRESS.Text, txtCOM_MOBILE.Text, cmbCOM_STATE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsCOMPANY.COM_ID, Classes.clsCOMPANY.COM_ID);
                    clear_data();
                }
                else
                {
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("COM_NAME_INDEX"))
                {
                    MessageBox.Show("اسم الشركة موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    clear_data();
                    return;
                }
                MessageBox.Show(ex.Message);
                clear_data();
            }

        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtCOMCode.Text != "" && txtCOMName.Text != "")
                {
                    Classes.clsCOMPANY.COMPANY_DATATABLE.DeleteCOMPANY(Classes.clsCOMPANY.COM_ID);
                    clear_data();
                }
                else
                {
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                clear_data();
            }
            
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsCom.SELECT_COMPANY(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["COM_NAME"]).ToString());
                    txtCOMCode.Text = Classes.clsCOMPANY.COM_CODE.ToString();
                    txtCOMName.Text = Classes.clsCOMPANY.COM_NAME.ToString();
                    txtCOMADDRESS.Text = Classes.clsCOMPANY.COM_ADDRESS.ToString();
                    txtCOM_MOBILE.Text = Classes.clsCOMPANY.COM_MOBILE.ToString();
                    cmbCOM_STATE.Text = Classes.clsCOMPANY.COM_STATE.ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}