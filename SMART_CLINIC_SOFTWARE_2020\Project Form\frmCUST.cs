﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;


namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmCUST : DevExpress.XtraEditors.XtraForm
    {
        public frmCUST()
        {
            InitializeComponent();
        }

        Classes.clsCUST NclsCUST = new Classes.clsCUST();
        Classes.clsCARD NclsCARD = new Classes.clsCARD();

        public void Clear_Date()
        {
            try
            {
                gridControl1.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData(txtSEARCH.Text, txtSEARCH.Text);
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["CUST_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["CUST_GENDER"]);
                gridView1.Columns.Remove(gridView1.Columns["CUST_NATION"]);
                gridView1.Columns.Remove(gridView1.Columns["CUST_AGE_MONTH"]);
                gridView1.Columns["CUST_CODE"].Caption = "كود المريض";
                gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
                gridView1.Columns["CUST_AGE"].Caption = "عمر المريض";
                gridView1.Columns["CUST_BD"].Caption = "تاريخ الميلاد";
                gridView1.Columns["CUST_MOBILE1"].Caption = "رقم الهاتف";
                gridView1.BestFitColumns();

                txtCUST_Code.Text = Classes.clsCUST.CUST_DATATABLE.maxCUST_CODE().Rows[0]["CUST_CODE"].ToString();
                txtCUST_F_Name.Text = "";
                txtCUST_S_Name.Text = "";
                txtCUST_T_Name.Text = "";
                txtCUST_L_Name.Text = "";
                txtCUST_AGE.Text = "0";
                dtpCUST_DATE.Value = DateTime.Now;
                txtCUST_MOBILE1.Text = "";
                txtCUST_MOBILE2.Text = "";
                txtCUST_ADDRESS.Text = "";
                txtCUST_STATE.Text = "";
                txtCUST_NOTE.Text = "";
                cmbGENDER.Text = "";
                cmbNATION.Text = "";
                txtMonth.Text = "0";
                cmbCARD_ID.DataSource = NclsCARD.CARD_List();
                cmbCARD_ID.ValueMember = "CARD_ID";
                cmbCARD_NAME.DataSource = cmbCARD_ID.DataSource;
                cmbCARD_NAME.ValueMember = "CARD_NAME";
                txtCUST_F_Name.Focus();

                if (txtCUST_STATE.Text == "مؤمن")
                {
                    cmbCARD_ID.Enabled = true;
                    cmbCARD_NAME.Enabled = true;
                    lblCARD_LIST.Enabled = true;
                    cmbCARD_ID.DataSource = NclsCARD.CARD_List();
                    cmbCARD_ID.ValueMember = "CARD_ID";
                    cmbCARD_NAME.DataSource = cmbCARD_ID.DataSource;
                    cmbCARD_NAME.ValueMember = "CARD_NAME";
                }
                //else if (txtCUST_STATE.Text == "غير مؤمن")
                //{
                //    cmbCARD_ID.Text = "1";
                //    cmbCARD_NAME.Text = "غير مؤمن";
                //    cmbCARD_ID.Enabled = false;
                //    cmbCARD_NAME.Enabled = false;
                //    lblCARD_LIST.Enabled = false;
                //}
                else
                {
                    cmbCARD_ID.Text = "1";
                    cmbCARD_NAME.Text = "غير مؤمن";
                    cmbCARD_ID.Enabled = false;
                    cmbCARD_NAME.Enabled = false;
                    lblCARD_LIST.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void frmCUST_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                Clear_Date();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void txtSEARCH_EditValueChanged(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            txtSEARCH.Text = "";
            txtSEARCH.Focus();
        }

        public void SELECT_CUST_DATA(string S_CUST_CODE, string S_CUST_NAME)
        {
            NclsCUST.Select_CUST(S_CUST_CODE, S_CUST_NAME);
            txtCUST_Code.Text = Classes.clsCUST.CUST_CODE.ToString();
            txtCUST_F_Name.Text = Classes.clsCUST.CUST_F_NAME;
            txtCUST_S_Name.Text = Classes.clsCUST.CUST_S_NAME;
            txtCUST_T_Name.Text = Classes.clsCUST.CUST_T_NAME;
            txtCUST_L_Name.Text = Classes.clsCUST.CUST_L_NAME;
            txtCUST_AGE.Text = Classes.clsCUST.CUST_AGE;
            dtpCUST_DATE.Value = Convert.ToDateTime(string.Format(Classes.clsCUST.CUST_BD, "yyyy/MM/dd"));
            txtCUST_MOBILE1.Text = Classes.clsCUST.CUST_MOBILE1;
            txtCUST_MOBILE2.Text = Classes.clsCUST.CUST_MOBILE2;
            txtCUST_ADDRESS.Text = Classes.clsCUST.CUST_ADDRESS;
            txtCUST_STATE.Text = Classes.clsCUST.CUST_SAVE_STATE;
            txtCUST_NOTE.Text = Classes.clsCUST.CUST_NOTE;
            cmbCARD_ID.Text = Classes.clsCUST.CARD_ID.ToString();
            txtMonth.Text = Classes.clsCUST.CUST_AGE_MONTH;
            cmbGENDER.Text = Classes.clsCUST.CUST_GENDER;
            cmbNATION.Text = Classes.clsCUST.CUST_NATION;
        }
       

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtCUST_Code.Text != "" && txtCUST_F_Name.Text != "")
                {
                    Classes.clsCUST.CUST_DATATABLE.InsertCUST(Convert.ToInt64(txtCUST_Code.Text), txtCUST_F_Name.Text, txtCUST_S_Name.Text, txtCUST_T_Name.Text, txtCUST_L_Name.Text, txtCUST_AGE.Text, Convert.ToDateTime(string.Format(dtpCUST_DATE.Value.ToString(), "yyyy/MM/dd")), txtCUST_MOBILE1.Text, txtCUST_MOBILE2.Text, txtCUST_ADDRESS.Text, txtCUST_STATE.Text, txtCUST_NOTE.Text, Convert.ToInt64(cmbCARD_ID.Text), Convert.ToInt64(Classes.clsCLINIC.CLI_ID), cmbGENDER.Text, cmbNATION.Text, txtMonth.Text);
                    Clear_Date();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("CUST_NAME_INDEX"))
                {
                    var RESULT = MessageBox.Show("اسم المريض موجودة مسبقا" + "\n" + "لتحميل ملف المريض Yes الغاء No", "!! تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                    if (RESULT == DialogResult.Yes)
                    {
                        SELECT_CUST_DATA(txtCUST_Code.Text, txtCUST_F_Name.Text + "" + txtCUST_S_Name.Text + "" + txtCUST_T_Name.Text + "" + txtCUST_L_Name.Text);
                        txtCUST_F_Name.Focus();
                        return;
                    }
                    else
                    {
                        Clear_Date();
                        return;
                    }
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
        }

        private void cmbCUST_STATE_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtCUST_STATE.Text == "مؤمن")
                {
                    cmbCARD_ID.Enabled = true;
                    cmbCARD_NAME.Enabled = true;
                    lblCARD_LIST.Enabled = true;
                    cmbCARD_ID.DataSource = NclsCARD.CARD_List();
                    cmbCARD_ID.ValueMember = "CARD_ID";
                    cmbCARD_NAME.DataSource = cmbCARD_ID.DataSource;
                    cmbCARD_NAME.ValueMember = "CARD_NAME";
                }
                //else if (txtCUST_STATE.Text == "غير مؤمن")
                //{
                //    cmbCARD_ID.Text = "1";
                //    cmbCARD_NAME.Text = "غير مؤمن";
                //    cmbCARD_ID.Enabled = false;
                //    cmbCARD_NAME.Enabled = false;
                //    lblCARD_LIST.Enabled = false;
                //}
                else
                {
                    cmbCARD_ID.Text = "1";
                    cmbCARD_NAME.Text = "غير مؤمن";
                    cmbCARD_ID.Enabled = false;
                    cmbCARD_NAME.Enabled = false;
                    lblCARD_LIST.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }      
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtCUST_Code.Text != "" && txtCUST_F_Name.Text != "")
                {
                    Classes.clsCUST.CUST_DATATABLE.UpdateCUST(Convert.ToInt64(txtCUST_Code.Text), txtCUST_F_Name.Text, txtCUST_S_Name.Text, txtCUST_T_Name.Text, txtCUST_L_Name.Text, txtCUST_AGE.Text, Convert.ToDateTime(string.Format(dtpCUST_DATE.Value.ToString(), "yyyy/MM/dd")), txtCUST_MOBILE1.Text, txtCUST_MOBILE2.Text, txtCUST_ADDRESS.Text, txtCUST_STATE.Text, txtCUST_NOTE.Text, Convert.ToInt64(cmbCARD_ID.Text), Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsCUST.CUST_ID, Classes.clsCUST.CUST_ID, cmbGENDER.Text, cmbNATION.Text, txtMonth.Text);
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("CUST_NAME_INDEX"))
                {
                    var RESULT = MessageBox.Show("اسم المريض موجودة مسبقا" + "\n" + "لتحميل ملف المريض Yes الغاء No", "!! تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                    if (RESULT == DialogResult.Yes)
                    {
                        SELECT_CUST_DATA(txtCUST_Code.Text, txtCUST_F_Name.Text + "" + txtCUST_S_Name.Text + "" + txtCUST_T_Name.Text + "" + txtCUST_L_Name.Text);
                        txtCUST_F_Name.Focus();
                        return;
                    }
                    else
                    {
                        Clear_Date();
                        return;
                    }
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtCUST_Code.Text != "" && txtCUST_F_Name.Text != "" && txtCUST_MOBILE1.Text != "")
                {
                    Classes.clsCUST.CUST_DATATABLE.DeleteCUST(Classes.clsCUST.CUST_ID);
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }           
        }

        private void lblCARD_LIST_Click(object sender, EventArgs e)
        {
            try
            {
                LIST_FORM.frmCARD_LIST frmCARD_LIST = new LIST_FORM.frmCARD_LIST();
                frmCARD_LIST.ShowDialog();
                cmbCARD_ID.Text = Classes.clsCARD.CARD_ID.ToString();
                cmbCARD_NAME.Text = Classes.clsCARD.CARD_NAME;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void txtCUST_AGE_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtCUST_AGE.Text != "" && txtCUST_AGE.Text != "0" && dtpCUST_DATE.Focused == false)
                {
                    dtpCUST_DATE.Value = DateTime.Now;
                    int Years = 0;
                    Years = Convert.ToInt16(txtCUST_AGE.Text) * -1;
                    dtpCUST_DATE.Value = dtpCUST_DATE.Value.AddYears(Years);
                }
                else
                {
                    NclsCUST.CalculateAge(dtpCUST_DATE.Value);
                    txtCUST_AGE.Text = Classes.clsCUST.Years.ToString();
                    txtMonth.Text = Classes.clsCUST.Months.ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void dtpCUST_DATE_ValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (dtpCUST_DATE.Focused == true)
                {
                    NclsCUST.CalculateAge(dtpCUST_DATE.Value);
                    txtCUST_AGE.Text = Classes.clsCUST.Years.ToString();
                    txtMonth.Text = Classes.clsCUST.Months.ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }     
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    SELECT_CUST_DATA(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CUST_CODE"]).ToString(), (gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CUST_NAME"]).ToString()));

                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}