﻿using Microsoft.SqlServer.Management.Common;
using Microsoft.SqlServer.Management.Smo;
using Microsoft.VisualBasic;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.IO;
using System.Management;
using System.Net.NetworkInformation;
using System.Threading;
using System.Windows.Forms;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmDATABASE_CONFIG : DevExpress.XtraEditors.XtraForm
    {
        public frmDATABASE_CONFIG()
        {
            InitializeComponent();
        }

        string CONNSTRING = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE;
        SqlConnection conn;
        Int64 L_LOCAL_KEY;

        public Boolean DataCONFIG()
        {
            Boolean ConnSTATE = false;
            string hostname = txtSER_NAME.Text == "." ? Environment.MachineName.ToString() : txtSER_NAME.Text;
            int timeout = 10000;
            Ping ping = new Ping();
            try
            {
                if (hostname != "" && hostname != null)
                {
                    PingReply pingreply = ping.Send(hostname, timeout);
                    if (pingreply.Status == IPStatus.Success)
                    {
                        if (txtCONNECTION_STRING.Text != "" && txtSER_NAME.Text != "" && txtUSER_NAME.Text != "" && txtUser_Password.Text != "")
                        {
                            try
                            {
                                conn = new SqlConnection(CONNSTRING);
                                conn.Open();
                                conn.Close();
                                ConnSTATE = true;
                                DataBase_Name();
                                cmbDATABASE_NAME.DataSource = DataBase_Name();
                                cmbDATABASE_NAME.DisplayMember = "name";

                            }
                            catch (Exception ex)
                            {
                                cmbDATABASE_NAME.DataSource = null;
                                cmbDATABASE_NAME.Text = "";
                                ConnSTATE = false;
                                MessageBox.Show("النظام غير متصل بقاعدة البيانات يرجى ادخال الاعدادات" + " / " + ex, "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                return ConnSTATE;
                            }
                        }
                    }
                    else
                    {
                        MessageBox.Show("الاتصال بالسيرفر غير متاح", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        ConnSTATE = false;
                        return ConnSTATE;

                    }
                }
                else
                {
                    ConnSTATE = false;
                    return ConnSTATE;
                }
                return ConnSTATE;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return ConnSTATE;
            }

        }

        public DataTable DataBase_Name()
        {
            DataTable dt = new DataTable();
            try
            {
                conn = new SqlConnection(CONNSTRING);
                SqlDataAdapter adp = new SqlDataAdapter("SELECT [NAME] FROM master.dbo.sysdatabases WHERE dbid > 4", conn);
                adp.Fill(dt);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            return dt;
        }

        public Boolean SYSTEM_ACTIVATION()
        {
            L_LOCAL_KEY = Convert.ToInt64(GET_HARDDISK_NO());
            Boolean Active_State = false;
            try
            {
                if (SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_S_DATE < SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_E_DATE && SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_KEY != "0" && SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_KEY == L_LOCAL_KEY.ToString())
                {
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_KEY = L_LOCAL_KEY.ToString();
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_S_DATE = DateTime.Now;
                    Active_State = true;
                    return Active_State;
                }
                else
                {
                    MessageBox.Show(GET_HARDDISK_NO(), "!! اتصل بصاحب البرنامج للحصول على تفعيل البرنامج دون اغلاق البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    string input = Interaction.InputBox("تظهر هذه النافذة في حال ان البرنامج منتهي الصلاحية" + "\n" + "او تم تشغيله لاول مرة" + "\n" + "يرجى ادخال رمز التفعيل" + "\n" + Eramake.eCryptography.Encrypt(GET_HARDDISK_NO()), "تفعيل النظام", "الرجاء ادخال الرمز التفعيلي هنا");
                    if (input != "" && Eramake.eCryptography.Decrypt(input) == L_LOCAL_KEY.ToString())
                    {
                        string inputLength = Eramake.eCryptography.Decrypt(Interaction.InputBox("", "", "0"));
                        if (inputLength != "")
                        {
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_KEY = L_LOCAL_KEY.ToString();
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_S_DATE = DateTime.Now;
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_E_DATE = DateTime.Now.AddDays(Convert.ToInt64(inputLength));
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Active_Date = DateTime.Now;
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                            Active_State = true;
                            return Active_State;
                        }
                        else
                        {
                            MessageBox.Show(" !! رمز التفعيل المدخل غير صحيح", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            Active_State = false;
                            return Active_State;
                        }
                    }
                    else
                    {
                        MessageBox.Show(" !! رمز التفعيل المدخل غير صحيح", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        Active_State = false;
                        return Active_State;
                    }
                }
                //return Active_State;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return Active_State;
            }

        }

        private void frmDATABASE_CONFIG_Load(object sender, EventArgs e)
        {

            try
            {
                if (SYSTEM_ACTIVATION() == true)
                {
                    txtCONNECTION_STRING.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE;
                    txtSER_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME;
                    txtUSER_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME;
                    txtUser_Password.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD;
                    cmbDATABASE_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DATABASE_NAME;
                    txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";
                    if (CHECKDEFULTDATABASE() == true)
                    {
                        if (DataCONFIG() == true)
                        {
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default["ClinicDataBase_2020ConnectionString1"] = txtCONNECTION_STRING.Text;
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME = txtSER_NAME.Text;
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME = txtUSER_NAME.Text;
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD = txtUser_Password.Text;
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DATABASE_NAME = cmbDATABASE_NAME.Text;
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE = txtCONNECTION_STRING.Text;
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();

                            this.Close();
                            Thread th = new Thread(OpenLoginForm);
                            th.SetApartmentState(ApartmentState.STA);
                            th.Start();
                        }
                        else
                        {
                            txtCONNECTION_STRING.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE;
                            txtSER_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME;
                            txtUSER_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME;
                            txtUser_Password.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD;
                            cmbDATABASE_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DATABASE_NAME;
                            SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                            lblConn_State.Text = "غير متصل بقاعدة البيانات";
                            lblConn_State.BackColor = Color.Red;
                        }
                    }
                }
                else
                {
                    Application.Exit();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void btnDATABASE_FILL_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtSER_NAME.Text != "")
                {
                    try
                    {
                        DataBase_Name();
                        cmbDATABASE_NAME.DataSource = DataBase_Name();
                        cmbDATABASE_NAME.DisplayMember = "name";
                    }
                    catch (Exception ex)
                    {
                        cmbDATABASE_NAME.DataSource = null;
                        cmbDATABASE_NAME.Text = "";
                        MessageBox.Show("لا يوجد قاعدة بيانات على هذا السيرفر " + " / " + ex, "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

        }

        private void txtSER_NAME_TextChanged(object sender, EventArgs e)
        {
            txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";
        }

        private void txtUSER_NAME_TextChanged(object sender, EventArgs e)
        {
            txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";

        }

        private void txtUser_Password_TextChanged(object sender, EventArgs e)
        {
            txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";

        }

        private void cmbDATABASE_NAME_TextChanged(object sender, EventArgs e)
        {
            txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";
        }

        private void btnCONN_CHECK_Click(object sender, EventArgs e)
        {
            txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";
            CONNSTRING = txtCONNECTION_STRING.Text;
            try
            {
                if (DataCONFIG() == true)
                {
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE = txtCONNECTION_STRING.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME = txtSER_NAME.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME = txtUSER_NAME.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD = txtUser_Password.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                    lblConn_State.Text = "متصل بقاعدة البيانات";
                    lblConn_State.BackColor = Color.Green;
                }
                else
                {
                    txtCONNECTION_STRING.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE;
                    txtSER_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME;
                    txtUSER_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME;
                    txtUser_Password.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD;
                    cmbDATABASE_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DATABASE_NAME;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                    lblConn_State.Text = "غير متصل بقاعدة البيانات";
                    lblConn_State.BackColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

        }

        private void btnLOGIN_Click(object sender, EventArgs e)
        {
            try
            {
                if (lblConn_State.Text == "متصل بقاعدة البيانات")
                {
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default["ClinicDataBase_2020ConnectionString1"] = txtCONNECTION_STRING.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME = txtSER_NAME.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME = txtUSER_NAME.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD = txtUser_Password.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                    this.Close();
                    Thread th = new Thread(OpenLoginForm);
                    th.SetApartmentState(ApartmentState.STA);
                    th.Start();
                }
                
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

        }
        public void OpenLoginForm()
        {
            Application.Run(new frmCLINIC_PANEL());
        }

        public string GET_HARDDISK_NO()
        {
            string SerialNumber = "";
            ManagementObjectSearcher SEARCHER = new ManagementObjectSearcher("SELECT * FROM WIN32_DISKDRIVE where SerialNumber IS NOT NULL AND MediaType = 'Fixed hard disk media'");
            foreach (ManagementObject info in SEARCHER.Get())
            {
                SerialNumber = (info["SerialNumber"].ToString());
            }
            string HD_serial = SerialNumber;
            string mytext = HD_serial;
            char[] myChars = mytext.ToCharArray();
            HD_serial = "";
            foreach (char ch in myChars)
            {
                if (char.IsDigit(ch))
                    HD_serial += (ch);
            }

            return HD_serial;
        }

        public void CREATE_DATABASE()
        {
            try
            {
                SqlConnection Conn = new SqlConnection("Data Source= " + Environment.MachineName.ToString() + ";Initial Catalog=master;Integrated Security=True");

                StreamReader SR = new StreamReader(Application.StartupPath + "\\ClinicDataScript.sql");
                string ST = SR.ReadToEnd();
                Server server = new Server(new ServerConnection(Conn));
                server.ConnectionContext.ExecuteNonQuery(ST);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

        }
        public Boolean CHECKDEFULTDATABASE()
        {
            try
            {
                SqlConnection Conn = new SqlConnection("Data Source = " + Environment.MachineName.ToString() + ";Initial Catalog=master;Integrated Security=True");
                SqlCommand cmd = new SqlCommand("Select * from sysdatabases where Name = 'ClinicDataBase_2020'", Conn);
                Conn.Open();
                SqlDataReader Dr;
                Dr = cmd.ExecuteReader();
                if (Dr.Read())
                {
                    return true;
                }
                else
                {
                    CREATE_DATABASE();
                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return false;
            }
        }

        private void groupControl1_CustomButtonClick(object sender, DevExpress.XtraBars.Docking2010.BaseButtonEventArgs e)
        {
            try
            {
                if (e.Button.Properties.GroupIndex == 1)
                {
                    Application.Exit();
                }
                else if (e.Button.Properties.GroupIndex == 2)
                {
                    this.WindowState = FormWindowState.Minimized;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}