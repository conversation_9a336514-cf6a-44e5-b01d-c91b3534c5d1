﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmDIAGNOIS : DevExpress.XtraEditors.XtraForm
    {
        public frmDIAGNOIS()
        {
            InitializeComponent();
        }

        Classes.clsDIAGNOIS NclsDIG = new Classes.clsDIAGNOIS();

        public void clear_data()
        {
            try
            {
                gridControl1.DataSource = NclsDIG.Diagnois_List();
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["DIG_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["DIG_NOTE"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns["DIG_CODE"].Caption = "كود التشخيص";
                gridView1.Columns["DIG_NAME"].Caption = "اسم التشخيص";
                gridView1.Columns["DIG_TYPE"].Caption = "نوع التشخيص";

                txtDIG_Code.Text = Classes.clsDIAGNOIS.Diagnois_DATATABLE.maxDIG_CODE().Rows[0]["DIG_CODE"].ToString();
                txtDIG_NAMED.Text = "";
                txtDIG_TYPE.Text = "";
                txtDIG_NOTE.Text = "";
                txtDIG_NAMED.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void frmDIAGNOIS_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                clear_data();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                clear_data();
            }
           
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtDIG_Code.Text != "" && txtDIG_NAMED.Text != "")
                {
                    Classes.clsDIAGNOIS.Diagnois_DATATABLE.InsertDIAGNOIS(Convert.ToInt64(txtDIG_Code.Text), txtDIG_NAMED.Text, txtDIG_TYPE.Text, txtDIG_NOTE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID));
                    clear_data();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    clear_data();

                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("DIG_NAME_INDEX"))
                {
                    MessageBox.Show("هذا التشخيص موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    clear_data();
                    return;
                }
                MessageBox.Show(ex.Message);
                clear_data();
            }
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtDIG_Code.Text != "" && txtDIG_NAMED.Text != "")
                {
                    Classes.clsDIAGNOIS.Diagnois_DATATABLE.UpdateDIAGNOIS(Convert.ToInt64(txtDIG_Code.Text), txtDIG_NAMED.Text, txtDIG_TYPE.Text, txtDIG_NOTE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsDIAGNOIS.DIG_ID, Classes.clsDIAGNOIS.DIG_ID);
                    clear_data();
                }
                else
                {
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("DIG_NAME_INDEX"))
                {
                    MessageBox.Show("هذا التشخيص موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    clear_data();
                    return;
                }
                MessageBox.Show(ex.Message);
                clear_data();
            }
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtDIG_Code.Text != "" && txtDIG_NAMED.Text != "")
                {
                    Classes.clsDIAGNOIS.Diagnois_DATATABLE.DeleteDIGNOIS(Classes.clsDIAGNOIS.DIG_ID);
                    clear_data();
                }
                else
                {
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            clear_data();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsDIG.Select_DIAGNOIS(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["DIG_NAME"]).ToString());
                    txtDIG_Code.Text = Classes.clsDIAGNOIS.DIG_CODE.ToString();
                    txtDIG_NAMED.Text = Classes.clsDIAGNOIS.DIG_NAME.ToString();
                    txtDIG_TYPE.Text = Classes.clsDIAGNOIS.DIG_TYPE.ToString();
                    txtDIG_NOTE.Text = Classes.clsDIAGNOIS.DIG_NOTE.ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}