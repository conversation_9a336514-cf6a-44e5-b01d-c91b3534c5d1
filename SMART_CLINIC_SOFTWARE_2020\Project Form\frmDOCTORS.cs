﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;

namespace SMART_CLINIC_SOFTWARE_2020
{
    public partial class frmDOCTORS : DevExpress.XtraEditors.XtraForm
    {
        public frmDOCTORS()
        {
            InitializeComponent();
        }
        Classes.clsDOCTORS NclsDoc = new Classes.clsDOCTORS();
        Classes.clsUSERS NclsUser = new Classes.clsUSERS();

        public void Clear_Date()
        {
            try
            {
                gridControl1.DataSource = NclsDoc.DOC_LIST();
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                //if (gridView1.RowCount ==1)
                //{
                    gridView1.Columns.Remove(gridView1.Columns["DOC_ID"]);
                    gridView1.Columns.Remove(gridView1.Columns["USER_ID"]);
                    gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                    gridView1.Columns["DOC_CODE"].Caption = "كود الطبيب";
                    gridView1.Columns["DOC_NAME"].Caption = "اسم الطبيب";
                    gridView1.Columns["DOC_MAJOR"].Caption = "التخصص";
                    gridView1.Columns["DOC_EXP"].Caption = "الخبرة";
                    gridView1.Columns["DOC_BD"].Caption = "تاريخ الميلاد";
                    gridView1.Columns["DOC_MOBILE"].Caption = "رقم الهاتف";
                    gridView1.Columns["DOC_ADDRESS"].Caption = "العنوان";

                //}
                cmbUSER_ID.DataSource = NclsUser.User_List();
                cmbUSER_ID.ValueMember = "USER_ID";
                cmbUSER_NAME.DataSource = cmbUSER_ID.DataSource;
                cmbUSER_NAME.ValueMember = "USER_NAME";
                txtDocCode.Text = Classes.clsDOCTORS.DOCTABLE.Max_DOC_CODE().Rows[0]["DOC_CODE"].ToString();
                txtDocName.Text = "";
                txtDocMajor.Text = "";
                txtDocExp.Text = "";
                txtDocMobile.Text = "";
                txtDocAddress.Text = "";
                //cmbUSER_ID.Text = "";
                //cmbUSER_NAME.Text = "";
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }
        private void frmDOCTORS_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                Clear_Date();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
            
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtDocName.Text != "" && txtDocCode.Text != "")
                {
                    Classes.clsDOCTORS.DOCTABLE.InsertDOCTOR(Convert.ToInt64(txtDocCode.Text), txtDocName.Text, txtDocMajor.Text, txtDocExp.Text, Convert.ToDateTime(dtpDocBD.Value.ToString("yyyy/MM/dd")), txtDocMobile.Text, txtDocAddress.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Convert.ToInt64(cmbUSER_ID.Text));
                    Clear_Date();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("DOCTOR_NAME_INDEX"))
                {
                    MessageBox.Show("اسم الطبيب موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }

        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void gridView1_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsDoc.SELECT_DOC(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["DOC_NAME"]).ToString());
                    txtDocCode.Text = Classes.clsDOCTORS.DOC_CODE.ToString();
                    txtDocName.Text = Classes.clsDOCTORS.DOC_NAME;
                    txtDocMajor.Text = Classes.clsDOCTORS.DOC_MAJOR;
                    txtDocExp.Text = Classes.clsDOCTORS.DOC_EXP;
                    dtpDocBD.Value = Convert.ToDateTime(string.Format(Classes.clsDOCTORS.DOC_BD, "yyyy/MM/dd"));
                    txtDocMobile.Text = Classes.clsDOCTORS.DOC_MOBILE;
                    txtDocAddress.Text = Classes.clsDOCTORS.DOC_ADDRESS;
                    cmbUSER_ID.Text = Classes.clsDOCTORS.USER_ID.ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void labelControl8_Click(object sender, EventArgs e)
        {
            try
            {
                LIST_FORM.frmUSER_LIST frmUserList = new LIST_FORM.frmUSER_LIST();
                frmUserList.ShowDialog();
                cmbUSER_ID.Text = Classes.clsUSERS.USER_ID.ToString();
                cmbUSER_NAME.Text = Classes.clsUSERS.USER_NAME;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (MessageBox.Show("هل انت متاكد من تعديل البيانات", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    if (gridView1.RowCount > 0 && txtDocCode.Text != "" && txtDocName.Text != "")
                    {
                        Classes.clsDOCTORS.DOCTABLE.updateDOCTOR(Convert.ToInt64(txtDocCode.Text), txtDocName.Text, txtDocMajor.Text, txtDocExp.Text, Convert.ToDateTime(dtpDocBD.Value.ToString("yyyy/MM/dd")), txtDocMobile.Text, txtDocAddress.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Convert.ToInt64(cmbUSER_ID.Text), Classes.clsDOCTORS.DOC_ID, Classes.clsDOCTORS.DOC_ID);
                        Clear_Date();
                    }
                    else
                    {
                        Clear_Date();
                    }
                }
               
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("DOCTOR_NAME_INDEX"))
                {
                    MessageBox.Show("اسم الطبيب موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }

        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (MessageBox.Show("هل انت متاكد من حذف البيانات", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    if (gridView1.RowCount > 0 && txtDocCode.Text != "" && txtDocName.Text != "")
                    {
                        Classes.clsDOCTORS.DOCTABLE.DeleteDOCTOR(Classes.clsDOCTORS.DOC_ID);
                        Clear_Date();
                    }
                    else
                    {
                        Clear_Date();
                    }
                }
                
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }            
        }

       
    }      
}