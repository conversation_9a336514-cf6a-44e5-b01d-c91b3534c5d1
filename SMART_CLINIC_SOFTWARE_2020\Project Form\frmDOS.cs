﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmDOS : DevExpress.XtraEditors.XtraForm
    {
        public frmDOS()
        {
            InitializeComponent();
        }

        Classes.clsDOS NclsDOS = new Classes.clsDOS();

        public void Clear_Date()
        {
            try
            {
                txtDOSName.Text = "";
                txtDOSTYPE.Text = "";
                gridControl1.DataSource = NclsDOS.Select_DOS(txtDOSName.Text, txtDOSTYPE.Text);
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                //if (gridView1.RowCount > 0)
                //{
                gridView1.Columns.Remove(gridView1.Columns["DOS_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns["DOS_CODE"].Caption = "كود الجرعة";
                gridView1.Columns["DOS_CODE"].VisibleIndex = 0;
                gridView1.Columns["DOS_NAME"].Caption = "اسم الجرعة";
                gridView1.Columns["DOS_NAME"].VisibleIndex = 1;
                gridView1.Columns["DOS_TYPE"].Caption = "نوع الجرعة";
                gridView1.Columns["DOS_TYPE"].VisibleIndex = 2;
                gridView1.BestFitColumns();
                //}
                if (txtDOSName.Text == "" && txtDOSTYPE.Text == "")
                {
                    txtDOSCode.Text = Classes.clsDOS.DOS_DATATABLE.maxDOS_CODE().Rows[0]["DOS_CODE"].ToString();
                }

                txtDOSName.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }                     
        }

        private void frmDOS_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                Clear_Date();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                Clear_Date();
            }            
        }

     
        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtDOSCode.Text != "" && txtDOSName.Text != "" && txtDOSTYPE.Text != "")
                {
                    Classes.clsDOS.DOS_DATATABLE.InsertDOS(Convert.ToInt64(txtDOSCode.Text), txtDOSName.Text, txtDOSTYPE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID));
                    Clear_Date();

                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("DOS_NAME_INDEX"))
                {
                    MessageBox.Show("هذه الجرعة موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }

        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtDOSCode.Text != "" && txtDOSName.Text != "" && txtDOSTYPE.Text != "")
                {
                    Classes.clsDOS.DOS_DATATABLE.UpdateDOS(Convert.ToInt64(txtDOSCode.Text), txtDOSName.Text, txtDOSTYPE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsDOS.DOS_ID, Classes.clsDOS.DOS_ID);
                    Clear_Date();

                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("DOS_NAME_INDEX"))
                {
                    MessageBox.Show("هذه الجرعة موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }

        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtDOSCode.Text != "" && txtDOSName.Text != "" && txtDOSTYPE.Text != "")
                {
                    Classes.clsDOS.DOS_DATATABLE.DeleteDOS(Classes.clsDOS.DOS_ID);
                    Clear_Date();

                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();

        }

        private void lblSEARCH_Click(object sender, EventArgs e)
        {
            try
            {
                gridControl1.DataSource = NclsDOS.Select_DOS(txtDOSName.Text, txtDOSTYPE.Text);
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["DOS_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns["DOS_CODE"].Caption = "كود الجرعة";
                gridView1.Columns["DOS_CODE"].VisibleIndex = 0;
                gridView1.Columns["DOS_NAME"].Caption = "اسم الجرعة";
                gridView1.Columns["DOS_NAME"].VisibleIndex = 1;
                gridView1.Columns["DOS_TYPE"].Caption = "نوع الجرعة";
                gridView1.Columns["DOS_TYPE"].VisibleIndex = 2;
                gridView1.BestFitColumns();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }          
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsDOS.Select_DOS(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["DOS_NAME"]).ToString(), gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["DOS_TYPE"]).ToString());
                    txtDOSCode.Text = Classes.clsDOS.DOS_CODE.ToString();
                    txtDOSName.Text = Classes.clsDOS.DOS_NAME;
                    txtDOSTYPE.Text = Classes.clsDOS.DOS_TYPE;

                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}