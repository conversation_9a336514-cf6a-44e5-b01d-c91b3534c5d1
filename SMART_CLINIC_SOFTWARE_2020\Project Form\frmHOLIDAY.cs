﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmHOLIDAY : DevExpress.XtraEditors.XtraForm
    {
        public frmHOLIDAY()
        {
            InitializeComponent();
        }
        Classes.clsHOLIDAY NclsHOL = new Classes.clsHOLIDAY();
        Classes.clsCUST NclsCUST = new Classes.clsCUST();

        public void Clear_Date()
        {
            try
            {
               
                gridControl1.DataSource = Classes.clsHOLIDAY.HOL_DATATABLE.HOLbyCUST_ID(cmbCUST_ID.Text == "" ? 0 : Convert.ToInt64(cmbCUST_ID.Text));
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["HOL_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["HOL_TIME"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["VIS_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["HOL_NOTE"]);
                gridView1.Columns.Remove(gridView1.Columns["HOL_TEXT"]);
                gridView1.Columns["HOL_CODE"].Caption = "الرقم";
                gridView1.Columns["HOL_DATE"].Caption = "التاريخ";
                gridView1.Columns["HOL_NAME"].Caption = "مدة الاجازة";
                gridView1.Columns["CUST_ID"].Caption = "رقم المريض";
                gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
                gridView1.BestFitColumns();
                txtHOL_Code.Text = Classes.clsHOLIDAY.HOL_DATATABLE.maxHOL_CODE().Rows[0]["HOL_CODE"].ToString();
                dtpHOL_DATE.Value = DateTime.Now;
                txtHOL_TIME.Text = string.Format(DateTime.Now.ToShortTimeString(), "HH:mm");
                txtHOL_LONG.Text = "";
                txtHOL_NOTE.Text = "";

                txtHOL_TEXT.Clear();
                txtHOL_TEXT.AppendText(" راجعني المذكور/ة اعلاه وقد كان يشكو من : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_NOTE.Text + " ");
                txtHOL_TEXT.AppendText(" وقد اعطي العلاج اللازم له/ا وينصح باستراحة لمدة : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_LONG.Text + " ساعة ");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void frmHOLIDAY_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                if (Classes.clsCUST.CUST_ID != 0)
                {
                    cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData(Classes.clsCUST.CUST_ID.ToString(), Classes.clsCUST.CUST_FULL_NAME);
                    cmbCUST_ID.DisplayMember = "CUST_ID";
                    cmbCUST_ID.ValueMember = "CUST_ID";
                    cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
                    cmbCUST_NAME.ValueMember = "CUST_NAME";
                }
                Clear_Date();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void txtHOL_NOTE_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                txtHOL_TEXT.Clear();
                txtHOL_TEXT.AppendText(" راجعني المذكور/ة اعلاه وقد كان يشكو من : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_NOTE.Text + " ");
                txtHOL_TEXT.AppendText(" وقد اعطي العلاج اللازم له/ا وينصح باستراحة لمدة : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_LONG.Text + " ساعة ");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void txtHOL_LONG_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                txtHOL_TEXT.Clear();
                txtHOL_TEXT.AppendText(" راجعني المذكور/ة اعلاه وقد كان يشكو من : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_NOTE.Text + " ");
                txtHOL_TEXT.AppendText(" وقد اعطي العلاج اللازم له/ا وينصح باستراحة لمدة : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_LONG.Text + " ساعة ");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            try
            {
                LIST_FORM.frmCUST_LIST frmCUST_LIST = new LIST_FORM.frmCUST_LIST();
                frmCUST_LIST.ShowDialog();
                cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtHOL_Code.Text != "" && txtHOL_NOTE.Text != "" && cmbCUST_ID.Text != "" && txtHOL_LONG.Text != "")
                {
                    Classes.clsHOLIDAY.HOL_DATATABLE.InsertHOL(Convert.ToInt64(txtHOL_Code.Text), Convert.ToDateTime(string.Format(dtpHOL_DATE.Value.ToString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtHOL_TIME.Text.ToString(), "HH:MI")), txtHOL_LONG.Text, txtHOL_TEXT.Text, txtHOL_NOTE.Text, Convert.ToInt64(cmbCUST_ID.Text), Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsVISIT.VIS_ID);
                    Clear_Date();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("HOL_CUST_ID_INDEX"))
                {
                    MessageBox.Show("لا يجوز تسجيل اجازة في نفس التاريخ", "!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    MessageBox.Show(ex.Message, "!!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            
           
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    Classes.clsHOLIDAY.HOL_DATATABLE.DeleteHOL(Convert.ToInt64(cmbCUST_ID.Text), Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["HOL_CODE"]).ToString()));
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("HOL_CUST_ID_INDEX"))
                {
                    MessageBox.Show("لا يجوز تسجيل اجازة في نفس التاريخ", "!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    MessageBox.Show(ex.Message, "!!تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void cmbCUST_ID_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (cmbCUST_ID.Text != "" && cmbCUST_NAME.Text != "")
                {
                    gridControl1.DataSource = Classes.clsHOLIDAY.HOL_DATATABLE.HOLbyCUST_ID(Convert.ToInt64(cmbCUST_ID.Text));
                    gridView1.Columns.Remove(gridView1.Columns["HOL_ID"]);
                    gridView1.Columns.Remove(gridView1.Columns["HOL_TIME"]);
                    gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                    gridView1.Columns.Remove(gridView1.Columns["VIS_ID"]);
                    gridView1.Columns.Remove(gridView1.Columns["HOL_NOTE"]);
                    gridView1.Columns.Remove(gridView1.Columns["HOL_TEXT"]);
                    gridView1.Columns["HOL_CODE"].Caption = "الرقم";
                    gridView1.Columns["HOL_DATE"].Caption = "التاريخ";
                    gridView1.Columns["HOL_NAME"].Caption = "مدة الاجازة";
                    gridView1.Columns["CUST_ID"].Caption = "رقم المريض";
                    gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
                    gridView1.BestFitColumns();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }
    }
}