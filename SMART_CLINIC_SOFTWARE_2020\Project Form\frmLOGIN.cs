﻿using SMART_CLINIC_SOFTWARE_2020.Project_Form;
using System;
using System.Data;
using System.Windows.Forms;

namespace SMART_CLINIC_SOFTWARE_2020
{
    public partial class frmLOGIN : DevExpress.XtraEditors.XtraForm
    {
        public frmLOGIN()
        {
            InitializeComponent();
            SetupFormDesign();
        }

        private void SetupFormDesign()
        {
            // إضافة تأثيرات بصرية للحقول
            AddTextBoxEffects();
            AddComboBoxEffects();
            AddButtonEffects();
            AddPanelShadowEffect();
        }

        private void AddPanelShadowEffect()
        {
            // إضافة تأثير الظل والحدود الاحترافية للوحة تسجيل الدخول
            panelLogin.Paint += (s, e) => {
                // رسم ظل احترافي للتصميم الرصاصي
                using (System.Drawing.SolidBrush shadowBrush = new System.Drawing.SolidBrush(System.Drawing.Color.FromArgb(40, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(shadowBrush, 2, 2, panelLogin.Width, panelLogin.Height);
                }

                // رسم حدود أنيقة
                using (System.Drawing.Pen borderPen = new System.Drawing.Pen(System.Drawing.Color.FromArgb(134, 142, 150), 2))
                {
                    e.Graphics.DrawRectangle(borderPen, 0, 0, panelLogin.Width - 1, panelLogin.Height - 1);
                }

                // إضافة توهج داخلي أزرق خفيف
                using (System.Drawing.Pen glowPen = new System.Drawing.Pen(System.Drawing.Color.FromArgb(20, 0, 123, 255), 1))
                {
                    e.Graphics.DrawRectangle(glowPen, 1, 1, panelLogin.Width - 3, panelLogin.Height - 3);
                }
            };
        }

        private void AddTextBoxEffects()
        {
            // إضافة تأثيرات للتركيز على حقل كلمة المرور
            txtUser_Password.Enter += (s, e) => {
                txtUser_Password.BackColor = System.Drawing.Color.FromArgb(134, 142, 150);
                // إضافة حدود ملونة عند التركيز
                AddBorderEffect(txtUser_Password, System.Drawing.Color.FromArgb(0, 123, 255));
            };
            txtUser_Password.Leave += (s, e) => {
                txtUser_Password.BackColor = System.Drawing.Color.FromArgb(108, 117, 125);
                RemoveBorderEffect(txtUser_Password);
            };

            // إضافة تأثير placeholder
            txtUser_Password.GotFocus += (s, e) => {
                if (txtUser_Password.ForeColor == System.Drawing.Color.Gray)
                {
                    txtUser_Password.ForeColor = System.Drawing.Color.FromArgb(255, 255, 255);
                }
            };
        }

        private void AddBorderEffect(Control control, System.Drawing.Color borderColor)
        {
            control.Paint += (s, e) => {
                using (System.Drawing.Pen pen = new System.Drawing.Pen(borderColor, 2))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, control.Width - 1, control.Height - 1);
                }
            };
        }

        private void RemoveBorderEffect(Control control)
        {
            control.Invalidate();
        }

        private void AddComboBoxEffects()
        {
            // إضافة تأثيرات للتركيز على قائمة المستخدمين
            cmbUserName.Enter += (s, e) => {
                cmbUserName.BackColor = System.Drawing.Color.FromArgb(134, 142, 150);
                AddBorderEffect(cmbUserName, System.Drawing.Color.FromArgb(0, 123, 255));
            };
            cmbUserName.Leave += (s, e) => {
                cmbUserName.BackColor = System.Drawing.Color.FromArgb(108, 117, 125);
                RemoveBorderEffect(cmbUserName);
            };
        }

        private void AddButtonEffects()
        {
            // إضافة تأثيرات للزر
            btnLOGIN.MouseEnter += (s, e) => {
                btnLOGIN.Appearance.BackColor = System.Drawing.Color.FromArgb(0, 86, 179);
                // إضافة تأثير الظل
                btnLOGIN.Appearance.BorderColor = System.Drawing.Color.FromArgb(0, 62, 128);
            };
            btnLOGIN.MouseLeave += (s, e) => {
                btnLOGIN.Appearance.BackColor = System.Drawing.Color.FromArgb(0, 123, 255);
                btnLOGIN.Appearance.BorderColor = System.Drawing.Color.FromArgb(0, 86, 179);
            };

            // تأثير الضغط على الزر
            btnLOGIN.MouseDown += (s, e) => {
                btnLOGIN.Appearance.BackColor = System.Drawing.Color.FromArgb(0, 62, 128);
            };
            btnLOGIN.MouseUp += (s, e) => {
                btnLOGIN.Appearance.BackColor = System.Drawing.Color.FromArgb(0, 86, 179);
            };
        }
        SMART_CLINIC_SOFTWARE_2020.Classes.clsUSERS NCLSUSER = new Classes.clsUSERS();
        Classes.clsUSERS NclsUser = new Classes.clsUSERS();
        Classes.clsDOCTORS NclsDoc = new Classes.clsDOCTORS();
        
        private void frmLOGIN_Load(object sender, EventArgs e)
        {
            try
            {
                cmbUserName.DataSource = Classes.clsUSERS.USER_DATATABLE.GetData();
                cmbUserName.ValueMember = "USER_NAME";

                // تركيز على أول حقل
                cmbUserName.Focus();

                // إضافة placeholder text effect
                if (string.IsNullOrEmpty(txtUser_Password.Text))
                {
                    txtUser_Password.ForeColor = System.Drawing.Color.Gray;
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

        }
      
        public void LOGIN_DATA()
        {
            try
            {
                // التحقق من صحة البيانات المدخلة
                if (string.IsNullOrEmpty(cmbUserName.Text))
                {
                    MessageBox.Show("الرجاء اختيار اسم المستخدم", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbUserName.Focus();
                    return;
                }

                if (string.IsNullOrEmpty(txtUser_Password.Text))
                {
                    MessageBox.Show("الرجاء إدخال كلمة المرور", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUser_Password.Focus();
                    return;
                }

                // تغيير مؤشر الماوس لإظهار التحميل
                this.Cursor = Cursors.WaitCursor;
                btnLOGIN.Enabled = false;
                btnLOGIN.Text = "جاري التحقق...";

                DataTable dt = new DataTable();
                dt = NclsUser.USER_LOGIN(cmbUserName.Text, txtUser_Password.Text);

                if (Classes.clsUSERS.USER_ID == 0 && dt.Rows.Count == 0)
                {
                    MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtUser_Password.Text = "";
                    txtUser_Password.Focus();
                }
                else if (dt.Rows.Count == 1)
                {
                    MessageBox.Show("تم تسجيل الدخول بنجاح", "مرحباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    frmMAIN.GETNEWUSER.Show();
                    this.Hide();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // إعادة تعيين حالة الزر والمؤشر
                this.Cursor = Cursors.Default;
                btnLOGIN.Enabled = true;
                btnLOGIN.Text = "تسجيل الدخول";
            }
        }

        private void btnLOGIN_Click(object sender, EventArgs e)
        {
            LOGIN_DATA();
        }

        private void cmbUserName_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (Classes.clsUSERS.USER_DATATABLE.USER_LISTbyName(cmbUserName.Text).Rows.Count > 0)
                {
                    string userType = Classes.clsUSERS.USER_DATATABLE.USER_LISTbyName(cmbUserName.Text).Rows[0]["USER_T_TYPE"].ToString();
                    lblUSER_TYPE.Text = $"نوع المستخدم: {userType}";

                    // تغيير لون نوع المستخدم حسب النوع مع ألوان متناسقة مع التصميم الرصاصي
                    if (userType.Contains("DOCTOR"))
                    {
                        lblUSER_TYPE.ForeColor = System.Drawing.Color.FromArgb(40, 167, 69); // أخضر للأطباء
                        lblUSER_TYPE.Text = $"👨‍⚕️ طبيب - {userType}";
                    }
                    else if (userType.Contains("ADMIN"))
                    {
                        lblUSER_TYPE.ForeColor = System.Drawing.Color.FromArgb(220, 53, 69); // أحمر للمدراء
                        lblUSER_TYPE.Text = $"👑 مدير - {userType}";
                    }
                    else if (userType.Contains("NURSE"))
                    {
                        lblUSER_TYPE.ForeColor = System.Drawing.Color.FromArgb(102, 16, 242); // بنفسجي للممرضين
                        lblUSER_TYPE.Text = $"👩‍⚕️ ممرض - {userType}";
                    }
                    else if (userType.Contains("RECEPTION"))
                    {
                        lblUSER_TYPE.ForeColor = System.Drawing.Color.FromArgb(255, 193, 7); // أصفر للاستقبال
                        lblUSER_TYPE.Text = $"🏢 استقبال - {userType}";
                    }
                    else
                    {
                        lblUSER_TYPE.ForeColor = System.Drawing.Color.FromArgb(0, 123, 255); // أزرق للموظفين
                        lblUSER_TYPE.Text = $"👤 موظف - {userType}";
                    }

                    NclsDoc.DOC_LIST_BY_USER_ID(Classes.clsUSERS.USER_ID);
                    txtUser_Password.Focus();
                }
                else
                {
                    lblUSER_TYPE.Text = "نوع المستخدم";
                    lblUSER_TYPE.ForeColor = System.Drawing.Color.FromArgb(173, 181, 189);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد نوع المستخدم: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void txtUser_Password_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (e.KeyData == Keys.Enter)
                {
                    LOGIN_DATA();
                }
                else if (e.KeyData == Keys.F1)
                {
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default["ClinicDataBase_2020ConnectionString"] = "";
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME = "";
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME = "";
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD = "";
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                    Application.Exit();
                }
                else if (e.KeyData == Keys.F2)
                {
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_KEY = "0";
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_S_DATE = DateTime.Now;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_E_DATE = DateTime.Now;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnBACK_Click(object sender, EventArgs e)
        {
            try
            {
                Classes.clsDATABASE_CONNECTION config_database = new Classes.clsDATABASE_CONNECTION();
                config_database.DATABASE_CONFIG(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE);
                SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default["ClinicDataBase_2020ConnectionString"] = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE;
                SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                frmCLINIC_PANEL CliPanel = new frmCLINIC_PANEL();
                CliPanel.Show();
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void groupControl1_CustomButtonClick(object sender, DevExpress.XtraBars.Docking2010.BaseButtonEventArgs e)
        {
            try
            {
                if (e.Button.Properties.GroupIndex == 1)
                {
                    Application.Exit();
                }
                else if (e.Button.Properties.GroupIndex == 2)
                {
                    this.WindowState = FormWindowState.Minimized;
                }
                else if (e.Button.Properties.GroupIndex == 3)
                {
                    Classes.clsDATABASE_CONNECTION config_database = new Classes.clsDATABASE_CONNECTION();
                    //config_database.DATABASE_CONFIG(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE);
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default["ClinicDataBase_2020ConnectionString"] = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                    frmCLINIC_PANEL CliPanel = new frmCLINIC_PANEL();
                    CliPanel.Show();
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void chkShowPassword_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                // تغيير خاصية إظهار/إخفاء كلمة المرور
                if (chkShowPassword.Checked)
                {
                    txtUser_Password.PasswordChar = '\0'; // إظهار كلمة المرور
                    chkShowPassword.Text = "🙈 إخفاء كلمة المرور";
                    chkShowPassword.ForeColor = System.Drawing.Color.FromArgb(220, 53, 69); // أحمر
                }
                else
                {
                    txtUser_Password.PasswordChar = '●'; // إخفاء كلمة المرور
                    chkShowPassword.Text = "👁️ إظهار كلمة المرور";
                    chkShowPassword.ForeColor = System.Drawing.Color.FromArgb(173, 181, 189); // رمادي متناسق
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تغيير إعدادات كلمة المرور: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}