﻿using DevExpress.XtraBars;
using SMART_CLINIC_SOFTWARE_2020.LIST_FORM;
using SMART_CLINIC_SOFTWARE_2020.Main_Form;
using SMART_CLINIC_SOFTWARE_2020.Project_Form;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Reflection;
using System.Threading;
using Timer = System.Windows.Forms.Timer;

namespace SMART_CLINIC_SOFTWARE_2020
{
    public partial class frmMAIN : DevExpress.XtraBars.FluentDesignSystem.FluentDesignForm
    {

        frmLOGIN frmlogin = new frmLOGIN();
        Boolean FRM_OPEN = false;
        private static frmMAIN frmmain;
        FormCollection fCollection = Application.OpenForms;
        public frmMAIN()
        {
            InitializeComponent();
            accordionControl1.ElementClick += accordionControl1_ElementClick;
            if (frmmain == null)
               frmmain = this;

            // تطبيق التصميم الاحترافي
            ApplyModernDesign();
        }

        private void ApplyModernDesign()
        {
            // تحسين مظهر القائمة الجانبية
            accordionControl1.Appearance.AccordionControl.BackColor = Color.FromArgb(73, 80, 87);
            accordionControl1.Appearance.AccordionControl.ForeColor = Color.FromArgb(255, 255, 255);

            // تحسين مظهر الحاوي الرئيسي
            fluentDesignFormContainer1.Appearance.BackColor = Color.FromArgb(64, 68, 75);

            // تحسين الشريط العلوي
            fluentDesignFormControl1.BackColor = Color.FromArgb(33, 37, 41);
            fluentDesignFormControl1.ForeColor = Color.FromArgb(255, 255, 255);

            // تحسين خلفية النموذج
            this.Appearance.BackColor = Color.FromArgb(64, 68, 75);
            this.Appearance.ForeColor = Color.FromArgb(255, 255, 255);

            // تحسين رسالة الترحيب
            //lblWelcome.BackColor = Color.Transparent;

            // تطبيق الألوان على جميع عناصر القائمة
            ApplyElementColors();

            // إضافة تأثيرات الأزرار
            AddButtonEffects();
        }

        private void ApplyElementColors()
        {
            // تطبيق الألوان المتناسقة على جميع عناصر القائمة
            foreach (var element in accordionControl1.GetElements())
            {
                if (element.Style == DevExpress.XtraBars.Navigation.ElementStyle.Item)
                {
                    // الحالة العادية
                    element.Appearance.Normal.ForeColor = Color.FromArgb(240, 240, 240);
                    element.Appearance.Normal.Font = new Font("Droid Arabic Kufi", 14F);
                    element.Appearance.Normal.Options.UseForeColor = true;
                    element.Appearance.Normal.Options.UseFont = true;

                    // عند التمرير
                    element.Appearance.Hovered.BackColor = Color.FromArgb(0, 123, 255);
                    element.Appearance.Hovered.ForeColor = Color.FromArgb(255, 255, 255);
                    element.Appearance.Hovered.Font = new Font("Droid Arabic Kufi", 14F, FontStyle.Bold);
                    element.Appearance.Hovered.Options.UseBackColor = true;
                    element.Appearance.Hovered.Options.UseForeColor = true;
                    element.Appearance.Hovered.Options.UseFont = true;

                    // عند الضغط
                    element.Appearance.Pressed.BackColor = Color.FromArgb(0, 86, 179);
                    element.Appearance.Pressed.ForeColor = Color.FromArgb(255, 255, 255);
                    element.Appearance.Pressed.Font = new Font("Droid Arabic Kufi", 14F, FontStyle.Bold);
                    element.Appearance.Pressed.Options.UseBackColor = true;
                    element.Appearance.Pressed.Options.UseForeColor = true;
                    element.Appearance.Pressed.Options.UseFont = true;

                    // عند التعطيل
                    element.Appearance.Disabled.ForeColor = Color.FromArgb(173, 181, 189);
                    element.Appearance.Disabled.Font = new Font("Droid Arabic Kufi", 14F);
                    element.Appearance.Disabled.Options.UseForeColor = true;
                    element.Appearance.Disabled.Options.UseFont = true;
                }
            }
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            // إظهار رسالة تأكيد
            DialogResult result = MessageBox.Show(
                "هل أنت متأكد من تسجيل الخروج؟",
                "تأكيد تسجيل الخروج",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2,
                MessageBoxOptions.RightAlign | MessageBoxOptions.DefaultDesktopOnly
            );

            if (result == DialogResult.Yes)
            {
                // إخفاء الشاشة الحالية
                this.Hide();

                // إظهار شاشة تسجيل الدخول
                frmLOGIN loginForm = new frmLOGIN();
                loginForm.Show();

                // إغلاق الشاشة الحالية
                this.Close();
            }
        }

        private void AddButtonEffects()
        {
            // إضافة تأثيرات لزر تسجيل الخروج
            btnLogout.MouseEnter += (s, e) => {
                btnLogout.Appearance.BackColor = Color.FromArgb(200, 35, 51);
                btnLogout.Appearance.BorderColor = Color.FromArgb(180, 25, 41);
            };

            btnLogout.MouseLeave += (s, e) => {
                btnLogout.Appearance.BackColor = Color.FromArgb(220, 53, 69);
                btnLogout.Appearance.BorderColor = Color.FromArgb(200, 35, 51);
            };

            btnLogout.MouseDown += (s, e) => {
                btnLogout.Appearance.BackColor = Color.FromArgb(180, 25, 41);
            };

            btnLogout.MouseUp += (s, e) => {
                btnLogout.Appearance.BackColor = Color.FromArgb(200, 35, 51);
            };
        }

        private void LoadDashboard()
        {
            try
            {
                // مسح المحتوى الحالي أولاً
                fluentDesignFormContainer1.Controls.Clear();

                // إنشاء لوحة معلومات مؤقتة بدلاً من ucDashboard
                Panel dashboardPanel = CreateDashboardPanel();

                // إضافة اللوحة للحاوي
                fluentDesignFormContainer1.Controls.Add(dashboardPanel);

                // التأكد من ظهور اللوحة
                dashboardPanel.BringToFront();

                // تحديث العرض
                fluentDesignFormContainer1.Refresh();

                // إضافة الزر إلى الشريط العلوي
                SetupLogoutButton();

                // رسالة تأكيد
                MessageBox.Show("تم تحميل لوحة المعلومات بنجاح!", "نجح التحميل",
                    MessageBoxButtons.OK, MessageBoxIcon.Information,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل لوحة المعلومات: {ex.Message}\n\nStack Trace: {ex.StackTrace}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
            }
        }

        private Panel CreateDashboardPanel()
        {
            // إنشاء لوحة رئيسية
            Panel mainPanel = new Panel();
            mainPanel.Name = "dashboardPanel";
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.BackColor = Color.FromArgb(64, 68, 75);
            mainPanel.RightToLeft = RightToLeft.Yes;

            // إضافة عنوان
            Label titleLabel = new Label();
            titleLabel.Text = "📊 لوحة المعلومات";
            titleLabel.Font = new Font("Droid Arabic Kufi", 24F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(0, 123, 255);
            titleLabel.Location = new Point(300, 30);
            titleLabel.Size = new Size(320, 61);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.RightToLeft = RightToLeft.Yes;
            mainPanel.Controls.Add(titleLabel);

            // إضافة بطاقات الإحصائيات
            AddStatsCards(mainPanel);

            // إضافة بطاقة الوقت
            AddTimeCard(mainPanel);

            return mainPanel;
        }

        private void AddStatsCards(Panel parent)
        {
            // بطاقة المراجعين مع معلومات إضافية وأيقونات كبيرة
            int totalPatients = GetPatientsCount();
            int newPatientsToday = GetNewPatientsToday();
            string patientsInfo = $"👥 إجمالي المراجعين\n📊 جدد اليوم: {newPatientsToday}";
            Panel patientsCard = CreateStatsCardWithLargeIcons(patientsInfo, totalPatients.ToString(), Color.FromArgb(40, 167, 69), new Point(50, 120), "👥");
            patientsCard.Name = "patientsCard";
            parent.Controls.Add(patientsCard);

            // بطاقة المواعيد - مع معلومات تفصيلية وأيقونات كبيرة
            string todayDate = DateTime.Now.ToString("dd/MM/yyyy");
            int totalAppointments = GetTodayAppointments();
            int remainingAppointments = GetRemainingAppointmentsToday();
            string nextAppointment = GetNextAppointmentTime();
            string appointmentsInfo = $"📅 مواعيد اليوم {todayDate}\n⏰ القادم: {nextAppointment} | متبقي: {remainingAppointments}";
            Panel appointmentsCard = CreateStatsCardWithLargeIcons(appointmentsInfo, totalAppointments.ToString(), Color.FromArgb(0, 123, 255), new Point(320, 120), "📅");
            appointmentsCard.Name = "appointmentsCard";
            parent.Controls.Add(appointmentsCard);

            // بطاقة الخزنة - مع معلومات تفصيلية وإظهار الملايين وأيقونات كبيرة
            decimal totalRevenue = GetTotalRevenue();
            int stockCount = GetTotalStockCount();
            string mainStockName = GetMainStockName();
            string revenueInfo = $"💰 إجمالي الخزنة\n🏦 {stockCount} خزنة | {mainStockName}";
            Panel revenueCard = CreateStatsCardWithDetailedAmountAndLargeIcons(revenueInfo, totalRevenue, Color.FromArgb(255, 193, 7), new Point(590, 120), "💰");
            revenueCard.Name = "revenueCard";
            parent.Controls.Add(revenueCard);

            // إضافة مؤقت لتحديث البيانات كل 30 ثانية
            Timer dataTimer = new Timer();
            dataTimer.Interval = 1000; // 30 ثانية
            dataTimer.Tick += (s, e) => UpdateStatsData(parent);
            dataTimer.Start();
        }

        private int GetPatientsCount()
        {
            try
            {
                // الحصول على العدد الفعلي للمرضى من قاعدة البيانات
                DataTable dt = Classes.clsCUST.CUST_DATATABLE.GetData();

                // تصفية المرضى حسب العيادة الحالية إذا كان هناك معرف عيادة
                if (Classes.clsCLINIC.CLI_ID > 0)
                {
                    // عد المرضى الذين ينتمون للعيادة الحالية
                    int count = 0;
                    foreach (DataRow row in dt.Rows)
                    {
                        if (Convert.ToInt64(row["CLI_ID"]) == Classes.clsCLINIC.CLI_ID)
                        {
                            count++;
                        }
                    }
                    return count;
                }
                else
                {
                    // إرجاع العدد الكلي لجميع المرضى
                    return dt.Rows.Count;
                }
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، إرجاع قيمة افتراضية
                return 0;
            }
        }

        // دالة للحصول على عدد المرضى الجدد اليوم
        private int GetNewPatientsToday()
        {
            try
            {
                DataTable dt = Classes.clsCUST.CUST_DATATABLE.GetData();
                int count = 0;
                DateTime today = DateTime.Today;

                foreach (DataRow row in dt.Rows)
                {
                    // التحقق من تاريخ إضافة المريض (إذا كان متوفراً)
                    if (row["CUST_BD"] != DBNull.Value)
                    {
                        DateTime custDate = Convert.ToDateTime(row["CUST_BD"]);
                        if (custDate.Date == today &&
                            (Classes.clsCLINIC.CLI_ID == 0 || Convert.ToInt64(row["CLI_ID"]) == Classes.clsCLINIC.CLI_ID))
                        {
                            count++;
                        }
                    }
                }
                return count;
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        private int GetTodayAppointments()
        {
            try
            {
                // الحصول على العدد الفعلي للمواعيد من قاعدة البيانات
                DataTable dt = Classes.clsAPO.APO_DATATABLE.GetData();
                int count = 0;
                DateTime today = DateTime.Today;

                foreach (DataRow row in dt.Rows)
                {
                    if (row["APO_DATE"] != DBNull.Value)
                    {
                        DateTime apoDate = Convert.ToDateTime(row["APO_DATE"]);
                        // التحقق من أن الموعد لليوم الحالي
                        if (apoDate.Date == today)
                        {
                            // تصفية حسب العيادة إذا كان هناك معرف عيادة
                            if (Classes.clsCLINIC.CLI_ID == 0 ||
                                (row["CLI_ID"] != DBNull.Value && Convert.ToInt64(row["CLI_ID"]) == Classes.clsCLINIC.CLI_ID))
                            {
                                count++;
                            }
                        }
                    }
                }
                return count;
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، إرجاع قيمة افتراضية
                return 0;
            }
        }

        // دالة للحصول على عدد المواعيد المكتملة اليوم
        private int GetCompletedAppointmentsToday()
        {
            try
            {
                DataTable dt = Classes.clsAPO.APO_DATATABLE.GetData();
                int count = 0;
                DateTime today = DateTime.Today;
                DateTime now = DateTime.Now;

                foreach (DataRow row in dt.Rows)
                {
                    if (row["APO_DATE"] != DBNull.Value && row["APO_TIME"] != DBNull.Value)
                    {
                        DateTime apoDate = Convert.ToDateTime(row["APO_DATE"]);
                        TimeSpan apoTime = (TimeSpan)row["APO_TIME"];

                        // التحقق من أن الموعد لليوم الحالي ووقته قد مضى
                        if (apoDate.Date == today)
                        {
                            DateTime appointmentDateTime = apoDate.Date.Add(apoTime);
                            if (appointmentDateTime < now)
                            {
                                // تصفية حسب العيادة
                                if (Classes.clsCLINIC.CLI_ID == 0 ||
                                    (row["CLI_ID"] != DBNull.Value && Convert.ToInt64(row["CLI_ID"]) == Classes.clsCLINIC.CLI_ID))
                                {
                                    count++;
                                }
                            }
                        }
                    }
                }
                return count;
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        // دالة للحصول على عدد المواعيد المتبقية اليوم
        private int GetRemainingAppointmentsToday()
        {
            try
            {
                int totalToday = GetTodayAppointments();
                int completedToday = GetCompletedAppointmentsToday();
                return totalToday - completedToday;
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        // دالة للحصول على الموعد القادم اليوم
        private string GetNextAppointmentTime()
        {
            try
            {
                DataTable dt = Classes.clsAPO.APO_DATATABLE.GetData();
                DateTime today = DateTime.Today;
                DateTime now = DateTime.Now;
                TimeSpan? nextTime = null;

                foreach (DataRow row in dt.Rows)
                {
                    if (row["APO_DATE"] != DBNull.Value && row["APO_TIME"] != DBNull.Value)
                    {
                        DateTime apoDate = Convert.ToDateTime(row["APO_DATE"]);
                        TimeSpan apoTime = (TimeSpan)row["APO_TIME"];

                        // التحقق من أن الموعد لليوم الحالي ولم يحن وقته بعد
                        if (apoDate.Date == today)
                        {
                            DateTime appointmentDateTime = apoDate.Date.Add(apoTime);
                            if (appointmentDateTime > now)
                            {
                                // تصفية حسب العيادة
                                if (Classes.clsCLINIC.CLI_ID == 0 ||
                                    (row["CLI_ID"] != DBNull.Value && Convert.ToInt64(row["CLI_ID"]) == Classes.clsCLINIC.CLI_ID))
                                {
                                    if (nextTime == null || apoTime < nextTime)
                                    {
                                        nextTime = apoTime;
                                    }
                                }
                            }
                        }
                    }
                }

                if (nextTime.HasValue)
                {
                    return nextTime.Value.ToString(@"hh\:mm");
                }
                else
                {
                    return "لا توجد";
                }
            }
            catch (Exception ex)
            {
                return "غير محدد";
            }
        }

        private decimal GetTotalRevenue()
        {
            try
            {
                // الحصول على المبلغ الفعلي للخزنة من قاعدة البيانات
                DataTable dt = Classes.clsSTOCK_DATA.STOCK_DATATABLE.GetData();
                decimal totalAmount = 0;

                foreach (DataRow row in dt.Rows)
                {
                    if (row["Money"] != DBNull.Value)
                    {
                        decimal stockMoney = Convert.ToDecimal(row["Money"]);
                        totalAmount += stockMoney;
                    }
                }

                return totalAmount;
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، إرجاع قيمة افتراضية
                return 0;
            }
        }

        // دالة للحصول على عدد الخزائن
        private int GetTotalStockCount()
        {
            try
            {
                DataTable dt = Classes.clsSTOCK_DATA.STOCK_DATATABLE.GetData();
                return dt.Rows.Count;
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        // دالة للحصول على أكبر رصيد في الخزائن
        private decimal GetMaxStockAmount()
        {
            try
            {
                DataTable dt = Classes.clsSTOCK_DATA.STOCK_DATATABLE.GetData();
                decimal maxAmount = 0;

                foreach (DataRow row in dt.Rows)
                {
                    if (row["Money"] != DBNull.Value)
                    {
                        decimal stockMoney = Convert.ToDecimal(row["Money"]);
                        if (stockMoney > maxAmount)
                        {
                            maxAmount = stockMoney;
                        }
                    }
                }

                return maxAmount;
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        // دالة للحصول على اسم الخزنة الرئيسية
        private string GetMainStockName()
        {
            try
            {
                DataTable dt = Classes.clsSTOCK_DATA.STOCK_DATATABLE.GetData();
                decimal maxAmount = 0;
                string mainStockName = "الخزنة الرئيسية";

                foreach (DataRow row in dt.Rows)
                {
                    if (row["Money"] != DBNull.Value && row["Stock_Name"] != DBNull.Value)
                    {
                        decimal stockMoney = Convert.ToDecimal(row["Money"]);
                        string stockName = row["Stock_Name"].ToString();

                        // البحث عن الخزنة الرئيسية أو أكبر رصيد
                        if (stockName.Contains("الرئيسية") || stockName.Contains("رئيسية") || stockMoney > maxAmount)
                        {
                            maxAmount = stockMoney;
                            mainStockName = stockName;
                        }
                    }
                }

                return mainStockName;
            }
            catch (Exception ex)
            {
                return "الخزنة الرئيسية";
            }
        }

        // دالة للحصول على آخر تحديث للخزنة
        private DateTime GetLastStockUpdate()
        {
            try
            {
                // يمكن إضافة استعلام للحصول على آخر تحديث من جدول Stock_Insert
                DataTable dt = Classes.clsStock_AddMoney.STOCK_INSERT_DATATABLE.GetData();
                DateTime lastUpdate = DateTime.Today;

                foreach (DataRow row in dt.Rows)
                {
                    if (row["Date"] != DBNull.Value)
                    {
                        DateTime updateDate = Convert.ToDateTime(row["Date"]);
                        if (updateDate > lastUpdate)
                        {
                            lastUpdate = updateDate;
                        }
                    }
                }

                return lastUpdate;
            }
            catch (Exception ex)
            {
                return DateTime.Today;
            }
        }

        private string FormatIraqiDinar(decimal amount)
        {
            // تنسيق المبلغ بالدينار العراقي مع إظهار الملايين
            if (amount >= 1000000)
            {
                // إذا كان المبلغ مليون أو أكثر
                decimal millions = amount / 1000000;
                if (millions >= 1000)
                {
                    // إذا كان المبلغ مليار أو أكثر
                    decimal billions = millions / 1000;
                    return billions.ToString("N2") + " مليار د.ع";
                }
                else
                {
                    return millions.ToString("N2") + " مليون د.ع";
                }
            }
            else if (amount >= 1000)
            {
                // إذا كان المبلغ ألف أو أكثر
                decimal thousands = amount / 1000;
                return thousands.ToString("N2") + " ألف د.ع";
            }
            else
            {
                // إذا كان المبلغ أقل من ألف
                return amount.ToString("N0") + " د.ع";
            }
        }

        // دالة لتنسيق المبلغ بالتفصيل (للعرض الكامل)
        private string FormatIraqiDinarDetailed(decimal amount)
        {
            // تنسيق المبلغ بالتفصيل مع فواصل الآلاف
            string formattedAmount = amount.ToString("N0");

            // إضافة وصف الملايين
            if (amount >= 1000000)
            {
                decimal millions = amount / 1000000;
                if (millions >= 1000)
                {
                    decimal billions = millions / 1000;
                    return $"{formattedAmount} د.ع\n({billions:N2} مليار)";
                }
                else
                {
                    return $"{formattedAmount} د.ع\n({millions:N2} مليون)";
                }
            }
            else if (amount >= 1000)
            {
                decimal thousands = amount / 1000;
                return $"{formattedAmount} د.ع\n({thousands:N2} ألف)";
            }
            else
            {
                return formattedAmount + " د.ع";
            }
        }

        private void UpdateStatsData(Panel parent)
        {
            try
            {
                // تحديث بيانات المراجعين
                Panel patientsCard = parent.Controls.Find("patientsCard", false).FirstOrDefault() as Panel;
                if (patientsCard != null)
                {
                    // تحديث العنوان مع المعلومات الجديدة
                    Label titleLabel = patientsCard.Controls.OfType<Label>().FirstOrDefault();
                    Label valueLabel = patientsCard.Controls.OfType<Label>().LastOrDefault();

                    if (titleLabel != null && valueLabel != null)
                    {
                        int totalPatients = GetPatientsCount();
                        int newPatientsToday = GetNewPatientsToday();

                        titleLabel.Text = $"👥 إجمالي المراجعين\n📊 جدد اليوم: {newPatientsToday}";
                        valueLabel.Text = totalPatients.ToString();

                        AnimateUpdate(valueLabel);
                        AnimateUpdate(titleLabel);
                    }
                }

                // تحديث بيانات المواعيد
                Panel appointmentsCard = parent.Controls.Find("appointmentsCard", false).FirstOrDefault() as Panel;
                if (appointmentsCard != null)
                {
                    Label titleLabel = appointmentsCard.Controls.OfType<Label>().FirstOrDefault();
                    Label valueLabel = appointmentsCard.Controls.OfType<Label>().LastOrDefault();

                    if (titleLabel != null && valueLabel != null)
                    {
                        string todayDate = DateTime.Now.ToString("dd/MM/yyyy");
                        int totalAppointments = GetTodayAppointments();
                        int remainingAppointments = GetRemainingAppointmentsToday();
                        string nextAppointment = GetNextAppointmentTime();

                        titleLabel.Text = $"📅 مواعيد اليوم {todayDate}\n⏰ القادم: {nextAppointment} | متبقي: {remainingAppointments}";
                        valueLabel.Text = totalAppointments.ToString();

                        AnimateUpdate(valueLabel);
                        AnimateUpdate(titleLabel);
                    }
                }

                // تحديث بيانات الخزنة
                Panel revenueCard = parent.Controls.Find("revenueCard", false).FirstOrDefault() as Panel;
                if (revenueCard != null)
                {
                    Label titleLabel = revenueCard.Controls.OfType<Label>().FirstOrDefault();
                    Label valueLabel = revenueCard.Controls.OfType<Label>().LastOrDefault();

                    if (titleLabel != null && valueLabel != null)
                    {
                        decimal totalRevenue = GetTotalRevenue();
                        int stockCount = GetTotalStockCount();
                        string mainStockName = GetMainStockName();

                        titleLabel.Text = $"💰 إجمالي الخزنة\n🏦 {stockCount} خزنة | {mainStockName}";
                        valueLabel.Text = FormatIraqiDinarDetailed(totalRevenue);

                        AnimateUpdate(valueLabel);
                        AnimateUpdate(titleLabel);
                    }
                }
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في التحديث التلقائي
            }
        }

        private void AnimateUpdate(Label label)
        {
            // تأثير بصري عند تحديث البيانات
            Color originalColor = label.ForeColor;
            label.ForeColor = Color.Yellow;

            Timer animationTimer = new Timer();
            animationTimer.Interval = 800;
            animationTimer.Tick += (s, e) => {
                label.ForeColor = originalColor;
                animationTimer.Stop();
                animationTimer.Dispose();
            };
            animationTimer.Start();
        }

        // دالة عامة لتحديث لوحة المعلومات من الخارج
        public static void RefreshDashboard()
        {
            try
            {
                if (frmmain != null)
                {
                    // البحث عن لوحة المعلومات في الحاوي الرئيسي
                    Panel dashboardPanel = frmmain.fluentDesignFormContainer1.Controls
                        .OfType<Panel>()
                        .FirstOrDefault(p => p.Name == "dashboardPanel");

                    if (dashboardPanel != null)
                    {
                        frmmain.UpdateStatsData(dashboardPanel);
                    }
                }
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في التحديث
            }
        }

        // دالة لتحديث عدد المرضى فقط (للاستدعاء السريع)
        public static void RefreshPatientsCount()
        {
            try
            {
                if (frmmain != null)
                {
                    Panel dashboardPanel = frmmain.fluentDesignFormContainer1.Controls
                        .OfType<Panel>()
                        .FirstOrDefault(p => p.Name == "dashboardPanel");

                    if (dashboardPanel != null)
                    {
                        Panel patientsCard = dashboardPanel.Controls.Find("patientsCard", false).FirstOrDefault() as Panel;
                        if (patientsCard != null)
                        {
                            Label titleLabel = patientsCard.Controls.OfType<Label>().FirstOrDefault();
                            Label valueLabel = patientsCard.Controls.OfType<Label>().LastOrDefault();

                            if (titleLabel != null && valueLabel != null)
                            {
                                int totalPatients = frmmain.GetPatientsCount();
                                int newPatientsToday = frmmain.GetNewPatientsToday();

                                titleLabel.Text = $"👥 إجمالي المراجعين\n📊 جدد اليوم: {newPatientsToday}";
                                valueLabel.Text = totalPatients.ToString();

                                // تحديث الأيقونة الكبيرة إذا وجدت
                                Label iconLabel = patientsCard.Controls.OfType<Label>().FirstOrDefault(l => l.Font.Size >= 30);
                                if (iconLabel != null && iconLabel.Text == "👥")
                                {
                                    frmmain.AnimateUpdate(iconLabel);
                                }

                                frmmain.AnimateUpdate(valueLabel);
                                frmmain.AnimateUpdate(titleLabel);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في التحديث
            }
        }

        // دالة لتحديث عدد المواعيد فقط (للاستدعاء السريع)
        public static void RefreshAppointmentsCount()
        {
            try
            {
                if (frmmain != null)
                {
                    Panel dashboardPanel = frmmain.fluentDesignFormContainer1.Controls
                        .OfType<Panel>()
                        .FirstOrDefault(p => p.Name == "dashboardPanel");

                    if (dashboardPanel != null)
                    {
                        Panel appointmentsCard = dashboardPanel.Controls.Find("appointmentsCard", false).FirstOrDefault() as Panel;
                        if (appointmentsCard != null)
                        {
                            Label titleLabel = appointmentsCard.Controls.OfType<Label>().FirstOrDefault();
                            Label valueLabel = appointmentsCard.Controls.OfType<Label>().LastOrDefault();

                            if (titleLabel != null && valueLabel != null)
                            {
                                string todayDate = DateTime.Now.ToString("dd/MM/yyyy");
                                int totalAppointments = frmmain.GetTodayAppointments();
                                int remainingAppointments = frmmain.GetRemainingAppointmentsToday();
                                string nextAppointment = frmmain.GetNextAppointmentTime();

                                titleLabel.Text = $"📅 مواعيد اليوم {todayDate}\n⏰ القادم: {nextAppointment} | متبقي: {remainingAppointments}";
                                valueLabel.Text = totalAppointments.ToString();

                                // تحديث الأيقونة الكبيرة إذا وجدت
                                Label iconLabel = appointmentsCard.Controls.OfType<Label>().FirstOrDefault(l => l.Font.Size >= 40);
                                if (iconLabel != null && iconLabel.Text == "📅")
                                {
                                    frmmain.AnimateUpdate(iconLabel);
                                }

                                frmmain.AnimateUpdate(valueLabel);
                                frmmain.AnimateUpdate(titleLabel);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في التحديث
            }
        }

        // دالة لتحديث مبلغ الخزنة فقط (للاستدعاء السريع)
        public static void RefreshRevenueAmount()
        {
            try
            {
                if (frmmain != null)
                {
                    Panel dashboardPanel = frmmain.fluentDesignFormContainer1.Controls
                        .OfType<Panel>()
                        .FirstOrDefault(p => p.Name == "dashboardPanel");

                    if (dashboardPanel != null)
                    {
                        Panel revenueCard = dashboardPanel.Controls.Find("revenueCard", false).FirstOrDefault() as Panel;
                        if (revenueCard != null)
                        {
                            Label titleLabel = revenueCard.Controls.OfType<Label>().FirstOrDefault();
                            Label valueLabel = revenueCard.Controls.OfType<Label>().LastOrDefault();

                            if (titleLabel != null && valueLabel != null)
                            {
                                decimal totalRevenue = frmmain.GetTotalRevenue();
                                int stockCount = frmmain.GetTotalStockCount();
                                string mainStockName = frmmain.GetMainStockName();

                                titleLabel.Text = $"💰 إجمالي الخزنة\n🏦 {stockCount} خزنة | {mainStockName}";
                                valueLabel.Text = frmmain.FormatIraqiDinarDetailed(totalRevenue);

                                // تحديث الأيقونة الكبيرة إذا وجدت
                                Label iconLabel = revenueCard.Controls.OfType<Label>().FirstOrDefault(l => l.Font.Size >= 30);
                                if (iconLabel != null && iconLabel.Text == "💰")
                                {
                                    frmmain.AnimateUpdate(iconLabel);
                                }

                                frmmain.AnimateUpdate(valueLabel);
                                frmmain.AnimateUpdate(titleLabel);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في التحديث
            }
        }

        private Panel CreateStatsCard(string title, string value, Color backColor, Point location)
        {
            Panel card = new Panel();
            card.Size = new Size(240, 160);
            card.Location = location;
            card.BackColor = backColor;
            card.BorderStyle = BorderStyle.FixedSingle;

            // إضافة تأثير الظل
            card.Paint += (s, e) => {
                ControlPaint.DrawBorder3D(e.Graphics, card.ClientRectangle, Border3DStyle.Raised);
            };

            // عنوان البطاقة - مع دعم النصوص متعددة الأسطر
            Label titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("Droid Arabic Kufi", 11F, FontStyle.Bold);
            titleLabel.ForeColor = Color.White;
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(220, 50);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.RightToLeft = RightToLeft.Yes;
            titleLabel.AutoSize = false;
            card.Controls.Add(titleLabel);

            // قيمة البطاقة
            Label valueLabel = new Label();
            valueLabel.Text = value;
            valueLabel.Font = new Font("Droid Arabic Kufi", 16F, FontStyle.Bold);
            valueLabel.ForeColor = backColor == Color.FromArgb(255, 193, 7) ? Color.FromArgb(33, 37, 41) : Color.White;
            valueLabel.Location = new Point(10, 70);
            valueLabel.Size = new Size(220, 80);
            valueLabel.TextAlign = ContentAlignment.MiddleCenter;
            valueLabel.RightToLeft = RightToLeft.Yes;
            valueLabel.AutoSize = false;
            card.Controls.Add(valueLabel);

            // إضافة تأثير التمرير
            card.MouseEnter += (s, e) => {
                card.BackColor = ControlPaint.Light(backColor, 0.1f);
            };

            card.MouseLeave += (s, e) => {
                card.BackColor = backColor;
            };

            return card;
        }

        private Panel CreateStatsCardWithDetailedAmount(string title, decimal amount, Color backColor, Point location)
        {
            Panel card = new Panel();
            card.Size = new Size(240, 160);
            card.Location =location;
            card.BackColor = backColor;
            card.BorderStyle = BorderStyle.FixedSingle;

            // إضافة تأثير الظل
            card.Paint += (s, e) => {
                ControlPaint.DrawBorder3D(e.Graphics, card.ClientRectangle, Border3DStyle.Raised);
            };

            // عنوان البطاقة - مع دعم النصوص متعددة الأسطر
            Label titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("Droid Arabic Kufi", 11F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(33, 37, 41);
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(220, 50);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.RightToLeft = RightToLeft.Yes;
            titleLabel.AutoSize = false;
            card.Controls.Add(titleLabel);

            // قيمة البطاقة - مع التنسيق المفصل
            Label valueLabel = new Label();
            valueLabel.Text = FormatIraqiDinarDetailed(amount);
            valueLabel.Font = new Font("Droid Arabic Kufi", 12F, FontStyle.Bold);
            valueLabel.ForeColor = Color.FromArgb(33, 37, 41);
            valueLabel.Location = new Point(10, 200);
            valueLabel.Size = new Size(220, 80);
            valueLabel.TextAlign = ContentAlignment.MiddleCenter;
            valueLabel.RightToLeft = RightToLeft.Yes;
            valueLabel.AutoSize = false;
            card.Controls.Add(valueLabel);

            // إضافة تأثير التمرير
            card.MouseEnter += (s, e) => {
                card.BackColor = ControlPaint.Light(backColor, 0.1f);
            };

            card.MouseLeave += (s, e) => {
                card.BackColor = backColor;
            };

            return card;
        }

        private Panel CreateStatsCardWithLargeIcons(string title, string value, Color backColor, Point location, string mainIcon)
        {
            Panel card = new Panel();
            card.Size = new Size(240, 160);
            card.Location = location;
            card.BackColor = backColor;
            card.BorderStyle = BorderStyle.FixedSingle;

            // إضافة تأثير الظل
            card.Paint += (s, e) => {
                ControlPaint.DrawBorder3D(e.Graphics, card.ClientRectangle, Border3DStyle.Raised);
            };

            // أيقونة كبيرة جداً في الجانب الأيمن
            Label iconLabel = new Label();
            iconLabel.Text = mainIcon;
            iconLabel.Font = new Font("Segoe UI Emoji", 36F, FontStyle.Regular);
            iconLabel.ForeColor = Color.White;
            iconLabel.Location = new Point(10, 15);
            iconLabel.Size = new Size(60, 60);
            iconLabel.TextAlign = ContentAlignment.MiddleCenter;
            iconLabel.BackColor = Color.Transparent;
            iconLabel.RightToLeft = RightToLeft.Yes;
            card.Controls.Add(iconLabel);

            // عنوان البطاقة - بجانب الأيقونة من اليسار
            Label titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("Droid Arabic Kufi", 10F, FontStyle.Bold);
            titleLabel.ForeColor = Color.White;
            titleLabel.Location = new Point(80, 10);
            titleLabel.Size = new Size(150, 50);
            titleLabel.TextAlign = ContentAlignment.TopRight;
            titleLabel.RightToLeft = RightToLeft.Yes;
            titleLabel.AutoSize = false;
            card.Controls.Add(titleLabel);

            // قيمة البطاقة - في المنتصف السفلي
            Label valueLabel = new Label();
            valueLabel.Text = value;
            valueLabel.Font = new Font("Droid Arabic Kufi", 24F, FontStyle.Bold);
            valueLabel.ForeColor = backColor == Color.FromArgb(255, 193, 7) ? Color.FromArgb(33, 37, 41) : Color.White;
            valueLabel.Location = new Point(10, 80);
            valueLabel.Size = new Size(220, 70);
            valueLabel.TextAlign = ContentAlignment.MiddleCenter;
            valueLabel.RightToLeft = RightToLeft.Yes;
            valueLabel.AutoSize = false;
            card.Controls.Add(valueLabel);

            // إضافة تأثيرات التمرير المحسّنة للأيقونات الكبيرة
            card.MouseEnter += (s, e) => {
                card.BackColor = ControlPaint.Light(backColor, 0.1f);
                iconLabel.ForeColor = Color.FromArgb(255, 255, 0); // أصفر عند التمرير
                iconLabel.Font = new Font("Segoe UI Emoji", 40F, FontStyle.Regular); // تكبير عند التمرير

                // تأثير نبضة للأيقونة
                Timer pulseTimer = new Timer();
                pulseTimer.Interval = 100;
                int pulseCount = 0;
                pulseTimer.Tick += (ts, te) => {
                    pulseCount++;
                    if (pulseCount % 2 == 0)
                        iconLabel.Font = new Font("Segoe UI Emoji", 38F, FontStyle.Regular);
                    else
                        iconLabel.Font = new Font("Segoe UI Emoji", 42F, FontStyle.Regular);

                    if (pulseCount >= 6) {
                        pulseTimer.Stop();
                        iconLabel.Font = new Font("Segoe UI Emoji", 40F, FontStyle.Regular);
                    }
                };
                pulseTimer.Start();
            };

            card.MouseLeave += (s, e) => {
                card.BackColor = backColor;
                iconLabel.ForeColor = Color.White;
                iconLabel.Font = new Font("Segoe UI Emoji", 36F, FontStyle.Regular); // العودة للحجم الأصلي
            };

            return card;
        }

        private Panel CreateStatsCardWithDetailedAmountAndLargeIcons(string title, decimal amount, Color backColor, Point location, string mainIcon)
        {
            Panel card = new Panel();
            card.Size = new Size(240, 160);
            card.Location = location;
            card.BackColor = backColor;
            card.BorderStyle = BorderStyle.FixedSingle;

            // إضافة تأثير الظل
            card.Paint += (s, e) => {
                ControlPaint.DrawBorder3D(e.Graphics, card.ClientRectangle, Border3DStyle.Raised);
            };

            // أيقونة كبيرة جداً في الجانب الأيمن
            Label iconLabel = new Label();
            iconLabel.Text = mainIcon;
            iconLabel.Font = new Font("Segoe UI Emoji", 36F, FontStyle.Regular);
            iconLabel.ForeColor = Color.FromArgb(33, 37, 41);
            iconLabel.Location = new Point(10, 15);
            iconLabel.Size = new Size(60, 60);
            iconLabel.TextAlign = ContentAlignment.MiddleCenter;
            iconLabel.BackColor = Color.Transparent;
            iconLabel.RightToLeft = RightToLeft.Yes;
            card.Controls.Add(iconLabel);

            // عنوان البطاقة - بجانب الأيقونة من اليسار
            Label titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("Droid Arabic Kufi", 10F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(33, 37, 41);
            titleLabel.Location = new Point(80, 10);
            titleLabel.Size = new Size(150, 50);
            titleLabel.TextAlign = ContentAlignment.TopRight;
            titleLabel.RightToLeft = RightToLeft.Yes;
            titleLabel.AutoSize = false;
            card.Controls.Add(titleLabel);

            // قيمة البطاقة - مع التنسيق المفصل في المنتصف السفلي
            Label valueLabel = new Label();
            valueLabel.Text = FormatIraqiDinarDetailed(amount);
            valueLabel.Font = new Font("Droid Arabic Kufi", 12F, FontStyle.Bold);
            valueLabel.ForeColor = Color.FromArgb(33, 37, 41);
            valueLabel.Location = new Point(10, 80);
            valueLabel.Size = new Size(220, 70);
            valueLabel.TextAlign = ContentAlignment.MiddleCenter;
            valueLabel.RightToLeft = RightToLeft.Yes;
            valueLabel.AutoSize = false;
            card.Controls.Add(valueLabel);

            // إضافة تأثيرات التمرير المحسّنة للخزنة
            card.MouseEnter += (s, e) => {
                card.BackColor = ControlPaint.Light(backColor, 0.1f);
                iconLabel.ForeColor = Color.FromArgb(255, 0, 0); // أحمر عند التمرير
                iconLabel.Font = new Font("Segoe UI Emoji", 40F, FontStyle.Regular); // تكبير عند التمرير

                // تأثير نبضة للأيقونة
                Timer pulseTimer = new Timer();
                pulseTimer.Interval = 100;
                int pulseCount = 0;
                pulseTimer.Tick += (ts, te) => {
                    pulseCount++;
                    if (pulseCount % 2 == 0)
                        iconLabel.Font = new Font("Segoe UI Emoji", 38F, FontStyle.Regular);
                    else
                        iconLabel.Font = new Font("Segoe UI Emoji", 42F, FontStyle.Regular);

                    if (pulseCount >= 6) {
                        pulseTimer.Stop();
                        iconLabel.Font = new Font("Segoe UI Emoji", 40F, FontStyle.Regular);
                    }
                };
                pulseTimer.Start();
            };

            card.MouseLeave += (s, e) => {
                card.BackColor = backColor;
                iconLabel.ForeColor = Color.FromArgb(33, 37, 41);
                iconLabel.Font = new Font("Segoe UI Emoji", 36F, FontStyle.Regular); // العودة للحجم الأصلي
            };

            return card;
        }

        private void AddTimeCard(Panel parent)
        {
            Panel timeCard = new Panel();
            timeCard.Size = new Size(320, 140);
            timeCard.Location = new Point(400, 500);
            timeCard.BackColor = Color.FromArgb(108, 117, 125);
            timeCard.BorderStyle = BorderStyle.FixedSingle;

            // إضافة تأثير الظل
            timeCard.Paint += (s, e) => {
                ControlPaint.DrawBorder3D(e.Graphics, timeCard.ClientRectangle, Border3DStyle.Raised);
            };

            // التاريخ بالعربية
            Label dateLabel = new Label();
            dateLabel.Text = GetArabicDate();
            dateLabel.Font = new Font("Droid Arabic Kufi", 12F, FontStyle.Bold);
            dateLabel.ForeColor = Color.White;
            dateLabel.Location = new Point(10, 10);
            dateLabel.Size = new Size(300, 35);
            dateLabel.TextAlign = ContentAlignment.MiddleCenter;
            dateLabel.RightToLeft = RightToLeft.Yes;
            dateLabel.AutoSize = false;
            timeCard.Controls.Add(dateLabel);

            // الوقت الحالي
            Label timeLabel = new Label();
            timeLabel.Text = DateTime.Now.ToString("HH:mm:ss");
            timeLabel.Font = new Font("Droid Arabic Kufi", 20F, FontStyle.Bold);
            timeLabel.ForeColor = Color.FromArgb(255, 255, 255);
            timeLabel.Location = new Point(10, 50);
            timeLabel.Size = new Size(300, 45);
            timeLabel.TextAlign = ContentAlignment.MiddleCenter;
            timeLabel.RightToLeft = RightToLeft.Yes;
            timeLabel.AutoSize = false;
            timeCard.Controls.Add(timeLabel);

            // معلومات إضافية
            Label infoLabel = new Label();
            infoLabel.Text = "🕐 التوقيت المحلي - العراق";
            infoLabel.Font = new Font("Droid Arabic Kufi", 10F, FontStyle.Regular);
            infoLabel.ForeColor = Color.FromArgb(220, 220, 220);
            infoLabel.Location = new Point(10, 105);
            infoLabel.Size = new Size(300, 25);
            infoLabel.TextAlign = ContentAlignment.MiddleCenter;
            infoLabel.RightToLeft = RightToLeft.Yes;
            infoLabel.AutoSize = false;
            timeCard.Controls.Add(infoLabel);

            parent.Controls.Add(timeCard);

            // إضافة مؤقت لتحديث الوقت والتاريخ
            Timer timeTimer = new Timer();
            timeTimer.Interval = 1000;
            timeTimer.Tick += (s, e) => {
                timeLabel.Text = DateTime.Now.ToString("HH:mm:ss");
                dateLabel.Text = GetArabicDate();
            };
            timeTimer.Start();

            // إضافة تأثير التمرير
            timeCard.MouseEnter += (s, e) => {
                timeCard.BackColor = Color.FromArgb(134, 142, 150);
            };

            timeCard.MouseLeave += (s, e) => {
                timeCard.BackColor = Color.FromArgb(108, 117, 125);
            };
        }

        private string GetArabicDate()
        {
            DateTime now = DateTime.Now;
            string[] arabicDays = { "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت" };
            string[] arabicMonths = { "", "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                                    "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول" };

            string dayName = arabicDays[(int)now.DayOfWeek];
            string monthName = arabicMonths[now.Month];

            return $"🗓️ {dayName}، {now.Day} {monthName} {now.Year}";
        }

        private void SetupLogoutButton()
        {
            try
            {
                // إزالة الزر من أي حاوي سابق
                if (btnLogout.Parent != null)
                {
                    btnLogout.Parent.Controls.Remove(btnLogout);
                }

                // إضافة الزر إلى الشريط العلوي
                btnLogout.Parent = fluentDesignFormControl1;
                btnLogout.Location = new Point(fluentDesignFormControl1.Width - 170, 5);
                btnLogout.Size = new Size(150, 25);
                btnLogout.Appearance.Font = new Font("Droid Arabic Kufi", 10F, FontStyle.Bold);
                btnLogout.BringToFront();

                fluentDesignFormControl1.Controls.Add(btnLogout);
            }
            catch (Exception ex)
            {
                // في حالة فشل إضافة الزر للشريط العلوي، أضفه للحاوي الرئيسي
                btnLogout.Parent = fluentDesignFormContainer1;
                btnLogout.Location = new Point(750, 20);
                btnLogout.Size = new Size(150, 45);
                fluentDesignFormContainer1.Controls.Add(btnLogout);
                btnLogout.BringToFront();
            }
        }

        private void accordionControl1_ElementClick(object sender, DevExpress.XtraBars.Navigation.ElementClickEventArgs e)
        {
            try
            {
                var tag = e.Element.Tag as string;
                if (tag != string.Empty)
                {
                    OPEN_FORM(tag);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }
        public void OPEN_FORM(string FRM_NAME)
        {
            try
            {
                var ins = Assembly.GetExecutingAssembly().GetTypes().FirstOrDefault(x => x.Name == FRM_NAME);
                if (ins == null) return;
                var frm = Activator.CreateInstance(ins) as Form;

                if (Application.OpenForms[frm.Name] != null)
                {
                    frm = Application.OpenForms[frm.Name];
                    frm.BringToFront();
                    if (frm.WindowState == FormWindowState.Minimized)
                    {
                        frm.WindowState = FormWindowState.Maximized;
                    }
                }
                else
                {
                    frm.TopLevel = false;
                    frmMAIN.GETNEWUSER.Dock = DockStyle.Fill;
                    frmMAIN.GETNEWUSER.fluentDesignFormContainer1.Controls.Add(frm);
                    frm.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
                    frm.Dock = DockStyle.Fill;
                    frm.BringToFront();
                    frm.Show();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }
        static void frmUSER_FormClosed(object sender, EventArgs e)
        {
            frmmain = null;
        }
        public static frmMAIN GETNEWUSER
        {
            get
            {
                if (frmmain == null)
                {
                    frmmain = new frmMAIN();
                    frmmain.FormClosed += new FormClosedEventHandler(frmUSER_FormClosed);
                }
                return frmmain;
            }
        }
        
        private void frmMAIN_Load(object sender, EventArgs e)
        {
            try
            {
                // تحميل لوحة المعلومات أولاً
                LoadDashboard();

                // تطبيق صلاحيات المستخدم
                foreach (var element in accordionControl1.GetElements())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(element.Name) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][element.Name]) == 0)
                    {
                        element.Enabled = false;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الشاشة الرئيسية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.RightAlign | MessageBoxOptions.RtlReading);
            }
        }

        private void frmMAIN_FormClosed(object sender, FormClosedEventArgs e)
        {
            try
            {
                frmMAIN.GETNEWUSER.Hide();
                frmMAIN.GETNEWUSER.Parent = null;
                frmlogin.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

     

     
    }
}
