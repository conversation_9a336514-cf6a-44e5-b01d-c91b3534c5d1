﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmMEDCHEK : DevExpress.XtraEditors.XtraForm
    {
        public frmMEDCHEK()
        {
            InitializeComponent();
        }

        Classes.clsMEDCHEK NclsMEDCHEK = new Classes.clsMEDCHEK();

        public void clear_data()
        {
            try
            {
                gridControl1.DataSource = NclsMEDCHEK.MEDCHEK_List();
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["MEDCHEK_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["MEDCHEK_NOTE"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns["MEDCHEK_CODE"].Caption = "الرقم";
                gridView1.Columns["MEDCHEK_NAME"].Caption = "اسم الفحص";
                gridView1.Columns["MEDCHEK_TYPE"].Caption = "نوع الفحص";
                gridView1.Columns["MEDCHEK_PRICE"].Caption = "سعر الفحص";
                gridView1.BestFitColumns();

                txtMEDCHEK_Code.Text = Classes.clsMEDCHEK.MEDCHEK_DATATABLE.maxMEDCHEK_CODE().Rows[0]["MEDCHEK_CODE"].ToString();
                txtMEDCHEK_Name.Text = "";
                txtMEDCHEK_TYPE.Text = "";
                txtMEDCHEK_PRICE.Text = "0";
                txtMEDCHEK_NOTE.Text = "";
                txtMEDCHEK_Name.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }        
        }

        private void frmMEDCHEK_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                clear_data();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

      
        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtMEDCHEK_Code.Text != "" && txtMEDCHEK_Name.Text != "")
                {
                    Classes.clsMEDCHEK.MEDCHEK_DATATABLE.InsertMEDCHEK(Convert.ToInt64(txtMEDCHEK_Code.Text), txtMEDCHEK_Name.Text, txtMEDCHEK_TYPE.Text, Convert.ToDecimal(txtMEDCHEK_PRICE.Text), txtMEDCHEK_NOTE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID));
                    clear_data();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    clear_data();

                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("MEDCHEK_NAME_INDEX"))
                {
                    MessageBox.Show("هذا الفحص موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    clear_data();
                    return;
                }
                MessageBox.Show(ex.Message);
                clear_data();
            }

        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtMEDCHEK_Code.Text != "" && txtMEDCHEK_Name.Text != "")
                {
                    Classes.clsMEDCHEK.MEDCHEK_DATATABLE.UpdateMEDCHEK(Convert.ToInt64(txtMEDCHEK_Code.Text), txtMEDCHEK_Name.Text, txtMEDCHEK_TYPE.Text, Convert.ToDecimal(txtMEDCHEK_PRICE.Text), txtMEDCHEK_NOTE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsMEDCHEK.MEDCHEK_ID, Classes.clsMEDCHEK.MEDCHEK_ID);
                    clear_data();
                }
                else
                {
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("MEDCHEK_NAME_INDEX"))
                {
                    MessageBox.Show("هذا الفحص موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    clear_data();
                    return;
                }
                MessageBox.Show(ex.Message);
                clear_data();
            }
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtMEDCHEK_Code.Text != "" && txtMEDCHEK_Name.Text != "")
                {
                    Classes.clsMEDCHEK.MEDCHEK_DATATABLE.DeleteMEDCHEK(Classes.clsMEDCHEK.MEDCHEK_ID);
                    clear_data();
                }
                else
                {
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            clear_data();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsMEDCHEK.Select_MEDCHEK(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MEDCHEK_NAME"]).ToString());
                    txtMEDCHEK_Code.Text = Classes.clsMEDCHEK.MEDCHEK_ID.ToString();
                    txtMEDCHEK_Name.Text = Classes.clsMEDCHEK.MEDCHEK_NAME.ToString();
                    txtMEDCHEK_TYPE.Text = Classes.clsMEDCHEK.MEDCHEK_TYPE.ToString();
                    txtMEDCHEK_PRICE.Text = Classes.clsMEDCHEK.MEDCHEK_PRICE.ToString();
                    txtMEDCHEK_NOTE.Text = Classes.clsMEDCHEK.MEDCHEK_NOTE.ToString();
                    txtMEDCHEK_Name.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}