﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmMEDCIN : DevExpress.XtraEditors.XtraForm
    {
        public frmMEDCIN()
        {
            InitializeComponent();
        }
        Classes.clsMEDCIN NclsMED = new Classes.clsMEDCIN();


        public void clear_data()
        {
            try
            {
                gridControl1.DataSource = NclsMED.MEDCIN_List();
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["MED_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["MED_S_NAME"]);
                gridView1.Columns["MED_CODE"].Caption = "كود العلاج";
                gridView1.Columns["MED_NAME"].Caption = "اسم العلاج";
                //gridView1.Columns["MED_S_NAME"].Caption = "الاسم العلمي";
                gridView1.Columns["MED_SOURSE"].Caption = "مصدر العلاج";
                gridView1.Columns["MED_PRICE"].Caption = "سعر العلاج";

                txtMEDCode.Text = Classes.clsMEDCIN.MEDCIN_DATATABLE.maxMED_CODE().Rows[0]["MED_CODE"].ToString();
                txtMEDName.Text = "";
                txtMEDSCIENCE.Text = "";
                txtMEDSOURCE.Text = "";
                txtMEDPRICE.Text = "0";
                txtMEDName.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }
        private void frmMEDCIN_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                clear_data();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

       
        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtMEDCode.Text != "" && txtMEDName.Text != "")
                {
                    Classes.clsMEDCIN.MEDCIN_DATATABLE.InsertMEDCIN(Convert.ToInt64(txtMEDCode.Text), txtMEDName.Text, txtMEDSCIENCE.Text, txtMEDSOURCE.Text, Convert.ToDecimal(txtMEDPRICE.Text), Convert.ToInt64(Classes.clsCLINIC.CLI_ID));
                    clear_data();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    clear_data();

                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("MEDICIN_NAME_INDEX"))
                {
                    MessageBox.Show("اسم الدواء موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    clear_data();
                    return;
                }
                MessageBox.Show(ex.Message);
                clear_data();
            }

        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtMEDCode.Text != "" && txtMEDName.Text != "")
                {
                    Classes.clsMEDCIN.MEDCIN_DATATABLE.UpdateMEDCIN(Convert.ToInt64(txtMEDCode.Text), txtMEDName.Text, txtMEDSCIENCE.Text, txtMEDSOURCE.Text, Convert.ToDecimal(txtMEDPRICE.Text), Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsMEDCIN.MED_ID, Classes.clsMEDCIN.MED_ID);
                    clear_data();
                }
                else
                {
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("MEDICIN_NAME_INDEX"))
                {
                    MessageBox.Show("اسم الدواء موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    clear_data();
                    return;
                }
                MessageBox.Show(ex.Message);
                clear_data();
            }

        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtMEDCode.Text != "" && txtMEDName.Text != "")
                {
                    Classes.clsMEDCIN.MEDCIN_DATATABLE.DeleteMEDCIN(Classes.clsMEDCIN.MED_ID);
                    clear_data();
                }
                else
                {
                    clear_data();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            clear_data();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsMED.Select_MEDCIN(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MED_NAME"]).ToString());
                    txtMEDCode.Text = Classes.clsMEDCIN.MED_CODE.ToString();
                    txtMEDName.Text = Classes.clsMEDCIN.MED_NAME.ToString();
                    txtMEDSCIENCE.Text = Classes.clsMEDCIN.MED_S_NAME.ToString();
                    txtMEDSOURCE.Text = Classes.clsMEDCIN.MED_SOURSE.ToString();
                    txtMEDPRICE.Text = Classes.clsMEDCIN.MED_PRICE.ToString();
                    txtMEDName.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}