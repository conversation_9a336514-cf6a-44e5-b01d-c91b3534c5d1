﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmMEDREP : DevExpress.XtraEditors.XtraForm
    {
        public frmMEDREP()
        {
            InitializeComponent();
        }

        Classes.clsMEDREP NclsMEDREP = new Classes.clsMEDREP();
        Classes.clsCUST NclsCUST = new Classes.clsCUST();

        public void Clear_Date()
        {
            try
            {
                cmbCUST_ID.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
                cmbCUST_ID.ValueMember = "CUST_ID";
                cmbCUST_NAME.DataSource = cmbCUST_ID.DataSource;
                cmbCUST_NAME.ValueMember = "CUST_NAME";
                if (Classes.clsCUST.CUST_ID != 0)
                {
                    cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
                }
                gridControl1.DataSource = Classes.clsMEDREP.MEDREP_DATATABLE.MREPbyCUST_ID(Convert.ToInt64(cmbCUST_ID.Text));
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["MREP_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["MREP_TIME"]);
                gridView1.Columns.Remove(gridView1.Columns["MREP_NAME"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["VIS_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["MREP_TEXT"]);
                gridView1.Columns["MREP_CODE"].Caption = "الرقم";
                gridView1.Columns["MREP_DATE"].Caption = "التاريخ";
                gridView1.Columns["MREP_NOTE"].Caption = "حالة المريض";
                gridView1.Columns["CUST_ID"].Caption = "رقم المريض";
                gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
                gridView1.BestFitColumns();

                txtHOL_Code.Text = Classes.clsMEDREP.MEDREP_DATATABLE.maxMREP().Rows[0]["MREP_CODE"].ToString();
                dtpHOL_DATE.Value = DateTime.Now;
                txtHOL_TIME.Text = string.Format(DateTime.Now.ToShortTimeString(), "HH:MI");
                txtHOL_LONG.Text = "";
                txtHOL_NOTE.Text = "";
                txtRESULT.Text = "";

                txtHOL_TEXT.Clear();
                txtHOL_TEXT.AppendText(" راجعني المذكور اعلاه وقد كان يشكو من : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_NOTE.Text + " ");
                txtHOL_TEXT.AppendText(" وقد اعطي العلاج اللازم : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_LONG.Text);
                txtHOL_TEXT.AppendText(" النتيجة : ");
                txtHOL_TEXT.AppendText(" " + txtRESULT.Text);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }          
        }

        private void frmMEDREP_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                Clear_Date();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void txtHOL_NOTE_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                txtHOL_TEXT.Clear();
                txtHOL_TEXT.AppendText(" راجعني المذكور اعلاه وقد كان يشكو من : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_NOTE.Text + " ");
                txtHOL_TEXT.AppendText(" وقد اعطي العلاج اللازم : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_LONG.Text);
                txtHOL_TEXT.AppendText(" النتيجة : ");
                txtHOL_TEXT.AppendText(" " + txtRESULT.Text);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void txtHOL_LONG_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                txtHOL_TEXT.Clear();
                txtHOL_TEXT.AppendText(" راجعني المذكور اعلاه وقد كان يشكو من : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_NOTE.Text + " ");
                txtHOL_TEXT.AppendText(" وقد اعطي العلاج اللازم : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_LONG.Text);
                txtHOL_TEXT.AppendText(" النتيجة : ");
                txtHOL_TEXT.AppendText(" " + txtRESULT.Text);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void txtRESULT_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                txtHOL_TEXT.Clear();
                txtHOL_TEXT.AppendText(" راجعني المذكور اعلاه وقد كان يشكو من : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_NOTE.Text + " ");
                txtHOL_TEXT.AppendText(" وقد اعطي العلاج اللازم : ");
                txtHOL_TEXT.AppendText(" " + txtHOL_LONG.Text);
                txtHOL_TEXT.AppendText(" النتيجة : ");
                txtHOL_TEXT.AppendText(" " + txtRESULT.Text);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void lblCUST_LIST_Click(object sender, EventArgs e)
        {
            try
            {
                LIST_FORM.frmCUST_LIST frmCUST_LIST = new LIST_FORM.frmCUST_LIST();
                frmCUST_LIST.ShowDialog();
                cmbCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtHOL_Code.Text != "" && txtHOL_NOTE.Text != "" && cmbCUST_ID.Text != "" && txtHOL_TEXT.Text != "")
                {
                    Classes.clsMEDREP.MEDREP_DATATABLE.InsertMREP(Convert.ToInt64(txtHOL_Code.Text), Convert.ToDateTime(string.Format(dtpHOL_DATE.Value.ToString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtHOL_TIME.Text.ToString(), "HH:MI")), txtHOL_LONG.Text, txtHOL_TEXT.Text, txtHOL_NOTE.Text, Convert.ToInt64(cmbCUST_ID.Text), Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsVISIT.VIS_ID);
                    var result = MessageBox.Show("هل تريد طباعة الموعد" + "\n" + "طباعة Yes الغاء No", "طباعة الموعد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (result == DialogResult.Yes)
                    {
                        if (cmbCUST_ID.Text != "")
                        {
                            frmMREP_VIS_REPORT mrepprint = new frmMREP_VIS_REPORT();
                            frmMREP_VIS_REPORT.MREP_CODE = Convert.ToInt64(txtHOL_Code.Text);
                            frmMREP_VIS_REPORT.CUST_ID = Convert.ToInt64(cmbCUST_ID.Text);
                            frmMREP_VIS_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                            DateTime MREP_DATE = Convert.ToDateTime(string.Format(dtpHOL_DATE.Value.ToString()));
                            frmMREP_VIS_REPORT.MREP_DATE = MREP_DATE.ToString("MM/dd/yyyy");
                            mrepprint.ShowDialog();
                        }
                        Clear_Date();
                    }
                    else
                    {
                        Clear_Date();
                    }
                    Clear_Date();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                Clear_Date();
            }

        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    Classes.clsMEDREP.MEDREP_DATATABLE.DeleteMREP(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MREP_CODE"]).ToString()), Convert.ToInt64(cmbCUST_ID.Text));
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void cmbCUST_ID_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (cmbCUST_ID.Text != "" && cmbCUST_NAME.Text != "")
                {
                    gridControl1.DataSource = Classes.clsMEDREP.MEDREP_DATATABLE.MREPbyCUST_ID(Convert.ToInt64(cmbCUST_ID.Text));
                    gridView1.Columns.Remove(gridView1.Columns["MREP_ID"]);
                    gridView1.Columns.Remove(gridView1.Columns["MREP_TIME"]);
                    gridView1.Columns.Remove(gridView1.Columns["MREP_NAME"]);
                    gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                    gridView1.Columns.Remove(gridView1.Columns["VIS_ID"]);
                    gridView1.Columns.Remove(gridView1.Columns["MREP_TEXT"]);
                    gridView1.Columns["MREP_CODE"].Caption = "الرقم";
                    gridView1.Columns["MREP_DATE"].Caption = "التاريخ";
                    gridView1.Columns["MREP_NOTE"].Caption = "حالة المريض";
                    gridView1.Columns["CUST_ID"].Caption = "رقم المريض";
                    gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
                    gridView1.BestFitColumns();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnPRINT_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbCUST_ID.Text != "")
                {
                    frmMREP_VIS_REPORT mrepprint = new frmMREP_VIS_REPORT();
                    frmMREP_VIS_REPORT.MREP_CODE = Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MREP_CODE"]));
                    frmMREP_VIS_REPORT.CUST_ID = Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CUST_ID"]));
                    frmMREP_VIS_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                    DateTime MREP_DATE = Convert.ToDateTime(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["MREP_DATE"]).ToString());
                    frmMREP_VIS_REPORT.MREP_DATE = MREP_DATE.ToString("MM/dd/yyyy");
                    mrepprint.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }
    }
}