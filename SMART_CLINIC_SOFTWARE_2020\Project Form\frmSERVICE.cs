﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmSERVICE : DevExpress.XtraEditors.XtraForm
    {
        public frmSERVICE()
        {
            InitializeComponent();
        }
        Classes.clsSERVICE NclsSER = new Classes.clsSERVICE();

        public void Clear_Date()
        {
            try
            {
                gridControl1.DataSource = NclsSER.SERVICE_List();
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["SER_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["SER_NOTE"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns["SER_CODE"].Caption = "كود الاجراء";
                gridView1.Columns["SER_NAME"].Caption = "اسم الاجراء";
                gridView1.Columns["SER_TYPE"].Caption = "نوع الاجراء";
                gridView1.Columns["SER_PRICE"].Caption = "سعر الاجراء";
                gridView1.BestFitColumns();

                txtSERCode.Text = Classes.clsSERVICE.SERVICE_DATATABLE.maxSER_CODE().Rows[0]["SER_CODE"].ToString();
                txtSERName.Text = "";
                txtSERTYPE.Text = "";
                txtSERPRICE.Text = "0";
                txtSERNOTE.Text = "";
                txtSERName.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void frmSERVICE_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                Clear_Date();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

       

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtSERCode.Text != "" && txtSERName.Text != "")
                {
                    Classes.clsSERVICE.SERVICE_DATATABLE.InsertSERVICE(Convert.ToInt64(txtSERCode.Text), txtSERName.Text, txtSERTYPE.Text, txtSERPRICE.Text, txtSERNOTE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID));
                    Clear_Date();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();

                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtSERCode.Text != "" && txtSERName.Text != "")
                {
                    Classes.clsSERVICE.SERVICE_DATATABLE.UpdateSERVICE(Convert.ToInt64(txtSERCode.Text), txtSERName.Text, txtSERTYPE.Text, txtSERPRICE.Text, txtSERNOTE.Text, Convert.ToInt64(Classes.clsCLINIC.CLI_ID), Classes.clsSERVICE.SER_ID, Classes.clsSERVICE.SER_ID);
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("SER_NAME_INDEX"))
                {
                    MessageBox.Show("اسم الاجراء موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }

        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtSERCode.Text != "" && txtSERName.Text != "")
                {
                    Classes.clsSERVICE.SERVICE_DATATABLE.DeleteSERVICE(Classes.clsSERVICE.SER_ID);
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("SER_NAME_INDEX"))
                {
                    MessageBox.Show("اسم الاجراء موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }

        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsSER.Select_SERVICE(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["SER_NAME"]).ToString());
                    txtSERCode.Text = Classes.clsSERVICE.SER_CODE.ToString();
                    txtSERName.Text = Classes.clsSERVICE.SER_NAME.ToString();
                    txtSERTYPE.Text = Classes.clsSERVICE.SER_TYPE.ToString();
                    txtSERPRICE.Text = Classes.clsSERVICE.SER_PRICE.ToString();
                    txtSERNOTE.Text = Classes.clsSERVICE.SER_NOTE.ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}