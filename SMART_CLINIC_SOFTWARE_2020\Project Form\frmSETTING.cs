﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;
using System.Drawing.Printing;
using System.Data.SqlClient;
using System.Net.NetworkInformation;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmSETTING : DevExpress.XtraEditors.XtraForm
    {
        public frmSETTING()
        {
            InitializeComponent();
        }
        Classes.clsCLINIC_TITLE NclsCLI_T = new Classes.clsCLINIC_TITLE();
        string path = Path.GetDirectoryName(Application.ExecutablePath);
        string sourcePath = "";
        string targetPath = "";

        string CONNSTRING = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE;
        SqlConnection conn;

        public void PRINT_SETTING()
        {
            try
            {
                NclsCLI_T.CLI_T_List();
                txtCLI_NAME.Text = Classes.clsCLINIC_TITLE.CLI_T_NAME;
                txtCLI_TITLE.Text = Classes.clsCLINIC_TITLE.CLI_T_TITLE;
                txtCLI_ADDRESS.Text = Classes.clsCLINIC_TITLE.CLI_T_ADDRESS;
                txtCLI_MOBILE.Text = Classes.clsCLINIC_TITLE.CLI_T_MOBILE;
                txtCLI_NOTE.Text = Classes.clsCLINIC_TITLE.CLI_T_NOTE;
                targetPath = path + "\\logo.jpg";
                if (Image.FromFile(path + "\\logo1.jpg") != null)
                {
                    pictureBox1.Image = Image.FromFile(path + "\\logo1.jpg");
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void PRINTERCOMBO()
        {
            try
            {
                foreach (string printname in System.Drawing.Printing.PrinterSettings.InstalledPrinters)
                {
                    cmbPRINTERS.Items.Add(printname);
                }
                cmbPRINTERS.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SYS_PRINTER_NAME;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }
        private void frmSETTING_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in this.Controls.OfType<DevExpress.XtraEditors.GroupControl>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }

                PRINT_SETTING();
                PRINTERCOMBO();
                txtCONNECTION_STRING.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE;
                txtSER_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME;
                txtUSER_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME;
                txtUser_Password.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD;
                cmbDATABASE_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DATABASE_NAME;
                txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";
                if (DataCONFIG() == true)
                {
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default["ClinicDataBase_2020ConnectionString"] = txtCONNECTION_STRING.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME = txtSER_NAME.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME = txtUSER_NAME.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD = txtUser_Password.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE = txtCONNECTION_STRING.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DATABASE_NAME = cmbDATABASE_NAME.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                    lblConn_State.Text = "متصل";
                    lblConn_State.BackColor = Color.Green;
                }
                else
                {
                    txtCONNECTION_STRING.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE;
                    txtSER_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME;
                    txtUSER_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME;
                    txtUser_Password.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD;
                    cmbDATABASE_NAME.Text = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DATABASE_NAME;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                    lblConn_State.Text = "غير متصل";
                    lblConn_State.BackColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            try
            {
                txtCLI_NAME.Text = "";
                txtCLI_TITLE.Text = "";
                txtCLI_ADDRESS.Text = "";
                txtCLI_MOBILE.Text = "";
                txtCLI_NOTE.Text = "";
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnOPEN_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog open = new OpenFileDialog();
                open.Filter = "Image Files(*.jpg; *.jpeg;)|*.jpg; *.jpeg;";
                if (open.ShowDialog() == DialogResult.OK)
                {
                    pictureBox1.Image = new Bitmap(open.FileName);
                    sourcePath = open.FileName;

                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (sourcePath != "")
                {
                    System.IO.File.Delete(targetPath);
                    System.IO.File.Copy(sourcePath, targetPath);
                }
                Classes.clsCLINIC_TITLE.CLINIC_T_DATATABLE.UpdateCLINIC_TITLE(txtCLI_NAME.Text, txtCLI_TITLE.Text, txtCLI_ADDRESS.Text, txtCLI_MOBILE.Text, txtCLI_NOTE.Text);
                MessageBox.Show("تم تعديل البيانات بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }           
        }

        private void btnSAVEPRINTER_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbPRINTERS.Text != "")
                {
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SYS_PRINTER_NAME = cmbPRINTERS.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                    MessageBox.Show("تم تعديل البيانات بنجاح");
                }
                else
                {
                    MessageBox.Show("يرجى اختيار اسم الطابعة من القائمة", " !! تنبية", MessageBoxButtons.OK, MessageBoxIcon.Hand);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void txtSER_NAME_TextChanged(object sender, EventArgs e)
        {
            txtCONNECTION_STRING.Clear();
            txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";
        }

        private void cmbDATABASE_NAME_TextChanged(object sender, EventArgs e)
        {
            txtCONNECTION_STRING.Clear();
            txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";
        }

        private void txtUSER_NAME_TextChanged(object sender, EventArgs e)
        {
            txtCONNECTION_STRING.Clear();
            txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";
        }

        private void txtUser_Password_TextChanged(object sender, EventArgs e)
        {
            txtCONNECTION_STRING.Clear();
            txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";
        }

        public DataTable DataBase_Name()
        {
            DataTable dt = new DataTable();
            try
            {
                conn = new SqlConnection(CONNSTRING);
                SqlDataAdapter adp = new SqlDataAdapter("SELECT [NAME] FROM master.dbo.sysdatabases WHERE dbid > 4", conn);
                adp.Fill(dt);

                return dt;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return dt;
            }
            
        }

        private void btnDATABASE_FILL_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtSER_NAME.Text != "")
                {
                    try
                    {
                        DataBase_Name();
                        cmbDATABASE_NAME.DataSource = DataBase_Name();
                        cmbDATABASE_NAME.DisplayMember = "name";
                    }
                    catch (Exception ex)
                    {
                        cmbDATABASE_NAME.DataSource = null;
                        cmbDATABASE_NAME.Text = "";
                        MessageBox.Show("لا يوجد قاعدة بيانات على هذا السيرفر " + " / " + ex, "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        public Boolean DataCONFIG()
        {
            Boolean ConnSTATE = false;
            try
            {
                
                string hostname = txtSER_NAME.Text;
                //int timeout = 10000;
                Ping ping = new Ping();
                if (hostname != "" && hostname != null)
                {
                    //PingReply pingreply = ping.Send(hostname, timeout);
                    //if (pingreply.Status == IPStatus.Success)
                    //{
                    if (txtCONNECTION_STRING.Text != "" && txtSER_NAME.Text != "" && txtUSER_NAME.Text != "" && txtUser_Password.Text != "")
                    {
                        try
                        {
                            conn = new SqlConnection(CONNSTRING);
                            conn.Open();
                            conn.Close();
                            ConnSTATE = true;
                            DataBase_Name();
                            //cmbDATABASE_NAME.DataSource = DataBase_Name();
                            //cmbDATABASE_NAME.DisplayMember = "name";

                        }
                        catch (Exception ex)
                        {
                            cmbDATABASE_NAME.DataSource = null;
                            cmbDATABASE_NAME.Text = "";
                            ConnSTATE = false;
                            MessageBox.Show("النظام غير متصل بقاعدة البيانات يرجى ادخال الاعدادات" + " / " + ex, "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return ConnSTATE;
                        }
                    }
                    //}
                    else
                    {
                        MessageBox.Show("الاتصال بالسيرفر غير متاح", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        ConnSTATE = false;
                        return ConnSTATE;

                    }
                }
                else
                {
                    ConnSTATE = false;
                    return ConnSTATE;
                }
                return ConnSTATE;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return ConnSTATE;
            }
            
        }

        private void btnCONN_CHECK_Click(object sender, EventArgs e)
        {
            try
            {
                txtCONNECTION_STRING.Text = "Data Source = " + txtSER_NAME.Text + "; Initial Catalog = " + cmbDATABASE_NAME.Text + "; User ID = " + txtUSER_NAME.Text + "; Password = " + txtUser_Password.Text + "";
                CONNSTRING = txtCONNECTION_STRING.Text;
                if (DataCONFIG() == true)
                {
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE = txtCONNECTION_STRING.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME = txtSER_NAME.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME = txtUSER_NAME.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD = txtUser_Password.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DATABASE_NAME = cmbDATABASE_NAME.Text;
                    SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                    lblConn_State.Text = "متصل";
                    lblConn_State.BackColor = Color.Green;
                }
                else
                {
                    lblConn_State.Text = "غير متصل";
                    lblConn_State.BackColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnEditeConn_Click(object sender, EventArgs e)
        {
            try
            {
                SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DEFAULT_DATABASE = txtCONNECTION_STRING.Text;
                SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.SERVER_NAME = txtSER_NAME.Text;
                SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_USER_NAME = txtUSER_NAME.Text;
                SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DB_PASSWORD = txtUser_Password.Text;
                SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.DATABASE_NAME = cmbDATABASE_NAME.Text;
                SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.Save();
                MessageBox.Show("تم تعديل البيانات بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }
    }
}