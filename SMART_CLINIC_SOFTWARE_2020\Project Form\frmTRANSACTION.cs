﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmTRANSACTION : DevExpress.XtraEditors.XtraForm
    {
        public frmTRANSACTION()
        {
            InitializeComponent();
        }
        Classes.clsCUST NclsCUST = new Classes.clsCUST();
        Classes.clsCOMPANY NclsCOMPANY = new Classes.clsCOMPANY();
        Classes.clsCLINIC NclsCLINIC = new Classes.clsCLINIC();
        Classes.clsVISIT NclsVISIT = new Classes.clsVISIT();
        Classes.clsTRANS_VIS_DATA NclsTRANS_DATA = new Classes.clsTRANS_VIS_DATA();
        Classes.clsTRANSACTION NclsTRANSACTION = new Classes.clsTRANSACTION();
        public void Clear_Date()
        {
            try
            {
                gridControl1.DataSource = Classes.clsTRANSACTION.T_DATATABLE.TRANS_LISTbyCLINICorCOMorVISorT_IDorT_NAME(txtSEARCH.Text, txtSEARCH.Text, txtSEARCH.Text, txtSEARCH.Text, txtSEARCH.Text);
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["T_CODE"]);
                gridView1.Columns.Remove(gridView1.Columns["CUST_PRICE"]);
                gridView1.Columns.Remove(gridView1.Columns["COMPANY_PRICE"]);
                gridView1.Columns.Remove(gridView1.Columns["T_NOTE"]);
                gridView1.Columns.Remove(gridView1.Columns["T_STATE"]);
                gridView1.Columns.Remove(gridView1.Columns["COM_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns["T_ID"].Caption = "الرقم";
                gridView1.Columns["T_ID"].VisibleIndex = 0;
                gridView1.Columns["T_NAME"].Caption = "البيان";
                gridView1.Columns["T_NAME"].VisibleIndex = 1;
                gridView1.Columns["T_DATE"].Caption = "التاريخ";
                gridView1.Columns["T_DATE"].VisibleIndex = 2;
                gridView1.Columns["T_TIME"].Caption = "الوقت";
                gridView1.Columns["T_TIME"].VisibleIndex = 3;
                gridView1.Columns["T_TYPE"].Caption = "نوع القيد";
                gridView1.Columns["T_TYPE"].VisibleIndex = 4;
                gridView1.Columns["T_PRICE"].Caption = "القيمة";
                gridView1.Columns["T_PRICE"].VisibleIndex = 5;
                gridView1.Columns["T_DISCOUNT"].Caption = "الخصم";
                gridView1.Columns["T_DISCOUNT"].VisibleIndex = 6;
                gridView1.Columns["T_TOTAL"].Caption = "الواصل";
                gridView1.Columns["T_TOTAL"].VisibleIndex = 7;
                gridView1.Columns["T_UNPAY"].Caption = "الباقي";
                gridView1.Columns["T_UNPAY"].VisibleIndex = 8;
                gridView1.Columns["VIS_ID"].Caption = "رقم الزيارة";
                gridView1.Columns["VIS_ID"].VisibleIndex = 9;
                gridView1.Columns["CLI_NAME"].Caption = "اسم العيادة";
                gridView1.Columns["CLI_NAME"].VisibleIndex = 11;
                gridView1.Columns["COM_NAME"].Caption = "شركة التامين";
                gridView1.Columns["COM_NAME"].VisibleIndex = 12;
                gridView1.BestFitColumns();

                cmbCLI_NAME.DataSource = NclsCLINIC.CLINIC_LIST();
                cmbCLI_NAME.DisplayMember = "CLI_NAME";
                cmbCLI_NAME.ValueMember = "CLI_ID";

                txtT_CODE.Text = Classes.clsTRANSACTION.T_DATATABLE.maxT_CODE().Rows[0]["T_CODE"].ToString();
                dtpT_DATE.Value = DateTime.Now;
                txtT_TIME.Text = DateTime.Now.ToString("HH:MM");
                txtT_TYPE.Text = "";
                txtT_STATE.Text = "";
                txtT_NAME.Text = "";
                txtVIS_ID.Enabled = false;
                lblVIS_LIST.Enabled = false;
                grbSAVE_DATA.Enabled = false;
                txtVIS_ID.Text = "0";
                txtCUST_NAME.Text = "";
                cmbCOM_NAME.Text = "";
                txtCUST_PRICE.Text = "0";
                txtCOM_PRICE.Text = "0";
                txtT_PRICE.Text = "0";
                txtT_DISCOUNT.Text = "0";
                txtT_UNPAY.Text = "0";
                txtCUST_PER.Text = "0";
                txtCOM_PER.Text = "0";
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }          
        }

        private void frmTRANSACTION_Load(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in groupControl1.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                if (Classes.clsTRANSACTION.T_ID != 0)
                {
                    txtT_CODE.Text = Classes.clsTRANSACTION.T_CODE.ToString();
                    dtpT_DATE.Value = Convert.ToDateTime(string.Format(Classes.clsTRANSACTION.T_DATE, "dd/MM/yyyy"));
                    txtT_TIME.Text = string.Format(Classes.clsTRANSACTION.T_TIME, "hh:MI");
                    txtT_STATE.Text = Classes.clsTRANSACTION.T_STATE;
                    txtT_NAME.Text = Classes.clsTRANSACTION.T_NAME;
                    txtT_TYPE.Text = Classes.clsTRANSACTION.T_TYPE;
                    txtT_PRICE.Text = Classes.clsTRANSACTION.T_PRICE.ToString();
                    txtT_DISCOUNT.Text = Classes.clsTRANSACTION.T_DISCOUNT.ToString();
                    txtT_UNPAY.Text = Classes.clsTRANSACTION.T_UNPAY.ToString();
                    txtVIS_ID.Text = Classes.clsTRANSACTION.VIS_ID.ToString();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }            
        }

        private void txtT_TYPE_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtT_TYPE.Text != "" && txtT_TYPE.Text == "كشف علاجي")
                {
                    txtVIS_ID.Enabled = true;
                    lblVIS_LIST.Enabled = true;
                    grbSAVE_DATA.Enabled = true;
                    cmbCOM_NAME.DataSource = NclsCOMPANY.COMPANY_LIST();
                    cmbCOM_NAME.DisplayMember = "COM_NAME";
                    cmbCOM_NAME.ValueMember = "COM_ID";
                    cmbCOM_NAME.Text = "";
                }
                else
                {
                    txtVIS_ID.Enabled = false;
                    lblVIS_LIST.Enabled = false;
                    grbSAVE_DATA.Enabled = false;
                    txtVIS_ID.Text = "0";
                    txtCUST_NAME.Text = "";
                    cmbCOM_NAME.Text = "";
                    txtCUST_PRICE.Text = "0";
                    txtCOM_PRICE.Text = "0";
                    txtCUST_PER.Text = "0";
                    txtCOM_PER.Text = "0";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }          
        }

        private void txtVIS_ID_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (NclsTRANS_DATA.TRANS_VIS_DATA(Convert.ToInt64(txtVIS_ID.Text)).Rows.Count > 0 && Classes.clsTRANS_VIS_DATA.VIS_ID != 0 && txtVIS_ID.Text != "" && txtVIS_ID.Text != "0")
                {
                    txtVIS_ID.Text = Classes.clsTRANS_VIS_DATA.VIS_ID.ToString();
                    txtCUST_NAME.Text = Classes.clsTRANS_VIS_DATA.CUST_NAME;
                    cmbCOM_NAME.Text = Classes.clsTRANS_VIS_DATA.COM_NAME;
                    txtCUST_PER.Text = (Classes.clsTRANS_VIS_DATA.CARD_PER).ToString();
                    txtCOM_PER.Text = (100 - Classes.clsTRANS_VIS_DATA.CARD_PER).ToString();
                    txtT_PRICE.Text = Classes.clsTRANS_VIS_DATA.VIS_PRICE.ToString();
                    txtT_DISCOUNT.Text = Classes.clsTRANS_VIS_DATA.VIS_DISCOUNT.ToString();
                    txtT_UNPAY.Text = Classes.clsTRANS_VIS_DATA.VIS_UNPAY.ToString();
                    if (Classes.clsTRANS_VIS_DATA.CARD_PER == 0)
                    {
                        txtCUST_PER.Text = "0";
                        txtCOM_PER.Text = "0";
                    }
                    else
                    {
                        txtCUST_PER.Text = (100 - Classes.clsTRANS_VIS_DATA.CARD_PER).ToString();
                        txtCOM_PER.Text = Classes.clsTRANS_VIS_DATA.CARD_PER.ToString();
                    }

                    if (txtT_TOTAL.Text != "" && Classes.clsTRANS_VIS_DATA.CARD_PER != 0)
                    {
                        txtCUST_PRICE.Text = (Convert.ToDecimal(txtT_TOTAL.Text) * (Convert.ToDecimal(txtCUST_PER.Text) / 100)).ToString(".00");
                        txtCOM_PRICE.Text = (Convert.ToDecimal(txtT_TOTAL.Text) * (Convert.ToDecimal(txtCOM_PER.Text) / 100)).ToString(".00");
                    }
                    else
                    {
                        txtCUST_PRICE.Text = "0";
                        txtCOM_PRICE.Text = "0";
                    }


                }
                else
                {
                    txtVIS_ID.Text = "0";
                    txtCUST_NAME.Text = "";
                    cmbCOM_NAME.Text = "";
                    txtCUST_PRICE.Text = "0";
                    txtCOM_PRICE.Text = "0";
                    txtCUST_PER.Text = "0";
                    txtCOM_PER.Text = "0";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }          
        }

        private void txtT_PRICE_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtT_PRICE.Text != "" && txtT_DISCOUNT.Text != "")
                {
                    txtT_TOTAL.Text = (Convert.ToDecimal(txtT_PRICE.Text) - Convert.ToDecimal(txtT_DISCOUNT.Text)).ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void txtT_DISCOUNT_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtT_PRICE.Text != "" && txtT_DISCOUNT.Text != "")
                {
                    txtT_TOTAL.Text = (Convert.ToDecimal(txtT_PRICE.Text) - Convert.ToDecimal(txtT_DISCOUNT.Text)).ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void txtCUST_PER_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtCUST_PER.Text != "" && txtCOM_PER.Text != "" && txtT_TOTAL.Text != "" && txtT_TOTAL.Text != "0" && txtT_TYPE.Text != "" && txtT_TYPE.Text == "كشف علاجي")
                {
                    txtT_TOTAL.Text = (Convert.ToDecimal(txtT_PRICE.Text) - Convert.ToDecimal(txtT_DISCOUNT.Text)).ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void txtCOM_PER_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtCUST_PER.Text != "" && txtCOM_PER.Text != "" && txtT_TOTAL.Text != "" && txtT_TOTAL.Text != "0" && txtT_TYPE.Text != "" && txtT_TYPE.Text == "كشف علاجي")
                {
                    txtT_TOTAL.Text = (Convert.ToDecimal(txtT_PRICE.Text) - Convert.ToDecimal(txtT_DISCOUNT.Text)).ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtT_CODE.Text != "" && txtT_TOTAL.Text != "" && cmbCLI_NAME.Text != "" && txtT_TYPE.Text != "")
                {
                    Classes.clsTRANSACTION.T_DATATABLE.InsertTRANSACTION(Convert.ToInt64(txtT_CODE.Text), txtT_NAME.Text != "" ? txtT_NAME.Text : txtT_TYPE.Text, Convert.ToDateTime(string.Format(dtpT_DATE.Value.ToShortDateString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtT_TIME.Text.ToString(), "HH:MI")), txtT_TYPE.Text, Convert.ToDecimal(txtT_PRICE.Text), Convert.ToDecimal(txtT_DISCOUNT.Text), Convert.ToDecimal(txtT_UNPAY.Text), Convert.ToDecimal(txtT_TOTAL.Text), Convert.ToDecimal(txtCUST_PRICE.Text), Convert.ToDecimal(txtCOM_PRICE.Text), txtT_NOTE.Text, txtT_STATE.Text != "" ? txtT_STATE.Text : "فعالة", txtVIS_ID.Text != "0" ? Convert.ToInt64(txtVIS_ID.Text) : 0, Convert.ToInt64(cmbCLI_NAME.SelectedValue), cmbCOM_NAME.Text != "" ? Classes.clsTRANS_VIS_DATA.COM_ID : 0, Convert.ToInt64(txtT_CODE.Text), Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID));
                    if (txtT_TYPE.Text != "كشف علاجي")
                    {
                        frmTRANSACTION_REPORT TRANS_REP = new frmTRANSACTION_REPORT();
                        frmTRANSACTION_REPORT.T_CODE = Convert.ToInt64(txtT_CODE.Text);
                        frmTRANSACTION_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                        TRANS_REP.ShowDialog();
                    }
                    else if(txtT_TYPE.Text == "كشف علاجي")
                    {
                        frmVIS_SER_PRICE_REPORT vis_ser_price = new frmVIS_SER_PRICE_REPORT();
                        frmVIS_SER_PRICE_REPORT.VIS_ID = Convert.ToInt64(txtVIS_ID.Text);
                        frmVIS_SER_PRICE_REPORT.CUST_ID = Convert.ToInt64(Classes.clsTRANS_VIS_DATA.CUST_ID);
                        frmVIS_SER_PRICE_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                        vis_ser_price.ShowDialog();
                    } 
                    Clear_Date();
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("TRANS_VIS_INDEX"))
                {
                    MessageBox.Show("لا يمكن تكرار الزيارة", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
        }


        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void txtT_TYPE_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtT_TYPE.Text != "" && txtT_TYPE.Text == "كشف علاجي")
                {
                    txtVIS_ID.Enabled = true;
                    lblVIS_LIST.Enabled = true;
                    grbSAVE_DATA.Enabled = true;
                    cmbCOM_NAME.DataSource = NclsCOMPANY.COMPANY_LIST();
                    cmbCOM_NAME.DisplayMember = "COM_NAME";
                    cmbCOM_NAME.ValueMember = "COM_ID";
                    txtCUST_NAME.DataSource = Classes.clsCUST.CUST_DATA_LIST.GetData("", "");
                    txtCUST_NAME.ValueMember = "CUST_NAME";
                    cmbCOM_NAME.Text = "";
                    txtCUST_NAME.Text = "";
                }
                else
                {
                    txtVIS_ID.Enabled = false;
                    lblVIS_LIST.Enabled = false;
                    grbSAVE_DATA.Enabled = false;
                    txtVIS_ID.Text = "0";
                    txtCUST_NAME.Text = "";
                    cmbCOM_NAME.Text = "";
                    txtCUST_PRICE.Text = "0";
                    txtCOM_PRICE.Text = "0";
                    txtT_PRICE.Text = "0";
                    txtT_DISCOUNT.Text = "0";
                    txtT_UNPAY.Text = "0";
                    txtCUST_PER.Text = "0";
                    txtCOM_PER.Text = "0";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }          
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtT_CODE.Text != "" && txtT_TOTAL.Text != "" && cmbCLI_NAME.Text != "" && txtT_TYPE.Text != "")
                {
                    Classes.clsTRANSACTION.T_DATATABLE.UpdateTRANSACTION(Convert.ToInt64(txtT_CODE.Text), txtT_NAME.Text, Convert.ToDateTime(string.Format(dtpT_DATE.Value.ToShortDateString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtT_TIME.Text.ToString(), "HH:MI")), txtT_TYPE.Text, Convert.ToDecimal(txtT_PRICE.Text), Convert.ToDecimal(txtT_DISCOUNT.Text), Convert.ToDecimal(txtT_UNPAY.Text), Convert.ToDecimal(txtT_TOTAL.Text), Convert.ToDecimal(txtCUST_PRICE.Text), Convert.ToDecimal(txtCOM_PRICE.Text), txtT_NOTE.Text, txtT_STATE.Text, txtVIS_ID.Text != "0" ? Convert.ToInt64(txtVIS_ID.Text) : 0, Convert.ToInt64(cmbCLI_NAME.SelectedValue), cmbCOM_NAME.Text != "" ? Classes.clsTRANS_VIS_DATA.COM_ID : 0, Classes.clsTRANSACTION.T_ID, Classes.clsTRANSACTION.CLI_ID, Classes.clsTRANSACTION.T_ID);
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("TRANS_VIS_INDEX"))
                {
                    MessageBox.Show("لا يمكن تكرار الزيارة", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0 && txtT_CODE.Text != "" && txtT_TOTAL.Text != "" && cmbCLI_NAME.Text != "" && txtT_TYPE.Text != "")
                {
                    Classes.clsTRANSACTION.T_DATATABLE.DeleteTRANSACTION(Classes.clsTRANSACTION.T_ID, Classes.clsTRANSACTION.CLI_ID);
                    Clear_Date();
                }
                else
                {
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void txtSEARCH_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                gridControl1.DataSource = Classes.clsTRANSACTION.T_DATATABLE.TRANS_LISTbyCLINICorCOMorVISorT_IDorT_NAME(txtSEARCH.Text, txtSEARCH.Text, txtSEARCH.Text, txtSEARCH.Text, txtSEARCH.Text);
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["T_CODE"]);
                gridView1.Columns.Remove(gridView1.Columns["CUST_PRICE"]);
                gridView1.Columns.Remove(gridView1.Columns["COMPANY_PRICE"]);
                gridView1.Columns.Remove(gridView1.Columns["T_NOTE"]);
                gridView1.Columns.Remove(gridView1.Columns["T_STATE"]);
                gridView1.Columns.Remove(gridView1.Columns["COM_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns["T_ID"].Caption = "الرقم";
                gridView1.Columns["T_NAME"].Caption = "البيان";
                gridView1.Columns["T_DATE"].Caption = "التاريخ";
                gridView1.Columns["T_TIME"].Caption = "الوقت";
                gridView1.Columns["T_TYPE"].Caption = "نوع القيد";
                gridView1.Columns["T_PRICE"].Caption = "القيمة";
                gridView1.Columns["T_DISCOUNT"].Caption = "الخصم";
                gridView1.Columns["T_UNPAY"].Caption = "الباقي";
                gridView1.Columns["T_TOTAL"].Caption = "المجموع";
                gridView1.Columns["VIS_ID"].Caption = "رقم الزيارة";
                gridView1.Columns["CLI_NAME"].Caption = "اسم العيادة";
                gridView1.Columns["COM_NAME"].Caption = "شركة التامين";
                gridView1.BestFitColumns();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            txtSEARCH.Text = "";
            txtSEARCH.Focus();
        }

        private void btnPRINT_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtT_TYPE.Text != "كشف علاجي")
                {
                    frmTRANSACTION_REPORT TRANS_REP = new frmTRANSACTION_REPORT();
                    frmTRANSACTION_REPORT.T_CODE = Convert.ToInt64(txtT_CODE.Text);
                    frmTRANSACTION_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                    TRANS_REP.ShowDialog();
                }
                else if (txtT_TYPE.Text == "كشف علاجي" && txtT_CODE.Text != "" && txtT_TYPE.Text != "" && txtT_PRICE.Text != "0")
                {
                    frmVIS_SER_PRICE_REPORT vis_ser_price = new frmVIS_SER_PRICE_REPORT();
                    frmVIS_SER_PRICE_REPORT.VIS_ID = Convert.ToInt64(txtVIS_ID.Text);
                    frmVIS_SER_PRICE_REPORT.CUST_ID = Convert.ToInt64(Classes.clsTRANS_VIS_DATA.CUST_ID);
                    frmVIS_SER_PRICE_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                    vis_ser_price.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnPRINTGV_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    gridControl1.ShowRibbonPrintPreview();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsTRANSACTION.Select_TRANS(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["T_NAME"]).ToString(), Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["T_ID"]).ToString()));
                    if (Classes.clsTRANSACTION.T_ID != 0)
                    {
                        txtT_CODE.Text = Classes.clsTRANSACTION.T_CODE.ToString();
                        dtpT_DATE.Value = Convert.ToDateTime(string.Format(Classes.clsTRANSACTION.T_DATE, "dd/MM/yyyy"));
                        txtT_TIME.Text = string.Format(Classes.clsTRANSACTION.T_TIME, "hh:MI");
                        txtT_STATE.Text = Classes.clsTRANSACTION.T_STATE;
                        txtT_NAME.Text = Classes.clsTRANSACTION.T_NAME;
                        txtT_TYPE.Text = Classes.clsTRANSACTION.T_TYPE;
                        txtT_PRICE.Text = Classes.clsTRANSACTION.T_PRICE.ToString();
                        txtT_DISCOUNT.Text = Classes.clsTRANSACTION.T_DISCOUNT.ToString();
                        txtT_UNPAY.Text = Classes.clsTRANSACTION.T_UNPAY.ToString();
                        txtCUST_PRICE.Text = Classes.clsTRANSACTION.CUST_PRICE.ToString();
                        txtCOM_PRICE.Text = Classes.clsTRANSACTION.COMPANY_PRICE.ToString();
                        txtVIS_ID.Text = Classes.clsTRANSACTION.VIS_ID.ToString();
                    }
                    else
                    {
                        Clear_Date();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

    }
}