﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmVISIT : DevExpress.XtraEditors.XtraForm
    {
        public frmVISIT()
        {
            InitializeComponent();
        }
        Classes.clsSTOCK_DATA NclsSTOCK = new Classes.clsSTOCK_DATA();
        DataTable dt_STOCK = new DataTable();

        Classes.clsCUST NclsCUST = new Classes.clsCUST();
        Classes.clsORDER NclsORDER = new Classes.clsORDER();
        Classes.clsCLINIC NclsCLINIC = new Classes.clsCLINIC();
        Classes.clsVISIT NclsVISIT = new Classes.clsVISIT();
        DataTable dt_CUST_CARD = new DataTable();
        public void ORDER_LIST()
        {
            try
            {
                gridControl1.DataSource = NclsORDER.ORDER_LIST();
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["ORDER_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["ORDER_NOTE"]);
                gridView1.Columns["ORDER_CODE"].Caption = "رقم الدور";
                gridView1.Columns["ORDER_DATE"].Caption = "التاريخ";
                gridView1.Columns["ORDER_TIME"].Caption = "الوقت";
                gridView1.Columns["CUST_ID"].Caption = "رقم المريض";
                gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
                gridView1.BestFitColumns();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }        
        }

        public void Clear_CUST_Date()
        {
            try
            {
                gridControl1.DataSource = NclsORDER.ORDER_LIST();
                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
                gridView1.Columns.Remove(gridView1.Columns["ORDER_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
                gridView1.Columns.Remove(gridView1.Columns["ORDER_NOTE"]);
                gridView1.Columns["ORDER_CODE"].Caption = "رقم الدور";
                gridView1.Columns["ORDER_DATE"].Caption = "التاريخ";
                gridView1.Columns["ORDER_TIME"].Caption = "الوقت";
                gridView1.Columns["CUST_ID"].Caption = "رقم المريض";
                gridView1.Columns["CUST_NAME"].Caption = "اسم المريض";
                gridView1.BestFitColumns();

                txtCUST_ID.Text = "";
                txtCUST_F_Name.Text = "";
                txtCUST_S_Name.Text = "";
                txtCUST_T_Name.Text = "";
                txtCUST_L_Name.Text = "";
                txtCUST_AGE.Text = "";
                txtMONTH.Text = "";
                txtUNPAY.Text = "";
                txtGENDER.Text = "";

                dtpCUST_DATE.Value = DateTime.Now;
                txtAPO_TIME.Text = string.Format(DateTime.Now.ToShortTimeString(), "hh:MM");

                cmbCurrentMoney.DataSource = NclsSTOCK.STOCK_List();
                cmbCurrentMoney.DisplayMember = "Money";

                txtCUST_NOTE.Text = "";
                cmbCLI_ID.DataSource = NclsCLINIC.CLINIC_LIST();
                cmbCLI_ID.DisplayMember = "CLI_ID";
                cmbCLI_NAME.DataSource = cmbCLI_ID.DataSource;
                cmbCLI_NAME.DisplayMember = "CLI_NAME";
                txtDOC_NOTE.Text = "";
                txtCOST.Text = "0";
                txtDISCOUNT.Text = "0";
                txtTOTAL.Text = "0";
                txtCOM_NAME.Text = "";
                txtCARD_ID.Text = "";
                txtVISIT_ID.Text = Classes.clsVISIT.VISIT_DATATABLE.maxVIS_IDandVIS_CODE().Rows[0]["VIS_ID"].ToString();
                Classes.clsVISIT.VIS_ID = Convert.ToInt64(txtVISIT_ID.Text);
                txtCUST_F_Name.Focus();

                grbPAY.Enabled = false;
                grbSAVE.Enabled = false;
                grbSERVICES.Enabled = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void frmVISIT_Load(object sender, EventArgs e)
        {
            cmbSTOCK_ID.DataSource = NclsSTOCK.STOCK_List();
            cmbSTOCK_ID.DisplayMember = "Stock_ID";
            cmbSTOCK_NAME.DataSource = cmbSTOCK_ID.DataSource;
            cmbSTOCK_NAME.DisplayMember = "Stock_Name";
            try
            {
                foreach (var item in grbSERVICES.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }

                foreach (var item in grbPAY.Controls.OfType<DevExpress.XtraEditors.TextEdit>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }

                foreach (var item in grbSAVE.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
                Clear_CUST_Date();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }         
        }

        private void txtCUST_ID_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtCUST_ID.Text == "" && txtVISIT_ID.Text == "" && txtCUST_F_Name.Text == "")
                {
                    Clear_CUST_Date();
                }
                else
                {
                    grbPAY.Enabled = true;
                    grbSAVE.Enabled = true;
                    grbSERVICES.Enabled = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnCUST_LIST_Click(object sender, EventArgs e)
        {
            try
            {
                LIST_FORM.frmCUST_LIST frmCUST_LIST = new LIST_FORM.frmCUST_LIST();
                frmCUST_LIST.ShowDialog();
                if (Classes.clsCUST.CUST_ID != 0)
                {
                    txtCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
                    txtCUST_F_Name.Text = Classes.clsCUST.CUST_F_NAME;
                    txtCUST_S_Name.Text = Classes.clsCUST.CUST_S_NAME;
                    txtCUST_T_Name.Text = Classes.clsCUST.CUST_T_NAME;
                    txtCUST_L_Name.Text = Classes.clsCUST.CUST_L_NAME;
                    txtCUST_AGE.Text = Classes.clsCUST.CUST_AGE;
                    txtMONTH.Text = Classes.clsCUST.CUST_AGE_MONTH;
                    txtGENDER.Text = Classes.clsCUST.CUST_GENDER;
                    dtpCUST_DATE.Value = Convert.ToDateTime(string.Format(Classes.clsCUST.CUST_BD, "dd/MM/yyyy"));
                    cmbCLI_ID.Text = Classes.clsCLINIC.CLI_ID.ToString();
                    cmbPAY_TYPE.Text = "كاش";

                    dt_CUST_CARD = NclsCUST.CUST_CARD_DATA(Classes.clsCUST.CUST_ID, Classes.clsCUST.CLI_ID);
                    if (dt_CUST_CARD.Rows.Count > 0)
                    {
                        txtCOM_NAME.Text = dt_CUST_CARD.Rows[0]["COM_NAME"].ToString();
                        txtCARD_ID.Text = dt_CUST_CARD.Rows[0]["CARD_ID"].ToString();
                    }
                    else
                    {
                        txtCOM_NAME.Text = "لا يوجد تامين";
                        txtCARD_ID.Text = "0";
                    }
                }
                else
                {
                    Clear_CUST_Date();
                    MessageBox.Show("لم يتم اختيار المريض", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnCLINIC_LIST_Click(object sender, EventArgs e)
        {
            try
            {
                LIST_FORM.frmCLINIC_LIST frmclinic_LIST = new LIST_FORM.frmCLINIC_LIST();
                frmclinic_LIST.ShowDialog();
                cmbCLI_ID.Text = Classes.clsCLINIC.CLI_ID.ToString();
                cmbCLI_NAME.Text = Classes.clsCLINIC.CLI_NAME;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnCLEAR_CUST_Click(object sender, EventArgs e)
        {
            Clear_CUST_Date();
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    Classes.clsORDER.ORDER_DATATABLE.DeleteORDER(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["ORDER_CODE"]).ToString()));
                    ORDER_LIST();
                }
                else
                {
                    ORDER_LIST();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnUPDATE_Click(object sender, EventArgs e)
        {
            ORDER_LIST();
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtVISIT_ID.Text != "" && txtCUST_ID.Text != "" && txtCUST_F_Name.Text != "" && cmbCLI_ID.Text != "")
                {
                    Classes.clsVISIT.VISIT_DATATABLE.InsertVIS(Convert.ToInt64(txtVISIT_ID.Text), Convert.ToInt64(Classes.clsVISIT.VISIT_DATATABLE.maxVIS_IDandVIS_CODE().Rows[0]["VIS_CODE"]), "كشف طبي", Convert.ToDateTime(string.Format(dtpVIS_DATE.Value.ToShortDateString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtAPO_TIME.Text.ToString(), "HH:MI")), "زيارة عادية", Convert.ToInt64(txtCUST_ID.Text), Convert.ToInt64(cmbCLI_ID.Text), Classes.clsDOCTORS.DOC_ID, Convert.ToDecimal(txtCOST.Text), Convert.ToDecimal(txtDISCOUNT.Text), Convert.ToDecimal(txtTOTAL.Text), txtDOC_NOTE.Text, cmbPAY_TYPE.Text, Convert.ToDecimal(txtUNPAY.Text), Convert.ToInt64(txtVISIT_ID.Text), Convert.ToInt64(cmbCLI_ID.Text));
                    Classes.clsTRANSACTION.T_DATATABLE.InsertTRANSACTION_by_VIS(Convert.ToInt64(Classes.clsTRANSACTION.T_DATATABLE.maxT_CODE().Rows[0]["T_CODE"]), "كشف علاجي", Convert.ToDateTime(string.Format(DateTime.Now.ToShortDateString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtAPO_TIME.Text.ToString(), "HH:MI")), "كشف علاجي", Convert.ToDecimal(txtCOST.Text), Convert.ToDecimal(txtDISCOUNT.Text), Convert.ToDecimal(txtUNPAY.Text), Convert.ToDecimal(txtTOTAL.Text), Convert.ToDecimal(txtCUST_PRICE.Text), Convert.ToDecimal(txtCOM_PRICE.Text), "", "فعالة", Convert.ToInt64(txtVISIT_ID.Text), Convert.ToInt64(cmbCLI_ID.Text), txtCOM_NAME.Text != "لا يوجد تامين" ? Convert.ToInt64(dt_CUST_CARD.Rows[0]["COM_ID"]) : 0, Convert.ToInt64(txtVISIT_ID.Text), Convert.ToInt64(cmbCLI_ID.Text));

                    Classes.clsSTOCK_DATA.STOCK_DATATABLE.UpdateSTOCK(1, "الخزنة الرئيسية", Convert.ToDecimal(txtTOTAL_STOCK.Text), 1);
                    MessageBox.Show("تم تعديل بيانات الخزنة بنجاح");

                    //Clear_CUST_Date();
                    MessageBox.Show("تم حفظ البيانات بشكل صحيح", "! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //DELETE ORDER
                    //try
                    //{
                    //    if (gridView1.RowCount > 0)
                    //    {
                    //        Classes.clsORDER.ORDER_DATATABLE.DeleteORDER(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["ORDER_CODE"]).ToString()));
                    //        ORDER_LIST();
                    //    }
                    //    else
                    //    {
                    //        ORDER_LIST();
                    //    }
                    //}
                    //catch (Exception ex)
                    //{
                    //    MessageBox.Show(ex.Message);
                    //}
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح", "! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    Clear_CUST_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("VIS_DATE_INDEX"))
                {
                    MessageBox.Show("لا يمكن تكرار الزيارة", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_CUST_Date(); 
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_CUST_Date();
            }
        }

        private void btnDIAGNOIS_Click(object sender, EventArgs e)
        {
            try
            {
                MED_FORM.frmDIAG_LIST frmDIG_LIST = new MED_FORM.frmDIAG_LIST();
                frmDIG_LIST.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }          
        }

        private void btnSERVICES_Click(object sender, EventArgs e)
        {
            try
            {
                MED_FORM.frmSER_LIST frmSER_LIST = new MED_FORM.frmSER_LIST();
                frmSER_LIST.ShowDialog();
                txtCOST.Text = Classes.clsSERLIST.SERLIST_PRICE_TOTAL.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnCHECK_Click(object sender, EventArgs e)
        {
            try
            {
                MED_FORM.frmMEDREQ frmMEDREQ_LIST = new MED_FORM.frmMEDREQ();
                frmMEDREQ_LIST.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnMEDCINE_Click(object sender, EventArgs e)
        {
            try
            {
                MED_FORM.frmMED_LIST frmMED_LIST = new MED_FORM.frmMED_LIST();
                frmMED_LIST.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }         
        }

        private void btnHOLIDAY_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("في مرحلة التحديث");
                //Project_Form.frmHOLIDAY frmHOL = new Project_Form.frmHOLIDAY();
                //frmHOL.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnREPORT_Click(object sender, EventArgs e)
        {
            try
            {
                Project_Form.frmMEDREP frmMEDREP = new Project_Form.frmMEDREP();
                frmMEDREP.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnAPO_Click(object sender, EventArgs e)
        {
            try
            {
                Project_Form.frmAPO frmAPO = new Project_Form.frmAPO();
                frmAPO.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnOLDMED_Click(object sender, EventArgs e)
        {
            try
            {
                MED_FORM.frmDES frmDES = new MED_FORM.frmDES();
                frmDES.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void txtCOST_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtCOST.Text != "" && Classes.clsSERLIST.SERLIST_PRICE_TOTAL != 0 && txtCOST.Text != "0" && txtDISCOUNT.Text != "")
                {
                    txtTOTAL.Text = (Convert.ToDecimal(txtCOST.Text) - Convert.ToDecimal(txtDISCOUNT.Text)).ToString();
                    if (txtTOTAL.Text != "0" && txtCOM_NAME.Text != "" && txtCOM_NAME.Text != "لا يوجد تامين" && txtCARD_ID.Text != "" && txtCARD_ID.Text != "0" && dt_CUST_CARD.Rows.Count > 0)
                    {
                        int CUST_PER, COM_PER;
                        CUST_PER = 100 - Convert.ToInt16(dt_CUST_CARD.Rows[0]["CARD_PER"]);
                        COM_PER = Convert.ToInt16(dt_CUST_CARD.Rows[0]["CARD_PER"]);
                        txtCUST_PRICE.Text = (Convert.ToDecimal(txtTOTAL.Text) * (Convert.ToDecimal(CUST_PER) / 100)).ToString(".00");
                        txtCOM_PRICE.Text = (Convert.ToDecimal(txtTOTAL.Text) * (Convert.ToDecimal(COM_PER) / 100)).ToString(".00");
                    }
                    else
                    {
                        txtCUST_PRICE.Text = "0";
                        txtCOM_PRICE.Text = "0";
                    }
                }
                else
                {
                    txtCUST_PRICE.Text = "0";
                    txtCOM_PRICE.Text = "0";
                    txtTOTAL.Text = "0";
                    txtDISCOUNT.Text = "0";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void txtDISCOUNT_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtCOST.Text != "" && Classes.clsSERLIST.SERLIST_PRICE_TOTAL != 0 && txtCOST.Text != "0" && txtDISCOUNT.Text != "")
                {
                    txtTOTAL.Text = (Convert.ToDecimal(txtCOST.Text) - Convert.ToDecimal(txtDISCOUNT.Text)).ToString();
                    if (txtTOTAL.Text != "0" && txtCOM_NAME.Text != "" && txtCOM_NAME.Text != "لا يوجد تامين" && txtCARD_ID.Text != "" && txtCARD_ID.Text != "0" && dt_CUST_CARD.Rows.Count > 0)
                    {
                        int CUST_PER, COM_PER;
                        CUST_PER = 100 - Convert.ToInt16(dt_CUST_CARD.Rows[0]["CARD_PER"]);
                        COM_PER = Convert.ToInt16(dt_CUST_CARD.Rows[0]["CARD_PER"]);
                        txtCUST_PRICE.Text = (Convert.ToDecimal(txtTOTAL.Text) * (Convert.ToDecimal(CUST_PER) / 100)).ToString(".00");
                        txtCOM_PRICE.Text = (Convert.ToDecimal(txtTOTAL.Text) * (Convert.ToDecimal(COM_PER) / 100)).ToString(".00");
                    }
                    else
                    {
                        txtCUST_PRICE.Text = "0";
                        txtCOM_PRICE.Text = "0";
                    }
                }
                else
                {
                    txtCUST_PRICE.Text = "0";
                    txtCOM_PRICE.Text = "0";
                    txtTOTAL.Text = "0";
                    txtDISCOUNT.Text = "0";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnCUST_CARD_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtVISIT_ID.Text != "" && txtCUST_ID.Text != "" && cmbCLI_ID.Text != "")
                {
                    frmCUST_PAST_HISTORY_REPORT frmCUST_FILE = new frmCUST_PAST_HISTORY_REPORT();
                    frmCUST_PAST_HISTORY_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                    frmCUST_PAST_HISTORY_REPORT.CUST_ID = Convert.ToInt64(txtCUST_ID.Text);
                    frmCUST_FILE.ShowDialog();
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnPRINT_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtVISIT_ID.Text != "" && txtCUST_ID.Text != "" && txtCUST_F_Name.Text != "" && cmbCLI_ID.Text != "")
                {
                    frmVIS_SER_PRICE_REPORT vis_ser_price = new frmVIS_SER_PRICE_REPORT();
                    frmVIS_SER_PRICE_REPORT.VIS_ID = Convert.ToInt64(txtVISIT_ID.Text);
                    frmVIS_SER_PRICE_REPORT.CUST_ID = Convert.ToInt64(Classes.clsCUST.CUST_ID);
                    frmVIS_SER_PRICE_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                    vis_ser_price.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsCUST.Select_CUST_ORDER(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CUST_ID"]).ToString(), (gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["CUST_NAME"]).ToString()));
                    txtCUST_ID.Text = Classes.clsCUST.CUST_ID.ToString();
                    txtCUST_F_Name.Text = Classes.clsCUST.CUST_F_NAME;
                    txtCUST_S_Name.Text = Classes.clsCUST.CUST_S_NAME;
                    txtCUST_T_Name.Text = Classes.clsCUST.CUST_T_NAME;
                    txtCUST_L_Name.Text = Classes.clsCUST.CUST_L_NAME;
                    txtCUST_AGE.Text = Classes.clsCUST.CUST_AGE;
                    txtMONTH.Text = Classes.clsCUST.CUST_AGE_MONTH.ToString();
                    txtGENDER.Text = Classes.clsCUST.CUST_GENDER;
                    dtpCUST_DATE.Value = Convert.ToDateTime(string.Format(Classes.clsCUST.CUST_BD, "dd/MM/yyyy"));
                    txtCUST_NOTE.Text = Classes.clsCUST.CUST_NOTE;
                    cmbCLI_ID.Text = Classes.clsCUST.CLI_ID.ToString();
                    cmbPAY_TYPE.Text = "كاش";

                    dt_CUST_CARD = NclsCUST.CUST_CARD_DATA(Classes.clsCUST.CUST_ID, Classes.clsCUST.CLI_ID);
                    if (dt_CUST_CARD.Rows.Count > 0)
                    {
                        txtCOM_NAME.Text = dt_CUST_CARD.Rows[0]["COM_NAME"].ToString();
                        txtCARD_ID.Text = dt_CUST_CARD.Rows[0]["CARD_ID"].ToString();
                    }
                    else
                    {
                        txtCOM_NAME.Text = "لا يوجد تامين";
                        txtCARD_ID.Text = "0";
                    }

                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void btnENDO_Click(object sender, EventArgs e)
        {
            try
            {
                MED_FORM.frm_ENDO frmENDO = new MED_FORM.frm_ENDO();
                frmENDO.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void txtTOTAL_EditValueChanged(object sender, EventArgs e)
        {
            decimal d = 0, d2 = 0;
            
            txtUNPAY.Text = (Convert.ToDecimal(txtCOST.Text) - Convert.ToDecimal(txtTOTAL.Text)).ToString();
            d = Convert.ToDecimal(cmbCurrentMoney.Text);
            d2 = Convert.ToDecimal(txtTOTAL.Text);
            txtTOTAL_STOCK.Text = (d + d2).ToString();

        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_CUST_Date();
           

        }

        private void cmbSTOCK_NAME_SelectedIndexChanged(object sender, EventArgs e)
        {
            dt_STOCK = NclsSTOCK.Select_STOCK_NAME(cmbSTOCK_NAME.Text);
            if (dt_STOCK.Rows.Count > 0)
            {
                cmbCurrentMoney.Text = dt_STOCK.Rows[0]["Money"].ToString();
            }
        }
    }
}