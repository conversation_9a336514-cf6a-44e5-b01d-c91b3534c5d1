﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SMART_CLINIC_SOFTWARE_2020.LIST_FORM;
using SMART_CLINIC_SOFTWARE_2020.MED_FORM;
using SMART_CLINIC_SOFTWARE_2020.Reports_Form;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class frmVISIT_EDIT : DevExpress.XtraEditors.XtraForm
    {
        public frmVISIT_EDIT()
        {
            InitializeComponent();
        }
        DataTable dt = new DataTable();
        private void frmVISIT_EDIT_Load(object sender, EventArgs e)
        {
            txtAPO_TIME.Text = string.Format(DateTime.Now.ToShortTimeString(), "hh:MM");
            try
            {
                foreach (var item in grbSERVICES.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }

                foreach (var item in grbPAY.Controls.OfType<DevExpress.XtraEditors.TextEdit>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }

                foreach (var item in grbSAVE.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
                {
                    if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                    {
                        item.Enabled = false;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnVIS_LIST_Click(object sender, EventArgs e)
        {
            try
            {
                frmVIS_LIST frmVIS_LIST = new frmVIS_LIST();
                frmVIS_LIST.ShowDialog();
                if (Classes.clsVISIT.VIS_ID != 0)
                {
                    txtVISIT_ID.Text = Classes.clsVISIT.VIS_ID.ToString();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void txtVISIT_ID_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtVISIT_ID.Text != "" && txtVISIT_ID.Text != "0" )
                {
                    if (Classes.clsVISIT.VISIT_DATATABLE.VIS_EDIT_LIST(Convert.ToInt64(txtVISIT_ID.Text), Convert.ToInt64(Classes.clsCLINIC.CLI_ID)).Rows.Count > 0)
                    {
                        dt = Classes.clsVISIT.VISIT_DATATABLE.VIS_EDIT_LIST(Convert.ToInt64(txtVISIT_ID.Text), Convert.ToInt64(Classes.clsCLINIC.CLI_ID));
                        Classes.clsVISIT.VIS_ID = Convert.ToInt64(txtVISIT_ID.Text);
                        Classes.clsCUST.CUST_ID = Convert.ToInt64(dt.Rows[0]["CUST_ID"]);
                        txtCUST_ID.Text = dt.Rows[0]["CUST_ID"].ToString();
                        txtCUST_F_Name.Text = dt.Rows[0]["CUST_F_NAME"].ToString();
                        txtCUST_S_Name.Text = dt.Rows[0]["CUST_S_NAME"].ToString();
                        txtCUST_T_Name.Text = dt.Rows[0]["CUST_T_NAME"].ToString();
                        txtCUST_L_Name.Text = dt.Rows[0]["CUST_L_NAME"].ToString();
                        txtCUST_AGE.Text = dt.Rows[0]["CUST_AGE"].ToString();
                        txtMONTH.Text = dt.Rows[0]["CUST_AGE_MONTH"].ToString();
                        txtGENDER.Text = dt.Rows[0]["CUST_GENDER"].ToString();
                        dtpCUST_DATE.Value = Convert.ToDateTime(dt.Rows[0]["CUST_BD"]);
                        dtpVIS_DATE.Value = Convert.ToDateTime(dt.Rows[0]["VIS_DATE"]);
                        cmbPAY_TYPE.Text = dt.Rows[0]["VIS_PAY_TYPE"].ToString();
                        cmbCLI_ID.Text = dt.Rows[0]["CLI_ID"].ToString();
                        cmbCLI_NAME.Text = dt.Rows[0]["CLI_NAME"].ToString();
                        txtCOST.Text = dt.Rows[0]["VIS_PRICE"].ToString();
                        txtDISCOUNT.Text = dt.Rows[0]["VIS_DISCOUNT"].ToString();
                        txtTOTAL.Text = dt.Rows[0]["VIS_TOTAL"].ToString();
                        txtUNPAY.Text = dt.Rows[0]["VIS_UNPAY"].ToString();
                        txtDOC_NOTE.Text = dt.Rows[0]["VIS_NOTE"].ToString();
                        txtCUST_NOTE.Text = dt.Rows[0]["CUST_NOTE"].ToString();

                        if (dt.Rows[0]["CUST_SAVE_STATE"].ToString() != "غير مؤمن")
                        {
                            txtCOM_NAME.Text = dt.Rows[0]["COM_NAME"].ToString();
                            txtCARD_ID.Text = dt.Rows[0]["CARD_ID"].ToString();
                            txtCUST_PRICE.Text = dt.Rows[0]["CUST_PRICE"].ToString();
                            txtCOM_PRICE.Text = dt.Rows[0]["COMPANY_PRICE"].ToString();
                        }
                        else
                        {
                            txtCOM_NAME.Text = "";
                            txtCARD_ID.Text = "0";
                            txtCUST_PRICE.Text = "0";
                            txtCOM_PRICE.Text = "0";
                        }
                    }
                }
                else
                {
                    CLEARTEXTBOX(this);
                    dtpCUST_DATE.Value = DateTime.Now;
                    dtpVIS_DATE.Value = DateTime.Now;
                    cmbPAY_TYPE.Text = "";
                    cmbCLI_ID.Text = "";
                    cmbCLI_NAME.Text = "";
                    Classes.clsVISIT.VIS_ID = 0;
                    Classes.clsCUST.CUST_ID = 0;
                    txtVISIT_ID.Text = "0";
                    txtVISIT_ID.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }           
        }

        public void CLEARTEXTBOX(Control ROOT)
        {
            try
            {
                foreach (Control ctrl in ROOT.Controls)
                {
                    CLEARTEXTBOX(ctrl);
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = string.Empty;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }
        private void btnDIAGNOIS_Click(object sender, EventArgs e)
        {
            try
            {
                frmDIAG_LIST frmDIG_LIST = new frmDIAG_LIST();
                frmDIG_LIST.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnSERVICES_Click(object sender, EventArgs e)
        {
            try
            {
                frmSER_LIST frmSER_LIST = new frmSER_LIST();
                frmSER_LIST.ShowDialog();
                txtCOST.Text = Classes.clsSERLIST.SERLIST_PRICE_TOTAL.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnMEDCINE_Click(object sender, EventArgs e)
        {
            try
            {
                frmMED_LIST frmMED_LIST = new frmMED_LIST();
                frmMED_LIST.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnCHECK_Click(object sender, EventArgs e)
        {
            try
            {
                frmMEDREQ frmMEDREQ_LIST = new frmMEDREQ();
                frmMEDREQ_LIST.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnREPORT_Click(object sender, EventArgs e)
        {
            try
            {
                Project_Form.frmMEDREP frmMEDREP = new Project_Form.frmMEDREP();
                frmMEDREP.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnHOLIDAY_Click(object sender, EventArgs e)
        {
            try
            {
                Project_Form.frmHOLIDAY frmHOL = new Project_Form.frmHOLIDAY();
                frmHOL.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnOLDMED_Click(object sender, EventArgs e)
        {
            try
            {
                MED_FORM.frmDES frmDES = new MED_FORM.frmDES();
                frmDES.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
           
        }

        private void btnAPO_Click(object sender, EventArgs e)
        {
            try
            {
                Project_Form.frmAPO frmAPO = new Project_Form.frmAPO();
                frmAPO.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtVISIT_ID.Text != "" && txtVISIT_ID.Text != "0" && Classes.clsVISIT.VIS_ID != 0)
                {
                    Classes.clsVISIT.VISIT_DATATABLE.InsertVIS(Convert.ToInt64(txtVISIT_ID.Text), Convert.ToInt64(Classes.clsVISIT.VISIT_DATATABLE.maxVIS_IDandVIS_CODE().Rows[0]["VIS_CODE"]), "كشف طبي", Convert.ToDateTime(string.Format(dtpVIS_DATE.Value.ToShortDateString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtAPO_TIME.Text.ToString(), "HH:MI")), "زيارة عادية", Convert.ToInt64(txtCUST_ID.Text), Convert.ToInt64(cmbCLI_ID.Text), Convert.ToInt64(dt.Rows[0]["DOC_ID"]), Convert.ToDecimal(txtCOST.Text), Convert.ToDecimal(txtDISCOUNT.Text), Convert.ToDecimal(txtTOTAL.Text), txtDOC_NOTE.Text, cmbPAY_TYPE.Text, Convert.ToDecimal(txtUNPAY.Text), Convert.ToInt64(txtVISIT_ID.Text), Convert.ToInt64(cmbCLI_ID.Text));
                    Classes.clsTRANSACTION.T_DATATABLE.InsertTRANSACTION_by_VIS(Convert.ToInt64(Classes.clsTRANSACTION.T_DATATABLE.maxT_CODE().Rows[0]["T_CODE"]), "كشف علاجي", Convert.ToDateTime(string.Format(DateTime.Now.ToShortDateString(), "yyyy/MM/dd")), TimeSpan.Parse(string.Format(txtAPO_TIME.Text.ToString(), "HH:MI")), "كشف علاجي", Convert.ToDecimal(txtCOST.Text), Convert.ToDecimal(txtDISCOUNT.Text), Convert.ToDecimal(txtUNPAY.Text), Convert.ToDecimal(txtTOTAL.Text), Convert.ToDecimal(txtCUST_PRICE.Text), Convert.ToDecimal(txtCOM_PRICE.Text), "", "فعالة", Convert.ToInt64(txtVISIT_ID.Text), Convert.ToInt64(cmbCLI_ID.Text), txtCOM_NAME.Text != "" ? Convert.ToInt64(dt.Rows[0]["COM_ID"]) : 0, Convert.ToInt64(txtVISIT_ID.Text), Convert.ToInt64(cmbCLI_ID.Text));
                    MessageBox.Show("تم تعديل البيانات بنجاح", "! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);

                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("VIS_DATE_INDEX"))
                {
                    MessageBox.Show("لا يمكن تكرار الزيارة", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                MessageBox.Show(ex.Message);
                
            }

        }

        private void txtCOST_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtCOST.Text != "" && txtCOST.Text != "0" && txtDISCOUNT.Text != "")
                {
                    txtTOTAL.Text = (Convert.ToDecimal(txtCOST.Text) - Convert.ToDecimal(txtDISCOUNT.Text)).ToString();
                    if (txtTOTAL.Text != "0" && txtCOM_NAME.Text != "" && txtCOM_NAME.Text != "لا يوجد تامين" && txtCARD_ID.Text != "" && txtCARD_ID.Text != "0" && dt.Rows.Count > 0)
                    {
                        int CUST_PER, COM_PER;
                        CUST_PER = 100 - Convert.ToInt16(dt.Rows[0]["CARD_PER"]);
                        COM_PER = Convert.ToInt16(dt.Rows[0]["CARD_PER"]);
                        txtCUST_PRICE.Text = (Convert.ToDecimal(txtTOTAL.Text) * (Convert.ToDecimal(CUST_PER) / 100)).ToString(".00");
                        txtCOM_PRICE.Text = (Convert.ToDecimal(txtTOTAL.Text) * (Convert.ToDecimal(COM_PER) / 100)).ToString(".00");
                    }
                    else
                    {
                        txtCUST_PRICE.Text = "0";
                        txtCOM_PRICE.Text = "0";
                    }
                }
                else
                {
                    txtCUST_PRICE.Text = "0";
                    txtCOM_PRICE.Text = "0";
                    txtTOTAL.Text = "0";
                    txtDISCOUNT.Text = "0";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }          
        }

        private void txtDISCOUNT_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtCOST.Text != "" && txtCOST.Text != "0" && txtDISCOUNT.Text != "")
                {
                    txtTOTAL.Text = (Convert.ToDecimal(txtCOST.Text) - Convert.ToDecimal(txtDISCOUNT.Text)).ToString();
                    if (txtTOTAL.Text != "0" && txtCOM_NAME.Text != "" && txtCOM_NAME.Text != "لا يوجد تامين" && txtCARD_ID.Text != "" && txtCARD_ID.Text != "0" && dt.Rows.Count > 0)
                    {
                        int CUST_PER, COM_PER;
                        CUST_PER = 100 - Convert.ToInt16(dt.Rows[0]["CARD_PER"]);
                        COM_PER = Convert.ToInt16(dt.Rows[0]["CARD_PER"]);
                        txtCUST_PRICE.Text = (Convert.ToDecimal(txtTOTAL.Text) * (Convert.ToDecimal(CUST_PER) / 100)).ToString(".00");
                        txtCOM_PRICE.Text = (Convert.ToDecimal(txtTOTAL.Text) * (Convert.ToDecimal(COM_PER) / 100)).ToString(".00");
                    }
                    else
                    {
                        txtCUST_PRICE.Text = "0";
                        txtCOM_PRICE.Text = "0";
                    }
                }
                else
                {
                    txtCUST_PRICE.Text = "0";
                    txtCOM_PRICE.Text = "0";
                    txtTOTAL.Text = "0";
                    txtDISCOUNT.Text = "0";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }          
        }

        private void btnCLEAR_CUST_Click(object sender, EventArgs e)
        {
            try
            {
                CLEARTEXTBOX(this);
                dtpCUST_DATE.Value = DateTime.Now;
                dtpVIS_DATE.Value = DateTime.Now;
                cmbPAY_TYPE.Text = "";
                cmbCLI_ID.Text = "";
                cmbCLI_NAME.Text = "";
                Classes.clsVISIT.VIS_ID = 0;
                Classes.clsCUST.CUST_ID = 0;
                txtVISIT_ID.Text = "0";
                txtVISIT_ID.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            try
            {
                CLEARTEXTBOX(this);
                dtpCUST_DATE.Value = DateTime.Now;
                dtpVIS_DATE.Value = DateTime.Now;
                cmbPAY_TYPE.Text = "";
                cmbCLI_ID.Text = "";
                cmbCLI_NAME.Text = "";
                Classes.clsVISIT.VIS_ID = 0;
                Classes.clsCUST.CUST_ID = 0;
                txtVISIT_ID.Text = "0";
                txtVISIT_ID.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void btnENDO_Click(object sender, EventArgs e)
        {
            try
            {
                MED_FORM.frm_ENDO frmENDO = new MED_FORM.frm_ENDO();
                frmENDO.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void btnCUST_CARD_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtVISIT_ID.Text != "" && txtCUST_ID.Text != "" && cmbCLI_ID.Text != "")
                {
                    frmCUST_PAST_HISTORY_REPORT frmCUST_FILE = new frmCUST_PAST_HISTORY_REPORT();
                    frmCUST_PAST_HISTORY_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                    frmCUST_PAST_HISTORY_REPORT.CUST_ID = Convert.ToInt64(txtCUST_ID.Text);
                    frmCUST_FILE.ShowDialog();
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void btnPRINT_Click(object sender, EventArgs e)
        {
            try
            {
                if (txtVISIT_ID.Text != "" && txtCUST_ID.Text != "" && txtCUST_F_Name.Text != "" && cmbCLI_ID.Text != "")
                {
                    frmVIS_SER_PRICE_REPORT vis_ser_price = new frmVIS_SER_PRICE_REPORT();
                    frmVIS_SER_PRICE_REPORT.VIS_ID = Convert.ToInt64(txtVISIT_ID.Text);
                    frmVIS_SER_PRICE_REPORT.CUST_ID = Convert.ToInt64(Classes.clsCUST.CUST_ID);
                    frmVIS_SER_PRICE_REPORT.CLI_ID = Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID);
                    vis_ser_price.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void btnCLINIC_LIST_Click(object sender, EventArgs e)
        {
            try
            {
                LIST_FORM.frmCLINIC_LIST frmclinic_LIST = new LIST_FORM.frmCLINIC_LIST();
                frmclinic_LIST.ShowDialog();
                cmbCLI_ID.Text = Classes.clsCLINIC.CLI_ID.ToString();
                cmbCLI_NAME.Text = Classes.clsCLINIC.CLI_NAME;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}