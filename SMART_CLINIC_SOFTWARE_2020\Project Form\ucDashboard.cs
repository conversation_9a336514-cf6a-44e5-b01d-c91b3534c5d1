using System;
using System.Drawing;
using System.Globalization;
using System.Windows.Forms;
using System.Data;
using System.Data.SqlClient;

namespace SMART_CLINIC_SOFTWARE_2020.Project_Form
{
    public partial class ucDashboard : UserControl
    {
        private Timer timeTimer;
        private Timer dataTimer;

        public ucDashboard()
        {
            InitializeComponent();
            InitializeTimers();
            ApplyModernDesign();
        }

        private void ucDashboard_Load(object sender, EventArgs e)
        {
            LoadDashboardData();
            UpdateDateTime();
            StartTimers();
        }

        private void InitializeTimers()
        {
            // مؤقت لتحديث الوقت كل ثانية
            timeTimer = new Timer();
            timeTimer.Interval = 1000; // ثانية واحدة
            timeTimer.Tick += TimeTimer_Tick;

            // مؤقت لتحديث البيانات كل 30 ثانية
            dataTimer = new Timer();
            dataTimer.Interval = 30000; // 30 ثانية
            dataTimer.Tick += DataTimer_Tick;
        }

        private void StartTimers()
        {
            timeTimer.Start();
            dataTimer.Start();
        }

        private void TimeTimer_Tick(object sender, EventArgs e)
        {
            UpdateDateTime();
        }

        private void DataTimer_Tick(object sender, EventArgs e)
        {
            LoadDashboardData();
        }

        private void UpdateDateTime()
        {
            try
            {
                DateTime now = DateTime.Now;
                
                // تحديث الوقت
                lblCurrentTime.Text = now.ToString("HH:mm:ss");
                
                // تحديث التاريخ بالعربية
                CultureInfo arabicCulture = new CultureInfo("ar-SA");
                string dayName = GetArabicDayName(now.DayOfWeek);
                string monthName = GetArabicMonthName(now.Month);
                
                lblCurrentDate.Text = $"🗓️ {dayName}، {now.Day} {monthName} {now.Year}";
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، عرض تاريخ افتراضي
                lblCurrentDate.Text = "🗓️ التاريخ الحالي";
                lblCurrentTime.Text = "00:00:00";
            }
        }

        private string GetArabicDayName(DayOfWeek dayOfWeek)
        {
            switch (dayOfWeek)
            {
                case DayOfWeek.Sunday: return "الأحد";
                case DayOfWeek.Monday: return "الاثنين";
                case DayOfWeek.Tuesday: return "الثلاثاء";
                case DayOfWeek.Wednesday: return "الأربعاء";
                case DayOfWeek.Thursday: return "الخميس";
                case DayOfWeek.Friday: return "الجمعة";
                case DayOfWeek.Saturday: return "السبت";
                default: return "غير محدد";
            }
        }

        private string GetArabicMonthName(int month)
        {
            string[] months = {
                "", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            };
            return month >= 1 && month <= 12 ? months[month] : "غير محدد";
        }

        private void LoadDashboardData()
        {
            try
            {
                LoadPatientsCount();
                LoadTodayAppointments();
                LoadTotalRevenue();
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، عرض قيم افتراضية
                lblPatientsCount.Text = "0";
                lblAppointmentsCount.Text = "0";
                lblRevenueAmount.Text = "0.00 ريال";
            }
        }

        private void LoadPatientsCount()
        {
            try
            {
                // محاكاة تحميل عدد المراجعين من قاعدة البيانات
                // يمكن استبدال هذا بالاستعلام الفعلي من قاعدة البيانات
                
                // مثال على استعلام SQL (يحتاج إلى تعديل حسب هيكل قاعدة البيانات)
                /*
                string query = "SELECT COUNT(*) FROM Patients";
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    SqlCommand command = new SqlCommand(query, connection);
                    connection.Open();
                    int count = (int)command.ExecuteScalar();
                    lblPatientsCount.Text = count.ToString();
                }
                */
                
                // قيمة تجريبية للعرض
                Random random = new Random();
                int patientsCount = random.Next(50, 200);
                lblPatientsCount.Text = patientsCount.ToString();
                
                // تأثير تحديث البيانات
                AnimateCounterUpdate(lblPatientsCount);
            }
            catch (Exception ex)
            {
                lblPatientsCount.Text = "0";
            }
        }

        private void LoadTodayAppointments()
        {
            try
            {
                // محاكاة تحميل مواعيد اليوم من قاعدة البيانات
                // يمكن استبدال هذا بالاستعلام الفعلي من قاعدة البيانات
                
                // مثال على استعلام SQL
                /*
                string query = "SELECT COUNT(*) FROM Appointments WHERE CAST(AppointmentDate AS DATE) = CAST(GETDATE() AS DATE)";
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    SqlCommand command = new SqlCommand(query, connection);
                    connection.Open();
                    int count = (int)command.ExecuteScalar();
                    lblAppointmentsCount.Text = count.ToString();
                }
                */
                
                // قيمة تجريبية للعرض
                Random random = new Random();
                int appointmentsCount = random.Next(10, 50);
                lblAppointmentsCount.Text = appointmentsCount.ToString();
                
                // تأثير تحديث البيانات
                AnimateCounterUpdate(lblAppointmentsCount);
            }
            catch (Exception ex)
            {
                lblAppointmentsCount.Text = "0";
            }
        }

        private void LoadTotalRevenue()
        {
            try
            {
                // محاكاة تحميل إجمالي الخزنة من قاعدة البيانات
                // يمكن استبدال هذا بالاستعلام الفعلي من قاعدة البيانات
                
                // مثال على استعلام SQL
                /*
                string query = "SELECT SUM(Amount) FROM CashBox WHERE IsActive = 1";
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    SqlCommand command = new SqlCommand(query, connection);
                    connection.Open();
                    object result = command.ExecuteScalar();
                    decimal totalAmount = result != DBNull.Value ? (decimal)result : 0;
                    lblRevenueAmount.Text = totalAmount.ToString("N2") + " ريال";
                }
                */
                
                // قيمة تجريبية للعرض
                Random random = new Random();
                decimal totalRevenue = (decimal)(random.NextDouble() * 50000 + 10000);
                lblRevenueAmount.Text = totalRevenue.ToString("N2") + " ريال";
                
                // تأثير تحديث البيانات
                AnimateCounterUpdate(lblRevenueAmount);
            }
            catch (Exception ex)
            {
                lblRevenueAmount.Text = "0.00 ريال";
            }
        }

        private void AnimateCounterUpdate(Label label)
        {
            // تأثير بصري بسيط عند تحديث البيانات
            Color originalColor = label.ForeColor;
            label.ForeColor = Color.FromArgb(255, 255, 0); // أصفر مؤقت
            
            Timer animationTimer = new Timer();
            animationTimer.Interval = 500;
            animationTimer.Tick += (s, e) =>
            {
                label.ForeColor = originalColor;
                animationTimer.Stop();
                animationTimer.Dispose();
            };
            animationTimer.Start();
        }

        private void ApplyModernDesign()
        {
            // تطبيق التصميم الحديث على العناصر
            this.BackColor = Color.FromArgb(64, 68, 75);
            
            // تحسين مظهر البطاقات
            AddCardHoverEffects();
        }

        private void AddCardHoverEffects()
        {
            // إضافة تأثيرات التمرير للبطاقات
            AddHoverEffect(panelPatients);
            AddHoverEffect(panelAppointments);
            AddHoverEffect(panelRevenue);
            AddHoverEffect(panelTime);
        }

        private void AddHoverEffect(DevExpress.XtraEditors.PanelControl panel)
        {
            Color originalBorderColor = panel.Appearance.BorderColor;
            
            panel.MouseEnter += (s, e) =>
            {
                panel.Appearance.BorderColor = Color.FromArgb(255, 255, 255);
            };
            
            panel.MouseLeave += (s, e) =>
            {
                panel.Appearance.BorderColor = originalBorderColor;
            };
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                timeTimer?.Stop();
                timeTimer?.Dispose();
                dataTimer?.Stop();
                dataTimer?.Dispose();
                
                if (components != null)
                {
                    components.Dispose();
                }
            }
            base.Dispose(disposing);
        }
    }
}
