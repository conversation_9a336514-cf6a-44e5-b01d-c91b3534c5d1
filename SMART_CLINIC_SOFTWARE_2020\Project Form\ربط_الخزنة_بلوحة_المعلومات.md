# 💰 ربط الخزنة الفعلية بلوحة المعلومات

## 🎯 الهدف
ربط المبلغ الكلي في لوحة المعلومات بالمبلغ الفعلي للخزنة من قاعدة البيانات، مع عرض معلومات تفصيلية عن الخزائن.

## 🔧 التحديثات المطبقة

### 1. دالة GetTotalRevenue() المحسّنة:
```csharp
private decimal GetTotalRevenue()
{
    try
    {
        // الحصول على المبلغ الفعلي للخزنة من قاعدة البيانات
        DataTable dt = Classes.clsSTOCK_DATA.STOCK_DATATABLE.GetData();
        decimal totalAmount = 0;
        
        foreach (DataRow row in dt.Rows)
        {
            if (row["Money"] != DBNull.Value)
            {
                decimal stockMoney = Convert.ToDecimal(row["Money"]);
                totalAmount += stockMoney;
            }
        }
        
        return totalAmount;
    }
    catch (Exception ex)
    {
        return 0;
    }
}
```

#### المميزات:
- **المبلغ الفعلي**: من جدول Stock_Data مباشرة
- **جمع جميع الخزائن**: إجمالي أرصدة جميع الخزائن
- **معالجة أخطاء**: قيمة افتراضية آمنة

### 2. دالة GetTotalStockCount() الجديدة:
```csharp
private int GetTotalStockCount()
{
    try
    {
        DataTable dt = Classes.clsSTOCK_DATA.STOCK_DATATABLE.GetData();
        return dt.Rows.Count;
    }
    catch (Exception ex)
    {
        return 0;
    }
}
```

#### المميزات:
- **عدد الخزائن**: إجمالي عدد الخزائن في النظام
- **حساب دقيق**: من عدد الصفوف في الجدول

### 3. دالة GetMainStockName():
```csharp
private string GetMainStockName()
{
    try
    {
        DataTable dt = Classes.clsSTOCK_DATA.STOCK_DATATABLE.GetData();
        decimal maxAmount = 0;
        string mainStockName = "الخزنة الرئيسية";
        
        foreach (DataRow row in dt.Rows)
        {
            if (row["Money"] != DBNull.Value && row["Stock_Name"] != DBNull.Value)
            {
                decimal stockMoney = Convert.ToDecimal(row["Money"]);
                string stockName = row["Stock_Name"].ToString();
                
                // البحث عن الخزنة الرئيسية أو أكبر رصيد
                if (stockName.Contains("الرئيسية") || stockName.Contains("رئيسية") || stockMoney > maxAmount)
                {
                    maxAmount = stockMoney;
                    mainStockName = stockName;
                }
            }
        }
        
        return mainStockName;
    }
    catch (Exception ex)
    {
        return "الخزنة الرئيسية";
    }
}
```

#### المميزات:
- **الخزنة الرئيسية**: البحث عن الخزنة الرئيسية أو أكبر رصيد
- **بحث ذكي**: بالاسم أو بالمبلغ
- **اسم واضح**: للعرض في البطاقة

### 4. دالة GetMaxStockAmount():
```csharp
private decimal GetMaxStockAmount()
{
    try
    {
        DataTable dt = Classes.clsSTOCK_DATA.STOCK_DATATABLE.GetData();
        decimal maxAmount = 0;
        
        foreach (DataRow row in dt.Rows)
        {
            if (row["Money"] != DBNull.Value)
            {
                decimal stockMoney = Convert.ToDecimal(row["Money"]);
                if (stockMoney > maxAmount)
                {
                    maxAmount = stockMoney;
                }
            }
        }
        
        return maxAmount;
    }
    catch (Exception ex)
    {
        return 0;
    }
}
```

#### المميزات:
- **أكبر رصيد**: في جميع الخزائن
- **مقارنة دقيقة**: لجميع الأرصدة

## 🎨 بطاقة الخزنة المحسّنة

### الشكل الجديد:
```csharp
// بطاقة الخزنة - مع معلومات تفصيلية
decimal totalRevenue = GetTotalRevenue();
int stockCount = GetTotalStockCount();
string mainStockName = GetMainStockName();
string revenueInfo = $"💰 إجمالي الخزنة\n🏦 {stockCount} خزنة | {mainStockName}";
Panel revenueCard = CreateStatsCard(revenueInfo, FormatIraqiDinar(totalRevenue), 
    Color.FromArgb(255, 193, 7), new Point(590, 120));
```

### المعلومات المعروضة:
- **المبلغ الكلي**: إجمالي جميع الخزائن بالدينار العراقي
- **عدد الخزائن**: إجمالي عدد الخزائن في النظام
- **الخزنة الرئيسية**: اسم الخزنة الرئيسية أو أكبر رصيد

## 📋 كيفية الاستخدام من صفحة الخزنة

### 1. في صفحة إضافة خزنة جديدة (frmSTOCK.cs):
```csharp
private void btnSAVE_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (txtSTOCK_Name.Text != "" && txtSTOCK_CODE.Text != "")
        {
            // إضافة الخزنة الجديدة
            Classes.clsSTOCK_DATA.STOCK_DATATABLE.InsertSTOCK(
                Convert.ToInt64(txtSTOCK_CODE.Text), 
                txtSTOCK_Name.Text, 
                Convert.ToDecimal(txtMONEY.Text)
            );
            
            // تحديث لوحة المعلومات فوراً
            frmMAIN.RefreshRevenueAmount();
            
            Clear_Date();
            MessageBox.Show("تم حفظ البيانات بشكل صحيح");
        }
        else
        {
            MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
        }
    }
    catch (Exception ex)
    {
        MessageBox.Show(ex.Message);
    }
}
```

### 2. في صفحة تعديل الخزنة:
```csharp
private void btnEDITE_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (MessageBox.Show("هل انت متاكد من تعديل البيانات", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
        {
            if (gridView1.RowCount > 0 && txtSTOCK_CODE.Text != "" && txtSTOCK_Name.Text != "")
            {
                // تعديل بيانات الخزنة
                Classes.clsSTOCK_DATA.STOCK_DATATABLE.UpdateSTOCK(
                    Convert.ToInt64(txtSTOCK_CODE.Text), 
                    txtSTOCK_Name.Text, 
                    Convert.ToDecimal(txtMONEY.Text), 
                    Convert.ToInt64(txtSTOCK_CODE.Text)
                );
                
                // تحديث لوحة المعلومات
                frmMAIN.RefreshRevenueAmount();
                
                Clear_Date();
                MessageBox.Show("تم تعديل البيانات بشكل صحيح");
            }
        }
    }
    catch (Exception ex)
    {
        MessageBox.Show(ex.Message);
    }
}
```

### 3. في صفحة حذف الخزنة:
```csharp
private void btnDELETE_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (MessageBox.Show("هل انت متاكد من حذف البيانات", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
        {
            if (gridView1.RowCount > 0 && txtSTOCK_CODE.Text != "" && txtSTOCK_Name.Text != "")
            {
                // حذف الخزنة
                Classes.clsSTOCK_DATA.STOCK_DATATABLE.DeleteSTOCK(Convert.ToInt64(txtSTOCK_CODE.Text));
                
                // تحديث لوحة المعلومات
                frmMAIN.RefreshRevenueAmount();
                
                Clear_Date();
                MessageBox.Show("تم حذف البيانات بشكل صحيح");
            }
        }
    }
    catch (Exception ex)
    {
        MessageBox.Show(ex.Message);
    }
}
```

### 4. في صفحة إضافة رصيد للخزنة (frmStock_AddMoney.cs):
```csharp
private void btnSAVE_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
{
    try
    {
        if (txtMONEY.Text != "0" && txtItemName.Text != "")
        {
            if (MessageBox.Show("هل انت متاكد سيتم اضافة هذا الرصيد للخزنه", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                // تحديث رصيد الخزنة
                Classes.clsSTOCK_DATA.STOCK_DATATABLE.UpdateSTOCK_MONEY(
                    cmbSTOCK_NAME.Text, 
                    Convert.ToDecimal(txtTOTAL.Text), 
                    cmbSTOCK_NAME.Text
                );
                
                // إضافة سجل الإضافة
                Classes.clsStock_AddMoney.STOCK_INSERT_DATATABLE.InsertSTOCK_ADDMONEY(
                    Convert.ToInt64(cmbSTOCK_ID.Text), 
                    Convert.ToDecimal(txtMONEY.Text), 
                    string.Format(DtbDate.Value.ToString(), "yyyy/MM/dd"), 
                    txtItemName.Text, 
                    "رصيد اضافى", 
                    txtReason.Text
                );
                
                // تحديث لوحة المعلومات فوراً
                frmMAIN.RefreshRevenueAmount();
                
                Clear_Date();
                MessageBox.Show("تم حفظ البيانات بشكل صحيح");
            }
        }
    }
    catch (Exception ex)
    {
        MessageBox.Show(ex.Message);
    }
}
```

## 🎨 الشكل النهائي لبطاقة الخزنة

```
┌─────────────────────────────────┐
│  💰 إجمالي الخزنة              │
│  🏦 3 خزنة | الخزنة الرئيسية   │
│                                 │
│        52,750,000 د.ع          │
│          (أصفر)                │
└─────────────────────────────────┘
```

## 🔄 التحديث التلقائي

### 1. التحديث الدوري:
- **كل 30 ثانية**: تحديث تلقائي لجميع البيانات
- **عند إضافة خزنة**: تحديث فوري للعدد والمبلغ
- **عند تعديل خزنة**: تحديث فوري للمعلومات
- **عند حذف خزنة**: تحديث فوري للعدد والمبلغ
- **عند إضافة رصيد**: تحديث فوري للمبلغ الكلي

### 2. التحديث الذكي:
- **المبلغ الكلي**: مجموع جميع الخزائن
- **عدد الخزائن**: يتغير عند الإضافة أو الحذف
- **الخزنة الرئيسية**: تتحدث حسب الاسم أو أكبر رصيد

## 📊 البيانات المعروضة

### المعلومات الأساسية:
- **المبلغ الكلي**: مجموع جميع الخزائن من جدول Stock_Data
- **عدد الخزائن**: إجمالي عدد الخزائن في النظام
- **الخزنة الرئيسية**: اسم الخزنة الرئيسية أو أكبر رصيد

### المعلومات التفصيلية:
- **تنسيق الدينار العراقي**: مع فواصل الآلاف + د.ع
- **أيقونات تعبيرية**: 💰 للمبلغ، 🏦 للخزائن
- **معلومات محدثة**: دائماً حسب آخر تحديث

## 🚀 المميزات المحققة

### ✅ البيانات الحقيقية:
- **مبلغ فعلي**: من جدول Stock_Data مباشرة
- **تحديث فوري**: عند إضافة أو تعديل الخزائن
- **جمع دقيق**: لجميع أرصدة الخزائن

### ✅ المعلومات التفصيلية:
- **المبلغ الكلي**: بالدينار العراقي مع التنسيق
- **عدد الخزائن**: إجمالي الخزائن في النظام
- **الخزنة الرئيسية**: اسم الخزنة الرئيسية
- **أيقونات واضحة**: للتمييز البصري

### ✅ التحديث الشامل:
- **تحديث مع العمليات**: إضافة، تعديل، حذف، إضافة رصيد
- **حساب دقيق**: للمبلغ الكلي والعدد
- **معلومات محدثة**: دائماً حسب الوضع الحالي

### ✅ التفاعل المباشر:
- **تحديث فوري**: عند أي تغيير في الخزنة
- **تحديث تلقائي**: كل 30 ثانية
- **تأثيرات بصرية**: عند التحديث

## 🎉 النتيجة النهائية

الآن لوحة المعلومات تعرض:
- **المبلغ الفعلي للخزنة** من قاعدة البيانات (بدلاً من الأرقام العشوائية)
- **معلومات تفصيلية شاملة** (عدد الخزائن، الخزنة الرئيسية)
- **تحديث فوري** عند إضافة أو تعديل أو حذف خزنة
- **تحديث فوري** عند إضافة رصيد للخزنة
- **تنسيق احترافي** بالدينار العراقي

هذا يجعل لوحة المعلومات تعكس الواقع الفعلي للوضع المالي في العيادة! 💰✨
