# 📅 ربط المواعيد الفعلية بلوحة المعلومات

## 🎯 الهدف
ربط عدد المواعيد في لوحة المعلومات بالعدد الفعلي للمواعيد من قاعدة البيانات، مع عرض معلومات تفصيلية عن المواعيد اليومية.

## 🔧 التحديثات المطبقة

### 1. دالة GetTodayAppointments() المحسّنة:
```csharp
private int GetTodayAppointments()
{
    try
    {
        // الحصول على العدد الفعلي للمواعيد من قاعدة البيانات
        DataTable dt = Classes.clsAPO.APO_DATATABLE.GetData();
        int count = 0;
        DateTime today = DateTime.Today;
        
        foreach (DataRow row in dt.Rows)
        {
            if (row["APO_DATE"] != DBNull.Value)
            {
                DateTime apoDate = Convert.ToDateTime(row["APO_DATE"]);
                // التحقق من أن الموعد لليوم الحالي
                if (apoDate.Date == today)
                {
                    // تصفية حسب العيادة إذا كان هناك معرف عيادة
                    if (Classes.clsCLINIC.CLI_ID == 0 || 
                        (row["CLI_ID"] != DBNull.Value && Convert.ToInt64(row["CLI_ID"]) == Classes.clsCLINIC.CLI_ID))
                    {
                        count++;
                    }
                }
            }
        }
        return count;
    }
    catch (Exception ex)
    {
        return 0;
    }
}
```

#### المميزات:
- **العدد الفعلي**: من جدول APO_TBL مباشرة
- **تصفية بالتاريخ**: مواعيد اليوم الحالي فقط
- **تصفية بالعيادة**: حسب العيادة الحالية
- **معالجة أخطاء**: قيمة افتراضية آمنة

### 2. دالة GetCompletedAppointmentsToday() الجديدة:
```csharp
private int GetCompletedAppointmentsToday()
{
    try
    {
        DataTable dt = Classes.clsAPO.APO_DATATABLE.GetData();
        int count = 0;
        DateTime today = DateTime.Today;
        DateTime now = DateTime.Now;
        
        foreach (DataRow row in dt.Rows)
        {
            if (row["APO_DATE"] != DBNull.Value && row["APO_TIME"] != DBNull.Value)
            {
                DateTime apoDate = Convert.ToDateTime(row["APO_DATE"]);
                TimeSpan apoTime = (TimeSpan)row["APO_TIME"];
                
                // التحقق من أن الموعد لليوم الحالي ووقته قد مضى
                if (apoDate.Date == today)
                {
                    DateTime appointmentDateTime = apoDate.Date.Add(apoTime);
                    if (appointmentDateTime < now)
                    {
                        // تصفية حسب العيادة
                        if (Classes.clsCLINIC.CLI_ID == 0 || 
                            (row["CLI_ID"] != DBNull.Value && Convert.ToInt64(row["CLI_ID"]) == Classes.clsCLINIC.CLI_ID))
                        {
                            count++;
                        }
                    }
                }
            }
        }
        return count;
    }
    catch (Exception ex)
    {
        return 0;
    }
}
```

#### المميزات:
- **المواعيد المكتملة**: التي انتهى وقتها
- **مقارنة الوقت**: مع الوقت الحالي
- **حساب دقيق**: للمواعيد المنجزة

### 3. دالة GetRemainingAppointmentsToday():
```csharp
private int GetRemainingAppointmentsToday()
{
    try
    {
        int totalToday = GetTodayAppointments();
        int completedToday = GetCompletedAppointmentsToday();
        return totalToday - completedToday;
    }
    catch (Exception ex)
    {
        return 0;
    }
}
```

#### المميزات:
- **المواعيد المتبقية**: الباقية لليوم
- **حساب تلقائي**: الكلي - المكتمل
- **تحديث مستمر**: مع تقدم الوقت

### 4. دالة GetNextAppointmentTime():
```csharp
private string GetNextAppointmentTime()
{
    try
    {
        DataTable dt = Classes.clsAPO.APO_DATATABLE.GetData();
        DateTime today = DateTime.Today;
        DateTime now = DateTime.Now;
        TimeSpan? nextTime = null;
        
        foreach (DataRow row in dt.Rows)
        {
            if (row["APO_DATE"] != DBNull.Value && row["APO_TIME"] != DBNull.Value)
            {
                DateTime apoDate = Convert.ToDateTime(row["APO_DATE"]);
                TimeSpan apoTime = (TimeSpan)row["APO_TIME"];
                
                // التحقق من أن الموعد لليوم الحالي ولم يحن وقته بعد
                if (apoDate.Date == today)
                {
                    DateTime appointmentDateTime = apoDate.Date.Add(apoTime);
                    if (appointmentDateTime > now)
                    {
                        // تصفية حسب العيادة
                        if (Classes.clsCLINIC.CLI_ID == 0 || 
                            (row["CLI_ID"] != DBNull.Value && Convert.ToInt64(row["CLI_ID"]) == Classes.clsCLINIC.CLI_ID))
                        {
                            if (nextTime == null || apoTime < nextTime)
                            {
                                nextTime = apoTime;
                            }
                        }
                    }
                }
            }
        }
        
        if (nextTime.HasValue)
        {
            return nextTime.Value.ToString(@"hh\:mm");
        }
        else
        {
            return "لا توجد";
        }
    }
    catch (Exception ex)
    {
        return "غير محدد";
    }
}
```

#### المميزات:
- **الموعد القادم**: أقرب موعد لم يحن وقته
- **تنسيق الوقت**: بصيغة HH:MM
- **رسائل واضحة**: "لا توجد" أو "غير محدد"

## 🎨 بطاقة المواعيد المحسّنة

### الشكل الجديد:
```csharp
// بطاقة المواعيد - مع معلومات تفصيلية
string todayDate = DateTime.Now.ToString("dd/MM/yyyy");
int totalAppointments = GetTodayAppointments();
int remainingAppointments = GetRemainingAppointmentsToday();
string nextAppointment = GetNextAppointmentTime();
string appointmentsInfo = $"📅 مواعيد اليوم {todayDate}\n⏰ القادم: {nextAppointment} | متبقي: {remainingAppointments}";
Panel appointmentsCard = CreateStatsCard(appointmentsInfo, totalAppointments.ToString(), 
    Color.FromArgb(0, 123, 255), new Point(320, 120));
```

### المعلومات المعروضة:
- **التاريخ الحالي**: dd/MM/yyyy
- **العدد الكلي**: جميع مواعيد اليوم
- **الموعد القادم**: أقرب موعد لم يحن وقته
- **المواعيد المتبقية**: عدد المواعيد الباقية

## 📋 كيفية الاستخدام من صفحة المواعيد

### 1. في صفحة إضافة موعد جديد (frmAPO.cs):
```csharp
private void btnSAVE_Click(object sender, EventArgs e)
{
    try
    {
        if (txtAPO_CODE.Text != "" && cmbCUST_ID.Text != "" && cmbDOC_ID.Text != "" && txtAPO_TIME.Text != " : ")
        {
            // إضافة الموعد الجديد
            Classes.clsAPO.APO_DATATABLE.InsertAPO(
                Convert.ToInt64(txtAPO_CODE.Text), 
                Convert.ToDateTime(string.Format(dtpAPO_DATE.Value.ToString(), "yyyy/MM/dd")), 
                TimeSpan.Parse(string.Format(txtAPO_TIME.Text.ToString(), "HH:MI")), 
                txtAPO_NAME.Text, 
                txtAPO_NOTE.Text, 
                Convert.ToInt64(Classes.clsCLINIC.CLI_ID), 
                Convert.ToInt64(cmbCUST_ID.Text), 
                Convert.ToInt64(cmbDOC_ID.Text), 
                Classes.clsVISIT.VIS_ID
            );
            
            // تحديث لوحة المعلومات فوراً
            frmMAIN.RefreshAppointmentsCount();
            
            Clear_Date();
        }
    }
    catch (Exception ex)
    {
        MessageBox.Show(ex.Message);
    }
}
```

### 2. في صفحة تعديل الموعد:
```csharp
private void btnEDITE_Click(object sender, EventArgs e)
{
    try
    {
        if (gridView1.RowCount > 0 && txtAPO_CODE.Text != "")
        {
            // تعديل بيانات الموعد
            Classes.clsAPO.APO_DATATABLE.UpdateAPO(/* البيانات */);
            
            // تحديث لوحة المعلومات
            frmMAIN.RefreshAppointmentsCount();
            
            Clear_Date();
        }
    }
    catch (Exception ex)
    {
        MessageBox.Show(ex.Message);
    }
}
```

### 3. في صفحة حذف الموعد:
```csharp
private void btnDELETE_Click(object sender, EventArgs e)
{
    try
    {
        if (/* شروط التحقق */)
        {
            // حذف الموعد
            Classes.clsAPO.APO_DATATABLE.DeleteAPO(Classes.clsAPO.APO_ID);
            
            // تحديث لوحة المعلومات
            frmMAIN.RefreshAppointmentsCount();
            
            Clear_Date();
        }
    }
    catch (Exception ex)
    {
        MessageBox.Show(ex.Message);
    }
}
```

## 🎨 الشكل النهائي لبطاقة المواعيد

```
┌─────────────────────────────────┐
│  📅 مواعيد اليوم 17/07/2025     │
│  ⏰ القادم: 14:30 | متبقي: 8    │
│                                 │
│              15                 │
│           (أزرق)               │
└─────────────────────────────────┘
```

## 🔄 التحديث التلقائي

### 1. التحديث الدوري:
- **كل 30 ثانية**: تحديث تلقائي لجميع البيانات
- **عند إضافة موعد**: تحديث فوري للعدد
- **عند تعديل موعد**: تحديث فوري للمعلومات
- **عند حذف موعد**: تحديث فوري للعدد

### 2. التحديث الذكي:
- **الموعد القادم**: يتغير تلقائياً مع الوقت
- **المواعيد المتبقية**: تقل تلقائياً مع انتهاء المواعيد
- **المواعيد المكتملة**: تزيد تلقائياً مع مرور الوقت

## 📊 البيانات المعروضة

### المعلومات الأساسية:
- **العدد الكلي**: جميع مواعيد اليوم من جدول APO_TBL
- **التاريخ الحالي**: بتنسيق dd/MM/yyyy
- **تصفية حسب العيادة**: إذا كان هناك عيادة محددة

### المعلومات التفصيلية:
- **الموعد القادم**: أقرب موعد لم يحن وقته
- **المواعيد المتبقية**: عدد المواعيد الباقية لليوم
- **المواعيد المكتملة**: التي انتهى وقتها

## 🚀 المميزات المحققة

### ✅ البيانات الحقيقية:
- **عدد فعلي**: من جدول APO_TBL مباشرة
- **تحديث فوري**: عند إضافة أو تعديل المواعيد
- **تصفية ذكية**: حسب العيادة والتاريخ والوقت

### ✅ المعلومات التفصيلية:
- **العدد الكلي**: جميع مواعيد اليوم
- **الموعد القادم**: مع الوقت المحدد
- **المواعيد المتبقية**: العدد الباقي لليوم
- **التاريخ الحالي**: واضح ومرئي

### ✅ التحديث الذكي:
- **تحديث مع الوقت**: المواعيد تتغير تلقائياً
- **حساب دقيق**: للمواعيد المكتملة والمتبقية
- **معلومات محدثة**: دائماً حسب الوقت الحالي

## 🎉 النتيجة النهائية

الآن لوحة المعلومات تعرض:
- **العدد الفعلي للمواعيد** من قاعدة البيانات (بدلاً من الأرقام العشوائية)
- **معلومات تفصيلية** عن المواعيد (القادم، المتبقي)
- **تحديث فوري** عند إضافة أو تعديل أو حذف موعد
- **تحديث ذكي مع الوقت** للمواعيد المكتملة والمتبقية
- **تصفية دقيقة** حسب العيادة والتاريخ

هذا يجعل لوحة المعلومات تعكس الواقع الفعلي لجدولة المواعيد! 📅✨
