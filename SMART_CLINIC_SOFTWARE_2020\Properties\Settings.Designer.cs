﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SMART_CLINIC_SOFTWARE_2020.Properties {
    
    
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "16.8.1.0")]
    internal sealed partial class Settings : global::System.Configuration.ApplicationSettingsBase {
        
        private static Settings defaultInstance = ((Settings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new Settings())));
        
        public static Settings Default {
            get {
                return defaultInstance;
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.ConnectionString)]
        [global::System.Configuration.DefaultSettingValueAttribute("Data Source=DESKTOP-TB02KDT;Initial Catalog=ClinicDataBase_2020;User ID=CLINIC_US" +
            "ER_2020;Password=19931993CLINIC2020")]
        public string ClinicDataBase_2020ConnectionString {
            get {
                return ((string)(this["ClinicDataBase_2020ConnectionString"]));
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string SERVER_NAME {
            get {
                return ((string)(this["SERVER_NAME"]));
            }
            set {
                this["SERVER_NAME"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string DATABASE_NAME {
            get {
                return ((string)(this["DATABASE_NAME"]));
            }
            set {
                this["DATABASE_NAME"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string DB_USER_NAME {
            get {
                return ((string)(this["DB_USER_NAME"]));
            }
            set {
                this["DB_USER_NAME"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string DB_PASSWORD {
            get {
                return ((string)(this["DB_PASSWORD"]));
            }
            set {
                this["DB_PASSWORD"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string CON_STRING {
            get {
                return ((string)(this["CON_STRING"]));
            }
            set {
                this["CON_STRING"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("2010-01-01")]
        public global::System.DateTime L_S_DATE {
            get {
                return ((global::System.DateTime)(this["L_S_DATE"]));
            }
            set {
                this["L_S_DATE"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("2010-01-01")]
        public global::System.DateTime L_E_DATE {
            get {
                return ((global::System.DateTime)(this["L_E_DATE"]));
            }
            set {
                this["L_E_DATE"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("0")]
        public string L_KEY {
            get {
                return ((string)(this["L_KEY"]));
            }
            set {
                this["L_KEY"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("2010-01-01")]
        public global::System.DateTime Active_Date {
            get {
                return ((global::System.DateTime)(this["Active_Date"]));
            }
            set {
                this["Active_Date"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("Data Source=DESKTOP-TB02KDT;Initial Catalog=ClinicDataBase_2020;User ID=CLINIC_US" +
            "ER_2020;Password=19931993CLINIC2020")]
        public string DEFAULT_DATABASE {
            get {
                return ((string)(this["DEFAULT_DATABASE"]));
            }
            set {
                this["DEFAULT_DATABASE"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string L_CLI_ID {
            get {
                return ((string)(this["L_CLI_ID"]));
            }
            set {
                this["L_CLI_ID"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string L_CLI_NAME {
            get {
                return ((string)(this["L_CLI_NAME"]));
            }
            set {
                this["L_CLI_NAME"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string SYS_PRINTER_NAME {
            get {
                return ((string)(this["SYS_PRINTER_NAME"]));
            }
            set {
                this["SYS_PRINTER_NAME"] = value;
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.ConnectionString)]
        [global::System.Configuration.DefaultSettingValueAttribute("Data Source=DESKTOP-TB02KDT;Initial Catalog=ClinicDataBase_2020;User ID=CLINIC_US" +
            "ER_2020;Password=19931993CLINIC2020")]
        public string ClinicDataBase_2020ConnectionString1 {
            get {
                return ((string)(this["ClinicDataBase_2020ConnectionString1"]));
            }
        }
    }
}
