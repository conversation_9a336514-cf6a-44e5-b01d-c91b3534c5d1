﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmAPOLIST_REPORT : DevExpress.XtraEditors.XtraForm
    {
        public frmAPOLIST_REPORT()
        {
            InitializeComponent();
        }
        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static string CUST_NAME;
        public static long CLI_ID;
        public static string DOC_NAME;
        public static string F_DATE;
        public static string S_DATE;

        private void frmAPOLIST_REPORT_Load(object sender, EventArgs e)
        {
            try
            {
                repAPOLIST_REPORT.Load(path + "\\REPORTS\\repAPOLIST_REPORT.frx");
                repAPOLIST_REPORT.SetParameterValue("DOC_NAME", DOC_NAME);
                repAPOLIST_REPORT.SetParameterValue("CUST_NAME", CUST_NAME);
                repAPOLIST_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repAPOLIST_REPORT.SetParameterValue("F_DATE", F_DATE);
                repAPOLIST_REPORT.SetParameterValue("S_DATE", S_DATE);
                repAPOLIST_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repAPOLIST_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repAPOLIST_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repAPOLIST_REPORT.PrintSettings.Printer = "";
                repAPOLIST_REPORT.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}