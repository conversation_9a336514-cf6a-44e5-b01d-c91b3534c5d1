﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmAPO_VIS_REPORT : DevExpress.XtraEditors.XtraForm
    {
        public frmAPO_VIS_REPORT()
        {
            InitializeComponent();
        }
        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static long CUST_ID;
        public static long CLI_ID;
        public static long VIS_ID;
        public static long APO_CODE;

        private void frmAPO_VIS_REPORT_Load(object sender, EventArgs e)
        {
            try
            {
                repAPO_VIS_REPORT.Load(path + "\\REPORTS\\repAPO_VIS_REPORT.frx");
                repAPO_VIS_REPORT.SetParameterValue("VIS_ID", VIS_ID);
                repAPO_VIS_REPORT.SetParameterValue("CUST_ID", CUST_ID);
                repAPO_VIS_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repAPO_VIS_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repAPO_VIS_REPORT.SetParameterValue("APO_CODE", APO_CODE);
                repAPO_VIS_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repAPO_VIS_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repAPO_VIS_REPORT.PrintSettings.Printer = "";
                //repAPO_VIS_REPORT.PrintSettings.ShowDialog = false;
                //repAPO_VIS_REPORT.Print();
                repAPO_VIS_REPORT.Show();
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }

        //public void Print_Rep()
        //{
        //    var result = MessageBox.Show("هل تريد طباعة الكرت" + "\n" + "طباعة Yes الغاء No", "طباعة الكرت", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
        //    if (result == DialogResult.Yes)
        //    {

        //        repAPO_VIS_REPORT.Load(path + "\\REPORTS\\repAPO_VIS_REPORT.frx");
        //        repAPO_VIS_REPORT.SetParameterValue("VIS_ID", VIS_ID);
        //        repAPO_VIS_REPORT.SetParameterValue("CUST_ID", CUST_ID);
        //        repAPO_VIS_REPORT.SetParameterValue("CLI_ID", CLI_ID);
        //        repAPO_VIS_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
        //        repAPO_VIS_REPORT.SetParameterValue("APO_CODE", APO_CODE);
        //        repAPO_VIS_REPORT.Preview = previewControl1;
        //        previewControl1.ZoomWholePage();
        //        repAPO_VIS_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
        //        repAPO_VIS_REPORT.PrintSettings.Printer = "";
        //        repAPO_VIS_REPORT.PrintSettings.ShowDialog = false;
        //        repAPO_VIS_REPORT.Print();
        //        this.Close();
        //    }
        //    else
        //    {
        //        path = Path.GetDirectoryName(Application.ExecutablePath);
        //        repAPO_VIS_REPORT.Load(path + "\\REPORTS\\repAPO_VIS_REPORT.frx");
        //        repAPO_VIS_REPORT.SetParameterValue("VIS_ID", VIS_ID);
        //        repAPO_VIS_REPORT.SetParameterValue("CUST_ID", CUST_ID);
        //        repAPO_VIS_REPORT.SetParameterValue("CLI_ID", CLI_ID);
        //        repAPO_VIS_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
        //        repAPO_VIS_REPORT.SetParameterValue("APO_CODE", APO_CODE);
        //        repAPO_VIS_REPORT.Preview = previewControl1;
        //        //prevControl1.ZoomWholePage();
        //        repAPO_VIS_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
        //        repAPO_VIS_REPORT.Show();
        //    }
        //}

        private void frmAPO_VIS_REPORT_Shown(object sender, EventArgs e)
        {
            //Print_Rep();
        }
    }
}