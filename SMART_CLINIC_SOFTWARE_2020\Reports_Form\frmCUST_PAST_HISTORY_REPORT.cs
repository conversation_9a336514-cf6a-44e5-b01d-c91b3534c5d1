﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;
using SMART_CLINIC_SOFTWARE_2020.Project_Form;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmCUST_PAST_HISTORY_REPORT : DevExpress.XtraEditors.XtraForm
    {
        public frmCUST_PAST_HISTORY_REPORT()
        {
            InitializeComponent();
        }
        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static long CUST_ID;
        public static long CLI_ID;
        Classes.clsCUST Nclscust = new Classes.clsCUST();

        private void frmCUST_PAST_HISTORY_REPORT_Load(object sender, EventArgs e)
        {
            try
            {
                Nclscust.Select_CUST(CUST_ID.ToString(), "");
                Nclscust.CalculateAge(Convert.ToDateTime(Classes.clsCUST.CUST_BD));
                Classes.clsCUST.CUST_DATATABLE.UpdateCUST_AGE(Classes.clsCUST.CUST_CODE, Classes.clsCUST.Years.ToString(), Classes.clsCUST.CLI_ID, Classes.clsCUST.Months.ToString(), Classes.clsCUST.CUST_ID);
                repCUST_PAST_HISTORY_REPORT.Load(path + "\\REPORTS\\repCUST_PAST_HISTORY_REPORT.frx");
                repCUST_PAST_HISTORY_REPORT.SetParameterValue("CUST_ID", CUST_ID);
                repCUST_PAST_HISTORY_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repCUST_PAST_HISTORY_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repCUST_PAST_HISTORY_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repCUST_PAST_HISTORY_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repCUST_PAST_HISTORY_REPORT.PrintSettings.Printer = "";
                repCUST_PAST_HISTORY_REPORT.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}