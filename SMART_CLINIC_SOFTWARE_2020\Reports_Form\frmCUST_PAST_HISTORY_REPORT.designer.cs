﻿namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    partial class frmCUST_PAST_HISTORY_REPORT
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmCUST_PAST_HISTORY_REPORT));
            this.repCUST_PAST_HISTORY_REPORT = new FastReport.Report();
            this.previewControl1 = new FastReport.Preview.PreviewControl();
            ((System.ComponentModel.ISupportInitialize)(this.repCUST_PAST_HISTORY_REPORT)).BeginInit();
            this.SuspendLayout();
            // 
            // repCUST_PAST_HISTORY_REPORT
            // 
            this.repCUST_PAST_HISTORY_REPORT.NeedRefresh = false;
            this.repCUST_PAST_HISTORY_REPORT.ReportResourceString = resources.GetString("repCUST_PAST_HISTORY_REPORT.ReportResourceString");
            // 
            // previewControl1
            // 
            this.previewControl1.BackColor = System.Drawing.SystemColors.AppWorkspace;
            this.previewControl1.Buttons = ((FastReport.PreviewButtons)((((((((((((FastReport.PreviewButtons.Print | FastReport.PreviewButtons.Open) 
            | FastReport.PreviewButtons.Save) 
            | FastReport.PreviewButtons.Email) 
            | FastReport.PreviewButtons.Find) 
            | FastReport.PreviewButtons.Zoom) 
            | FastReport.PreviewButtons.Outline) 
            | FastReport.PreviewButtons.PageSetup) 
            | FastReport.PreviewButtons.Watermark) 
            | FastReport.PreviewButtons.Navigator) 
            | FastReport.PreviewButtons.Close) 
            | FastReport.PreviewButtons.CopyPage)));
            this.previewControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.previewControl1.Font = new System.Drawing.Font("Tahoma", 8F);
            this.previewControl1.Location = new System.Drawing.Point(0, 0);
            this.previewControl1.Name = "previewControl1";
            this.previewControl1.PageOffset = new System.Drawing.Point(10, 10);
            this.previewControl1.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.previewControl1.SaveInitialDirectory = null;
            this.previewControl1.Size = new System.Drawing.Size(981, 502);
            this.previewControl1.TabIndex = 0;
            // 
            // frmCUST_PAST_HISTORY_REPORT
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(981, 502);
            this.Controls.Add(this.previewControl1);
            this.Name = "frmCUST_PAST_HISTORY_REPORT";
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "frmCUST_PAST_HISTORY_REPORT";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.Load += new System.EventHandler(this.frmCUST_PAST_HISTORY_REPORT_Load);
            ((System.ComponentModel.ISupportInitialize)(this.repCUST_PAST_HISTORY_REPORT)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private FastReport.Report repCUST_PAST_HISTORY_REPORT;
        private FastReport.Preview.PreviewControl previewControl1;
    }
}