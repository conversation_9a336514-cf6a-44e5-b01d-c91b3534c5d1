﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmMEDLIST_VIS_REPORT : DevExpress.XtraEditors.XtraForm
    {
        public frmMEDLIST_VIS_REPORT()
        {
            InitializeComponent();
        }

        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static long CUST_ID;
        public static long CLI_ID;
        public static long VIS_ID;
        public static long MEDLIST_CODE;

        private void frmMEDLIST_REPORT_Load(object sender, EventArgs e)
        {
            path = Path.GetDirectoryName(Application.ExecutablePath);
            repMEDLIST_VIS_REPORT.Load(path + "\\REPORTS\\repMEDLIST_VIS_REPORT.frx");
            repMEDLIST_VIS_REPORT.SetParameterValue("VIS_ID", VIS_ID);
            repMEDLIST_VIS_REPORT.SetParameterValue("CUST_ID", CUST_ID);
            repMEDLIST_VIS_REPORT.SetParameterValue("CLI_ID", CLI_ID);
            repMEDLIST_VIS_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
            repMEDLIST_VIS_REPORT.SetParameterValue("MEDLIST_CODE", MEDLIST_CODE);
            repMEDLIST_VIS_REPORT.Preview = previewControl1;
            previewControl1.ZoomWholePage();
            repMEDLIST_VIS_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
            repMEDLIST_VIS_REPORT.Show();
        }

        public void Print_Rep()
        {
            var result = MessageBox.Show("هل تريد طباعة الكرت" + "\n" + "طباعة Yes الغاء No", "طباعة الكرت", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {

                repMEDLIST_VIS_REPORT.Load(path + "\\REPORTS\\repMEDLIST_VIS_REPORT.frx");
                repMEDLIST_VIS_REPORT.SetParameterValue("VIS_ID", VIS_ID);
                repMEDLIST_VIS_REPORT.SetParameterValue("CUST_ID", CUST_ID);
                repMEDLIST_VIS_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repMEDLIST_VIS_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repMEDLIST_VIS_REPORT.SetParameterValue("MEDLIST_CODE", MEDLIST_CODE);
                repMEDLIST_VIS_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repMEDLIST_VIS_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repMEDLIST_VIS_REPORT.PrintSettings.Printer = "";
                repMEDLIST_VIS_REPORT.PrintSettings.ShowDialog = false;
                repMEDLIST_VIS_REPORT.Print();
                this.Close();
            }
            else
            {
                path = Path.GetDirectoryName(Application.ExecutablePath);
                repMEDLIST_VIS_REPORT.Load(path + "\\REPORTS\\repMEDLIST_VIS_REPORT.frx");
                repMEDLIST_VIS_REPORT.SetParameterValue("VIS_ID", VIS_ID);
                repMEDLIST_VIS_REPORT.SetParameterValue("CUST_ID", CUST_ID);
                repMEDLIST_VIS_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repMEDLIST_VIS_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repMEDLIST_VIS_REPORT.SetParameterValue("MEDLIST_CODE", MEDLIST_CODE);
                repMEDLIST_VIS_REPORT.Preview = previewControl1;
                //prevControl1.ZoomWholePage();
                repMEDLIST_VIS_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repMEDLIST_VIS_REPORT.Show();
            }
        }

        private void frmMEDLIST_VIS_REPORT_Shown(object sender, EventArgs e)
        {
            Print_Rep();
        }
    }
}