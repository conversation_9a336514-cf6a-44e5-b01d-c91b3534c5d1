﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmMEDREQ_REPORT : DevExpress.XtraEditors.XtraForm
    {
        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static long CUST_ID;
        public static long CLI_ID;

        public frmMEDREQ_REPORT()
        {
            InitializeComponent();
        }

        private void frmMEDREQ_REPORT_Load(object sender, EventArgs e)
        {
            path = Path.GetDirectoryName(Application.ExecutablePath);
            repMEDREQ.Load(path + "\\REPORTS\\repMEDREQ_REPORT.frx");
            repMEDREQ.SetParameterValue("CUST_ID", CUST_ID);
            repMEDREQ.SetParameterValue("CLI_ID", CLI_ID);
            repMEDREQ.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
            repMEDREQ.Preview = previewControl1;
            previewControl1.ZoomWholePage();
            repMEDREQ.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
            repMEDREQ.Show();
        }

        public void Print_Rep()
        {
            var result = MessageBox.Show("هل تريد طباعة الكرت" + "\n" + "طباعة Yes الغاء No", "طباعة الكرت", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {

                repMEDREQ.Load(path + "\\REPORTS\\repMEDREQ_REPORT.frx");
                repMEDREQ.SetParameterValue("CUST_ID", CUST_ID);
                repMEDREQ.SetParameterValue("CLI_ID", CLI_ID);
                repMEDREQ.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repMEDREQ.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repMEDREQ.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repMEDREQ.PrintSettings.Printer = "";
                repMEDREQ.PrintSettings.ShowDialog = false;
                repMEDREQ.Print();
                this.Close();
            }
            else
            {
                path = Path.GetDirectoryName(Application.ExecutablePath);
                repMEDREQ.Load(path + "\\REPORTS\\repMEDREQ_REPORT.frx");
                repMEDREQ.SetParameterValue("CUST_ID", CUST_ID);
                repMEDREQ.SetParameterValue("CLI_ID", CLI_ID);
                repMEDREQ.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repMEDREQ.Preview = previewControl1;
                //prevControl1.ZoomWholePage();
                repMEDREQ.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repMEDREQ.Show();
            }
        }

        private void frmMEDREQ_REPORT_Shown(object sender, EventArgs e)
        {
            Print_Rep();
        }
    }
}