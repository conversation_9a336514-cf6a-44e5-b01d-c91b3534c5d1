﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmMEDREQ_VIS_REPORT : DevExpress.XtraEditors.XtraForm
    {
        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static long CUST_ID;
        public static long CLI_ID;
        public static long VIS_ID;
        public static long MEDREQ_CODE;

        public frmMEDREQ_VIS_REPORT()
        {
            InitializeComponent();
        }

        private void frmMEDREQ_VIS_REPORT_Load(object sender, EventArgs e)
        {
            path = Path.GetDirectoryName(Application.ExecutablePath);
            repMEDREQ_VIS_REPORT.Load(path + "\\REPORTS\\repMEDREQ_VIS_REPORT.frx");
            repMEDREQ_VIS_REPORT.SetParameterValue("VIS_ID", VIS_ID);
            repMEDREQ_VIS_REPORT.SetParameterValue("CUST_ID", CUST_ID);
            repMEDREQ_VIS_REPORT.SetParameterValue("CLI_ID", CLI_ID);
            repMEDREQ_VIS_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
            repMEDREQ_VIS_REPORT.SetParameterValue("MEDREQ_CODE", MEDREQ_CODE);
            repMEDREQ_VIS_REPORT.Preview = previewControl1;
            previewControl1.ZoomWholePage();
            repMEDREQ_VIS_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
            repMEDREQ_VIS_REPORT.Show();
        }

        public void Print_Rep()
        {
            var result = MessageBox.Show("هل تريد طباعة الكرت" + "\n" + "طباعة Yes الغاء No", "طباعة الكرت", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {

                repMEDREQ_VIS_REPORT.Load(path + "\\REPORTS\\repMEDREQ_VIS_REPORT.frx");
                repMEDREQ_VIS_REPORT.SetParameterValue("VIS_ID", VIS_ID);
                repMEDREQ_VIS_REPORT.SetParameterValue("CUST_ID", CUST_ID);
                repMEDREQ_VIS_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repMEDREQ_VIS_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repMEDREQ_VIS_REPORT.SetParameterValue("MEDREQ_CODE", MEDREQ_CODE);
                repMEDREQ_VIS_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repMEDREQ_VIS_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repMEDREQ_VIS_REPORT.PrintSettings.Printer = "";
                repMEDREQ_VIS_REPORT.PrintSettings.ShowDialog = false;
                repMEDREQ_VIS_REPORT.Print();
                this.Close();
            }
            else
            {
                path = Path.GetDirectoryName(Application.ExecutablePath);
                repMEDREQ_VIS_REPORT.Load(path + "\\REPORTS\\repMEDREQ_VIS_REPORT.frx");
                repMEDREQ_VIS_REPORT.SetParameterValue("VIS_ID", VIS_ID);
                repMEDREQ_VIS_REPORT.SetParameterValue("CUST_ID", CUST_ID);
                repMEDREQ_VIS_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repMEDREQ_VIS_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repMEDREQ_VIS_REPORT.SetParameterValue("MEDREQ_CODE", MEDREQ_CODE);
                repMEDREQ_VIS_REPORT.Preview = previewControl1;
                //prevControl1.ZoomWholePage();
                repMEDREQ_VIS_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repMEDREQ_VIS_REPORT.Show();
            }
        }

        private void frmMEDREQ_VIS_REPORT_Shown(object sender, EventArgs e)
        {
            Print_Rep();
        }
    }
}