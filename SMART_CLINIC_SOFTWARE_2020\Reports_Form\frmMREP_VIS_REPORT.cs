﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmMREP_VIS_REPORT : DevExpress.XtraEditors.XtraForm
    {
        public frmMREP_VIS_REPORT()
        {
            InitializeComponent();
        }
        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static long CUST_ID;
        public static long CLI_ID;
        public static long MREP_CODE;
        public static string MREP_DATE;

        private void frmMREP_VIS_REPORT_Load(object sender, EventArgs e)
        {
            try
            {
                repMREP_VIS_REPORT.Load(path + "\\REPORTS\\repMEDREPORT_VIS_REPORT.frx");
                repMREP_VIS_REPORT.SetParameterValue("MREP_CODE", MREP_CODE);
                repMREP_VIS_REPORT.SetParameterValue("CUST_ID", CUST_ID);
                repMREP_VIS_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repMREP_VIS_REPORT.SetParameterValue("MREP_DATE", MREP_DATE);
                repMREP_VIS_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repMREP_VIS_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repMREP_VIS_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repMREP_VIS_REPORT.PrintSettings.Printer = "";
                //repMREP_VIS_REPORT.PrintSettings.ShowDialog = false;
                //repMREP_VIS_REPORT.Print();
                repMREP_VIS_REPORT.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}