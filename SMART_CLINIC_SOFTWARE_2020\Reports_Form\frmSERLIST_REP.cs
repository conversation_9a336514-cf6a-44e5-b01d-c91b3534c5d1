﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmSERLIST_REP : DevExpress.XtraEditors.XtraForm
    {
        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static long CUST_ID;
        public static long CLI_ID;

        public frmSERLIST_REP()
        {
            InitializeComponent();
        }
        

        private void frmSERLIST_REP_Load(object sender, EventArgs e)
        {
            //path = Path.GetDirectoryName(Application.ExecutablePath);
            repSERLIST.Load(path + "\\REPORTS\\repSERLIST_REPORT.frx");
            repSERLIST.SetParameterValue("CUST_ID", CUST_ID);
            repSERLIST.SetParameterValue("CLI_ID", CLI_ID);
            repSERLIST.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
            repSERLIST.Preview = previewControl1;
            previewControl1.ZoomWholePage();
            repSERLIST.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
            repSERLIST.Show();
        }

        public void Print_Rep()
        {
            var result = MessageBox.Show("هل تريد طباعة الكرت" + "\n" + "طباعة Yes الغاء No", "طباعة الكرت", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {

                repSERLIST.Load(path + "\\REPORTS\\repSERLIST_REPORT.frx");
                repSERLIST.SetParameterValue("CUST_ID", CUST_ID);
                repSERLIST.SetParameterValue("CLI_ID", CLI_ID);
                repSERLIST.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repSERLIST.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repSERLIST.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repSERLIST.PrintSettings.Printer = "";
                repSERLIST.PrintSettings.ShowDialog = false;
                repSERLIST.Print();
                this.Close();
            }
            else
            {
                //path = Path.GetDirectoryName(Application.ExecutablePath);
                repSERLIST.Load(path + "\\REPORTS\\repSERLIST_REPORT.frx");
                repSERLIST.SetParameterValue("CUST_ID", CUST_ID);
                repSERLIST.SetParameterValue("CLI_ID", CLI_ID);
                repSERLIST.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repSERLIST.Preview = previewControl1;
                repSERLIST.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repSERLIST.Show();
            }
        }

        private void frmSERLIST_REP_Shown(object sender, EventArgs e)
        {
            Print_Rep();
        }
    }
}