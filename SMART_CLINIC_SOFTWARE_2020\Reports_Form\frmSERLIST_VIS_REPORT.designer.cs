﻿namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    partial class frmSERLIST_VIS_REPORT
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmSERLIST_VIS_REPORT));
            this.repSERLIST_VIS_REPORT = new FastReport.Report();
            this.prevControl1 = new FastReport.Preview.PreviewControl();
            ((System.ComponentModel.ISupportInitialize)(this.repSERLIST_VIS_REPORT)).BeginInit();
            this.SuspendLayout();
            // 
            // repSERLIST_VIS_REPORT
            // 
            this.repSERLIST_VIS_REPORT.NeedRefresh = false;
            this.repSERLIST_VIS_REPORT.ReportResourceString = resources.GetString("repSERLIST_VIS_REPORT.ReportResourceString");
            // 
            // prevControl1
            // 
            this.prevControl1.BackColor = System.Drawing.SystemColors.AppWorkspace;
            this.prevControl1.Buttons = ((FastReport.PreviewButtons)(((((((((((FastReport.PreviewButtons.Print | FastReport.PreviewButtons.Save) 
            | FastReport.PreviewButtons.Email) 
            | FastReport.PreviewButtons.Find) 
            | FastReport.PreviewButtons.Zoom) 
            | FastReport.PreviewButtons.Outline) 
            | FastReport.PreviewButtons.PageSetup) 
            | FastReport.PreviewButtons.Watermark) 
            | FastReport.PreviewButtons.Navigator) 
            | FastReport.PreviewButtons.Close) 
            | FastReport.PreviewButtons.CopyPage)));
            this.prevControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.prevControl1.Font = new System.Drawing.Font("Tahoma", 8F);
            this.prevControl1.Location = new System.Drawing.Point(0, 0);
            this.prevControl1.Name = "prevControl1";
            this.prevControl1.PageOffset = new System.Drawing.Point(10, 10);
            this.prevControl1.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.prevControl1.SaveInitialDirectory = null;
            this.prevControl1.Size = new System.Drawing.Size(981, 502);
            this.prevControl1.TabIndex = 0;
            // 
            // frmSERLIST_VIS_REPORT
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(981, 502);
            this.Controls.Add(this.prevControl1);
            this.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "frmSERLIST_VIS_REPORT";
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "frmSERLIST_VIS_REPORT";
            this.Load += new System.EventHandler(this.frmSERLIST_VIS_REPORT_Load);
            this.Shown += new System.EventHandler(this.frmSERLIST_VIS_REPORT_Shown);
            ((System.ComponentModel.ISupportInitialize)(this.repSERLIST_VIS_REPORT)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private FastReport.Report repSERLIST_VIS_REPORT;
        public FastReport.Preview.PreviewControl prevControl1;
    }
}