﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmTRANSACTION_LIST_DATA_REPORT : DevExpress.XtraEditors.XtraForm
    {
        public frmTRANSACTION_LIST_DATA_REPORT()
        {
            InitializeComponent();
        }
        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static string CUST_NAME;
        public static string CLI_NAME;
        public static string COM_NAME;
        public static DateTime F_DATE;
        public static DateTime S_DATE;
        public static string VIS_ID;
        public static string T_ID;
        public static string T_TYPE;
        private void frmTRANSACTION_LIST_DATA_REPORT_Load(object sender, EventArgs e)
        {
            try
            {
                repTRANSACTION_LIST_DATA_REPORT.Load(path + "\\REPORTS\\repTRANSACTION_LIST_DATA_REPORT.frx");
                repTRANSACTION_LIST_DATA_REPORT.SetParameterValue("CUST_NAME", CUST_NAME);
                repTRANSACTION_LIST_DATA_REPORT.SetParameterValue("CLI_NAME", CLI_NAME);
                repTRANSACTION_LIST_DATA_REPORT.SetParameterValue("COM_NAME", COM_NAME);
                repTRANSACTION_LIST_DATA_REPORT.SetParameterValue("F_DATE", F_DATE.ToString("MM/dd/yyyy"));
                repTRANSACTION_LIST_DATA_REPORT.SetParameterValue("S_DATE", S_DATE.ToString("MM/dd/yyyy"));
                repTRANSACTION_LIST_DATA_REPORT.SetParameterValue("VIS_ID", VIS_ID);
                repTRANSACTION_LIST_DATA_REPORT.SetParameterValue("T_ID", T_ID);
                repTRANSACTION_LIST_DATA_REPORT.SetParameterValue("T_TYPE", T_TYPE);
                repTRANSACTION_LIST_DATA_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repTRANSACTION_LIST_DATA_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repTRANSACTION_LIST_DATA_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repTRANSACTION_LIST_DATA_REPORT.PrintSettings.Printer = "";
                repTRANSACTION_LIST_DATA_REPORT.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}