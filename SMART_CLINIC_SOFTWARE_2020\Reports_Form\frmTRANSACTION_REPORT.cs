﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmTRANSACTION_REPORT : DevExpress.XtraEditors.XtraForm
    {
        public frmTRANSACTION_REPORT()
        {
            InitializeComponent();
        }

        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static long T_CODE;
        public static long CLI_ID;
        private void frmTRANSACTION_REPORT_Load(object sender, EventArgs e)
        {
            try
            {
                repTRANSACTION_REPORT.Load(path + "\\REPORTS\\repTRANSACTION_REPORT.frx");
                repTRANSACTION_REPORT.SetParameterValue("T_CODE", T_CODE);
                repTRANSACTION_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repTRANSACTION_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repTRANSACTION_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repTRANSACTION_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repTRANSACTION_REPORT.PrintSettings.Printer = "";
                repTRANSACTION_REPORT.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        public void Print_Rep()
        {
            var result = MessageBox.Show("هل تريد طباعة الكرت" + "\n" + "طباعة Yes الغاء No", "طباعة الكرت", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {

                repTRANSACTION_REPORT.Load(path + "\\REPORTS\\repTRANSACTION_REPORT.frx");
                repTRANSACTION_REPORT.SetParameterValue("T_CODE", T_CODE);
                repTRANSACTION_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repTRANSACTION_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repTRANSACTION_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repTRANSACTION_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repTRANSACTION_REPORT.PrintSettings.Printer = "";
                repTRANSACTION_REPORT.PrintSettings.ShowDialog = false;
                repTRANSACTION_REPORT.Print();
                this.Close();
            }
            else
            {
                path = Path.GetDirectoryName(Application.ExecutablePath);
                repTRANSACTION_REPORT.Load(path + "\\REPORTS\\repTRANSACTION_REPORT.frx");
                repTRANSACTION_REPORT.SetParameterValue("T_CODE", T_CODE);
                repTRANSACTION_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repTRANSACTION_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repTRANSACTION_REPORT.Preview = previewControl1;
                repTRANSACTION_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repTRANSACTION_REPORT.Show();
            }
        }

        private void frmTRANSACTION_REPORT_Shown(object sender, EventArgs e)
        {
            Print_Rep();
        }
    }
}