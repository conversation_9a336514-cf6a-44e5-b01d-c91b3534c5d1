﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmVISLIST_REPORT : DevExpress.XtraEditors.XtraForm
    {
        public frmVISLIST_REPORT()
        {
            InitializeComponent();
        }
        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static string CUST_NAME;
        public static string CLI_NAME;
        public static string DOC_NAME;
        public static DateTime F_DATE;
        public static DateTime S_DATE;
        public static string VIS_ID;
        private void frmVISLIST_REPORT_Load(object sender, EventArgs e)
        {
            try
            {
                repVISLIST_REPORT.Load(path + "\\REPORTS\\repVISLIST_REPORT.frx");
                repVISLIST_REPORT.SetParameterValue("CUST_NAME", CUST_NAME);
                repVISLIST_REPORT.SetParameterValue("CLI_NAME", CLI_NAME);
                repVISLIST_REPORT.SetParameterValue("DOC_NAME", DOC_NAME);
                repVISLIST_REPORT.SetParameterValue("F_DATE", F_DATE.ToString("MM/dd/yyyy"));
                repVISLIST_REPORT.SetParameterValue("S_DATE", S_DATE.ToString("MM/dd/yyyy"));
                repVISLIST_REPORT.SetParameterValue("VIS_ID", VIS_ID);
                repVISLIST_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repVISLIST_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repVISLIST_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repVISLIST_REPORT.PrintSettings.Printer = "";
                repVISLIST_REPORT.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
       
    }
}