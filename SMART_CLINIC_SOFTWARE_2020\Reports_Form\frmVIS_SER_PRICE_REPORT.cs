﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.IO;

namespace SMART_CLINIC_SOFTWARE_2020.Reports_Form
{
    public partial class frmVIS_SER_PRICE_REPORT : DevExpress.XtraEditors.XtraForm
    {
        public frmVIS_SER_PRICE_REPORT()
        {
            InitializeComponent();
        }
        string path = Path.GetDirectoryName(Application.ExecutablePath);
        public static long CUST_ID;
        public static long CLI_ID;
        public static long VIS_ID;

        private void frmVIS_SER_PRICE_REPORT_Load(object sender, EventArgs e)
        {
            try
            {
                repVIS_SER_PRICE_REPORT.Load(path + "\\REPORTS\\repVIS_SER_PRICE_REPORT.frx");
                repVIS_SER_PRICE_REPORT.SetParameterValue("CUST_ID", CUST_ID);
                repVIS_SER_PRICE_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repVIS_SER_PRICE_REPORT.SetParameterValue("VIS_ID", VIS_ID);
                repVIS_SER_PRICE_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repVIS_SER_PRICE_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repVIS_SER_PRICE_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repVIS_SER_PRICE_REPORT.PrintSettings.Printer = "";
                repVIS_SER_PRICE_REPORT.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        public void Print_Rep()
        {
            var result = MessageBox.Show("هل تريد طباعة الكرت" + "\n" + "طباعة Yes الغاء No", "طباعة الكرت", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {

                repVIS_SER_PRICE_REPORT.Load(path + "\\REPORTS\\repVIS_SER_PRICE_REPORT.frx");
                repVIS_SER_PRICE_REPORT.SetParameterValue("CUST_ID", CUST_ID);
                repVIS_SER_PRICE_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repVIS_SER_PRICE_REPORT.SetParameterValue("VIS_ID", VIS_ID);
                repVIS_SER_PRICE_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repVIS_SER_PRICE_REPORT.Preview = previewControl1;
                previewControl1.ZoomWholePage();
                repVIS_SER_PRICE_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repVIS_SER_PRICE_REPORT.PrintSettings.Printer = "";
                repVIS_SER_PRICE_REPORT.PrintSettings.ShowDialog = false;
                repVIS_SER_PRICE_REPORT.Print();
                this.Close();
            }
            else
            {
                path = Path.GetDirectoryName(Application.ExecutablePath);
                repVIS_SER_PRICE_REPORT.Load(path + "\\REPORTS\\repVIS_SER_PRICE_REPORT.frx");
                repVIS_SER_PRICE_REPORT.SetParameterValue("CUST_ID", CUST_ID);
                repVIS_SER_PRICE_REPORT.SetParameterValue("CLI_ID", CLI_ID);
                repVIS_SER_PRICE_REPORT.SetParameterValue("VIS_ID", VIS_ID);
                repVIS_SER_PRICE_REPORT.SetParameterValue("IMG_LOC", path + "\\logo.jpg");
                repVIS_SER_PRICE_REPORT.Preview = previewControl1;
                //prevControl1.ZoomWholePage();
                repVIS_SER_PRICE_REPORT.Dictionary.Connections[0].ConnectionString = SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.ClinicDataBase_2020ConnectionString;
                repVIS_SER_PRICE_REPORT.Show();
            }
        }

        private void frmVIS_SER_PRICE_REPORT_Shown(object sender, EventArgs e)
        {
            Print_Rep();
        }
    }
}