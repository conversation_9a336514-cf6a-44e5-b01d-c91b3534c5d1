﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{4476ACC0-8C9D-4D19-A4B8-59CB418C8E82}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SMART_CLINIC_SOFTWARE_2020</RootNamespace>
    <AssemblyName>SMART_CLINIC_SOFTWARE_2020</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.BonusSkins.v21.2" />
    <Reference Include="DevExpress.Data.Desktop.v21.2" />
    <Reference Include="DevExpress.Data.v21.2" />
    <Reference Include="DevExpress.Images.v21.2, Version=21.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v21.2" />
    <Reference Include="DevExpress.Sparkline.v21.2.Core" />
    <Reference Include="DevExpress.XtraBars.v21.2, Version=21.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraEditors.v21.2" />
    <Reference Include="DevExpress.Printing.v21.2.Core" />
    <Reference Include="DevExpress.XtraGrid.v21.2, Version=21.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraLayout.v21.2, Version=21.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraPrinting.v21.2, Version=21.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="eCryptography, Version=1.0.0.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Eramake.eCryptography.1.1.6\lib\net45\eCryptography.dll</HintPath>
    </Reference>
    <Reference Include="FastReport, Version=2018.4.7.0, Culture=neutral, processorArchitecture=MSIL" />
    <Reference Include="FastReport.Bars, Version=2018.4.7.0, Culture=neutral, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.SqlServer.ConnectionInfo, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\..\Program Files\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.ConnectionInfo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.Sdk.Sfc, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\..\Program Files\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.Management.Sdk.Sfc.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Smo, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\..\Program Files\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.Smo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.IO.Log" />
    <Reference Include="System.Management" />
    <Reference Include="System.Management.Instrumentation" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Classes\clsAPO.cs" />
    <Compile Include="Classes\clsCARD.cs" />
    <Compile Include="Classes\clsCLINIC.cs" />
    <Compile Include="Classes\clsCLINIC_TITLE.cs" />
    <Compile Include="Classes\clsCOMPANY.cs" />
    <Compile Include="Classes\clsCUST.cs" />
    <Compile Include="Classes\clsDATABASE_CONNECTION.cs" />
    <Compile Include="Classes\clsDES.cs" />
    <Compile Include="Classes\clsDIAGNOIS.cs" />
    <Compile Include="Classes\clsDIGLIST.cs" />
    <Compile Include="Classes\clsDOCTORS.cs" />
    <Compile Include="Classes\clsDOS.cs" />
    <Compile Include="Classes\clsENDO.cs" />
    <Compile Include="Classes\clsHOLIDAY.cs" />
    <Compile Include="Classes\clsMEDCHEK.cs" />
    <Compile Include="Classes\clsMEDCIN.cs" />
    <Compile Include="Classes\clsMEDLIST.cs" />
    <Compile Include="Classes\clsMEDREP.cs" />
    <Compile Include="Classes\clsMEDREQ.cs" />
    <Compile Include="Classes\clsORDER.cs" />
    <Compile Include="Classes\clsSERLIST.cs" />
    <Compile Include="Classes\clsSERVICE.cs" />
    <Compile Include="Classes\clsStock_AddMoney.cs" />
    <Compile Include="Classes\clsSTOCK_DATA.cs" />
    <Compile Include="Classes\clsStock_PullMoney.cs" />
    <Compile Include="Classes\clsTRANSACTION.cs" />
    <Compile Include="Classes\clsTRANS_VIS_DATA.cs" />
    <Compile Include="Classes\clsUSERS.cs" />
    <Compile Include="Classes\clsUSER_PER.cs" />
    <Compile Include="Classes\clsUSER_TYPE.cs" />
    <Compile Include="Classes\clsVISIT.cs" />
    <Compile Include="CLINIC_DATASET.cs">
      <DependentUpon>CLINIC_DATASET.xsd</DependentUpon>
    </Compile>
    <Compile Include="CLINIC_DATASET.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CLINIC_DATASET.xsd</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmAPO_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmAPO_LIST.designer.cs">
      <DependentUpon>frmAPO_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmCARD_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmCARD_LIST.designer.cs">
      <DependentUpon>frmCARD_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmCLINIC_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmCLINIC_LIST.designer.cs">
      <DependentUpon>frmCLINIC_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmCOM_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmCOM_LIST.designer.cs">
      <DependentUpon>frmCOM_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmCUST_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmCUST_LIST.designer.cs">
      <DependentUpon>frmCUST_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmDIG_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmDIG_LIST.designer.cs">
      <DependentUpon>frmDIG_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmDOC_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmDOC_LIST.designer.cs">
      <DependentUpon>frmDOC_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmMEDCHEK_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmMEDCHEK_LIST.designer.cs">
      <DependentUpon>frmMEDCHEK_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmMEDCIN_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmMEDCIN_LIST.designer.cs">
      <DependentUpon>frmMEDCIN_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmSERVICE_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmSERVICE_LIST.designer.cs">
      <DependentUpon>frmSERVICE_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmTRANSACTION_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmTRANSACTION_LIST.designer.cs">
      <DependentUpon>frmTRANSACTION_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="LIST_FORM\frmVIS_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LIST_FORM\frmVIS_LIST.designer.cs">
      <DependentUpon>frmVIS_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmAPP_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmAPP_MAIN.designer.cs">
      <DependentUpon>frmAPP_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmCLINIC_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmCLINIC_MAIN.designer.cs">
      <DependentUpon>frmCLINIC_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmCOMPANY_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmCOMPANY_MAIN.designer.cs">
      <DependentUpon>frmCOMPANY_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmCUST_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmCUST_MAIN.designer.cs">
      <DependentUpon>frmCUST_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmDIAGNOIS_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmDIAGNOIS_MAIN.designer.cs">
      <DependentUpon>frmDIAGNOIS_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmDOCTOR_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmDOCTOR_MAIN.designer.cs">
      <DependentUpon>frmDOCTOR_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmHOLEDAY_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmHOLEDAY_MAIN.designer.cs">
      <DependentUpon>frmHOLEDAY_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmMEDCHECK_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmMEDCHECK_MAIN.designer.cs">
      <DependentUpon>frmMEDCHECK_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmMEDCIN_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmMEDCIN_MAIN.designer.cs">
      <DependentUpon>frmMEDCIN_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmREPORT_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmREPORT_MAIN.designer.cs">
      <DependentUpon>frmREPORT_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmRES_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmRES_MAIN.designer.cs">
      <DependentUpon>frmRES_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmSAVE_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmSAVE_MAIN.designer.cs">
      <DependentUpon>frmSAVE_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmSERVICE_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmSERVICE_MAIN.designer.cs">
      <DependentUpon>frmSERVICE_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmSTOCK_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmSTOCK_MAIN.Designer.cs">
      <DependentUpon>frmSTOCK_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmTRANS_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmTRANS_MAIN.designer.cs">
      <DependentUpon>frmTRANS_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmUSER_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmUSER_MAIN.designer.cs">
      <DependentUpon>frmUSER_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Main_Form\frmVIS_MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main_Form\frmVIS_MAIN.designer.cs">
      <DependentUpon>frmVIS_MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frmDES.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frmDES.designer.cs">
      <DependentUpon>frmDES.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frmDIAG_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frmDIAG_LIST.designer.cs">
      <DependentUpon>frmDIAG_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frmMEDREQ.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frmMEDREQ.designer.cs">
      <DependentUpon>frmMEDREQ.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frmMEDREQ_RESULT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frmMEDREQ_RESULT.designer.cs">
      <DependentUpon>frmMEDREQ_RESULT.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frmMED_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frmMED_LIST.designer.cs">
      <DependentUpon>frmMED_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frmOLD_DIAG_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frmOLD_DIAG_LIST.designer.cs">
      <DependentUpon>frmOLD_DIAG_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frmOLD_MEDREP.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frmOLD_MEDREP.designer.cs">
      <DependentUpon>frmOLD_MEDREP.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frmOLD_MEDREQ_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frmOLD_MEDREQ_LIST.designer.cs">
      <DependentUpon>frmOLD_MEDREQ_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frmOLD_MED_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frmOLD_MED_LIST.designer.cs">
      <DependentUpon>frmOLD_MED_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frmOLD_SER_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frmOLD_SER_LIST.designer.cs">
      <DependentUpon>frmOLD_SER_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frmSER_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frmSER_LIST.designer.cs">
      <DependentUpon>frmSER_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="MED_FORM\frm_ENDO.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MED_FORM\frm_ENDO.Designer.cs">
      <DependentUpon>frm_ENDO.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Project Form\frmAPO.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmAPO.designer.cs">
      <DependentUpon>frmAPO.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmCARD.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmCARD.designer.cs">
      <DependentUpon>frmCARD.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmCLINIC.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmCLINIC.designer.cs">
      <DependentUpon>frmCLINIC.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmCLINIC_PANEL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmCLINIC_PANEL.designer.cs">
      <DependentUpon>frmCLINIC_PANEL.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmCOMPANY.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmCOMPANY.designer.cs">
      <DependentUpon>frmCOMPANY.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmCUST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmCUST.designer.cs">
      <DependentUpon>frmCUST.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmCUST_RES.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmCUST_RES.designer.cs">
      <DependentUpon>frmCUST_RES.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmDATABASE_CONFIG.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmDATABASE_CONFIG.designer.cs">
      <DependentUpon>frmDATABASE_CONFIG.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmDIAGNOIS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmDIAGNOIS.designer.cs">
      <DependentUpon>frmDIAGNOIS.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmDOCTORS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmDOCTORS.designer.cs">
      <DependentUpon>frmDOCTORS.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmDOS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmDOS.designer.cs">
      <DependentUpon>frmDOS.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmHOLIDAY.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmHOLIDAY.designer.cs">
      <DependentUpon>frmHOLIDAY.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmLOGIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmLOGIN.designer.cs">
      <DependentUpon>frmLOGIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmMAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmMAIN.designer.cs">
      <DependentUpon>frmMAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmMEDCHEK.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmMEDCHEK.designer.cs">
      <DependentUpon>frmMEDCHEK.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmMEDCIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmMEDCIN.designer.cs">
      <DependentUpon>frmMEDCIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmMEDREP.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmMEDREP.designer.cs">
      <DependentUpon>frmMEDREP.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmSERVICE.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmSERVICE.designer.cs">
      <DependentUpon>frmSERVICE.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmSETTING.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmSETTING.designer.cs">
      <DependentUpon>frmSETTING.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmTRANSACTION.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmTRANSACTION.designer.cs">
      <DependentUpon>frmTRANSACTION.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmVISIT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmVISIT.designer.cs">
      <DependentUpon>frmVISIT.cs</DependentUpon>
    </Compile>
    <Compile Include="Project Form\frmVISIT_EDIT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project Form\frmVISIT_EDIT.designer.cs">
      <DependentUpon>frmVISIT_EDIT.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmAPOLIST_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmAPOLIST_REPORT.designer.cs">
      <DependentUpon>frmAPOLIST_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmAPO_VIS_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmAPO_VIS_REPORT.designer.cs">
      <DependentUpon>frmAPO_VIS_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmCUST_PAST_HISTORY_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmCUST_PAST_HISTORY_REPORT.designer.cs">
      <DependentUpon>frmCUST_PAST_HISTORY_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmMEDLIST_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmMEDLIST_REPORT.designer.cs">
      <DependentUpon>frmMEDLIST_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmMEDLIST_VIS_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmMEDLIST_VIS_REPORT.designer.cs">
      <DependentUpon>frmMEDLIST_VIS_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmMEDREQ_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmMEDREQ_REPORT.designer.cs">
      <DependentUpon>frmMEDREQ_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmMEDREQ_VIS_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmMEDREQ_VIS_REPORT.designer.cs">
      <DependentUpon>frmMEDREQ_VIS_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmMREP_VIS_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmMREP_VIS_REPORT.designer.cs">
      <DependentUpon>frmMREP_VIS_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmSERLIST_REP.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmSERLIST_REP.designer.cs">
      <DependentUpon>frmSERLIST_REP.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmSERLIST_VIS_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmSERLIST_VIS_REPORT.designer.cs">
      <DependentUpon>frmSERLIST_VIS_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmTRANSACTION_LIST_DATA_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmTRANSACTION_LIST_DATA_REPORT.designer.cs">
      <DependentUpon>frmTRANSACTION_LIST_DATA_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmTRANSACTION_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmTRANSACTION_REPORT.designer.cs">
      <DependentUpon>frmTRANSACTION_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmVISLIST_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmVISLIST_REPORT.designer.cs">
      <DependentUpon>frmVISLIST_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports_Form\frmVIS_SER_PRICE_REPORT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports_Form\frmVIS_SER_PRICE_REPORT.designer.cs">
      <DependentUpon>frmVIS_SER_PRICE_REPORT.cs</DependentUpon>
    </Compile>
    <Compile Include="STOCK_DATA\frmSTOCK.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="STOCK_DATA\frmSTOCK.Designer.cs">
      <DependentUpon>frmSTOCK.cs</DependentUpon>
    </Compile>
    <Compile Include="STOCK_DATA\frmStock_AddMoney.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="STOCK_DATA\frmStock_AddMoney.Designer.cs">
      <DependentUpon>frmStock_AddMoney.cs</DependentUpon>
    </Compile>
    <Compile Include="STOCK_DATA\frm_Stock_AddMoney_List.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="STOCK_DATA\frm_Stock_AddMoney_List.Designer.cs">
      <DependentUpon>frm_Stock_AddMoney_List.cs</DependentUpon>
    </Compile>
    <Compile Include="STOCK_DATA\Frm_Stock_PullMoney.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="STOCK_DATA\Frm_Stock_PullMoney.Designer.cs">
      <DependentUpon>Frm_Stock_PullMoney.cs</DependentUpon>
    </Compile>
    <Compile Include="STOCK_DATA\Frm_Stock_PullMoney_List.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="STOCK_DATA\Frm_Stock_PullMoney_List.Designer.cs">
      <DependentUpon>Frm_Stock_PullMoney_List.cs</DependentUpon>
    </Compile>
    <Compile Include="User_Form\frmUSERS_DATA.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="User_Form\frmUSERS_DATA.designer.cs">
      <DependentUpon>frmUSERS_DATA.cs</DependentUpon>
    </Compile>
    <Compile Include="User_Form\frmUSER_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="User_Form\frmUSER_LIST.designer.cs">
      <DependentUpon>frmUSER_LIST.cs</DependentUpon>
    </Compile>
    <Compile Include="User_Form\frmUSER_PER.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="User_Form\frmUSER_PER.designer.cs">
      <DependentUpon>frmUSER_PER.cs</DependentUpon>
    </Compile>
    <Compile Include="User_Form\frmUSER_TYPE.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="User_Form\frmUSER_TYPE.designer.cs">
      <DependentUpon>frmUSER_TYPE.cs</DependentUpon>
    </Compile>
    <Compile Include="User_Form\frmUSER_TYPE_LIST.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="User_Form\frmUSER_TYPE_LIST.designer.cs">
      <DependentUpon>frmUSER_TYPE_LIST.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="LIST_FORM\frmAPO_LIST.resx">
      <DependentUpon>frmAPO_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LIST_FORM\frmCARD_LIST.resx">
      <DependentUpon>frmCARD_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LIST_FORM\frmCLINIC_LIST.resx">
      <DependentUpon>frmCLINIC_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LIST_FORM\frmCOM_LIST.resx">
      <DependentUpon>frmCOM_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LIST_FORM\frmCUST_LIST.resx">
      <DependentUpon>frmCUST_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LIST_FORM\frmDIG_LIST.resx">
      <DependentUpon>frmDIG_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LIST_FORM\frmDOC_LIST.resx">
      <DependentUpon>frmDOC_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LIST_FORM\frmMEDCHEK_LIST.resx">
      <DependentUpon>frmMEDCHEK_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LIST_FORM\frmMEDCIN_LIST.resx">
      <DependentUpon>frmMEDCIN_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LIST_FORM\frmSERVICE_LIST.resx">
      <DependentUpon>frmSERVICE_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LIST_FORM\frmTRANSACTION_LIST.resx">
      <DependentUpon>frmTRANSACTION_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LIST_FORM\frmVIS_LIST.resx">
      <DependentUpon>frmVIS_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmAPP_MAIN.resx">
      <DependentUpon>frmAPP_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmCLINIC_MAIN.resx">
      <DependentUpon>frmCLINIC_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmCOMPANY_MAIN.resx">
      <DependentUpon>frmCOMPANY_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmCUST_MAIN.resx">
      <DependentUpon>frmCUST_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmDIAGNOIS_MAIN.resx">
      <DependentUpon>frmDIAGNOIS_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmDOCTOR_MAIN.resx">
      <DependentUpon>frmDOCTOR_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmHOLEDAY_MAIN.resx">
      <DependentUpon>frmHOLEDAY_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmMEDCHECK_MAIN.resx">
      <DependentUpon>frmMEDCHECK_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmMEDCIN_MAIN.resx">
      <DependentUpon>frmMEDCIN_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmREPORT_MAIN.resx">
      <DependentUpon>frmREPORT_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmRES_MAIN.resx">
      <DependentUpon>frmRES_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmSAVE_MAIN.resx">
      <DependentUpon>frmSAVE_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmSERVICE_MAIN.resx">
      <DependentUpon>frmSERVICE_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmSTOCK_MAIN.resx">
      <DependentUpon>frmSTOCK_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmTRANS_MAIN.resx">
      <DependentUpon>frmTRANS_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmUSER_MAIN.resx">
      <DependentUpon>frmUSER_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main_Form\frmVIS_MAIN.resx">
      <DependentUpon>frmVIS_MAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frmDES.resx">
      <DependentUpon>frmDES.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frmDIAG_LIST.resx">
      <DependentUpon>frmDIAG_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frmMEDREQ.resx">
      <DependentUpon>frmMEDREQ.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frmMEDREQ_RESULT.resx">
      <DependentUpon>frmMEDREQ_RESULT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frmMED_LIST.resx">
      <DependentUpon>frmMED_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frmOLD_DIAG_LIST.resx">
      <DependentUpon>frmOLD_DIAG_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frmOLD_MEDREP.resx">
      <DependentUpon>frmOLD_MEDREP.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frmOLD_MEDREQ_LIST.resx">
      <DependentUpon>frmOLD_MEDREQ_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frmOLD_MED_LIST.resx">
      <DependentUpon>frmOLD_MED_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frmOLD_SER_LIST.resx">
      <DependentUpon>frmOLD_SER_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frmSER_LIST.resx">
      <DependentUpon>frmSER_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MED_FORM\frm_ENDO.resx">
      <DependentUpon>frm_ENDO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmAPO.resx">
      <DependentUpon>frmAPO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmCARD.resx">
      <DependentUpon>frmCARD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmCLINIC.resx">
      <DependentUpon>frmCLINIC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmCLINIC_PANEL.resx">
      <DependentUpon>frmCLINIC_PANEL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmCOMPANY.resx">
      <DependentUpon>frmCOMPANY.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmCUST.resx">
      <DependentUpon>frmCUST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmCUST_RES.resx">
      <DependentUpon>frmCUST_RES.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmDATABASE_CONFIG.resx">
      <DependentUpon>frmDATABASE_CONFIG.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmDIAGNOIS.resx">
      <DependentUpon>frmDIAGNOIS.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmDOCTORS.resx">
      <DependentUpon>frmDOCTORS.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmDOS.resx">
      <DependentUpon>frmDOS.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmHOLIDAY.resx">
      <DependentUpon>frmHOLIDAY.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmLOGIN.resx">
      <DependentUpon>frmLOGIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmMAIN.resx">
      <DependentUpon>frmMAIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmMEDCHEK.resx">
      <DependentUpon>frmMEDCHEK.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmMEDCIN.resx">
      <DependentUpon>frmMEDCIN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmMEDREP.resx">
      <DependentUpon>frmMEDREP.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmSERVICE.resx">
      <DependentUpon>frmSERVICE.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmSETTING.resx">
      <DependentUpon>frmSETTING.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmTRANSACTION.resx">
      <DependentUpon>frmTRANSACTION.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmVISIT.resx">
      <DependentUpon>frmVISIT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project Form\frmVISIT_EDIT.resx">
      <DependentUpon>frmVISIT_EDIT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmAPOLIST_REPORT.resx">
      <DependentUpon>frmAPOLIST_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmAPO_VIS_REPORT.resx">
      <DependentUpon>frmAPO_VIS_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmCUST_PAST_HISTORY_REPORT.resx">
      <DependentUpon>frmCUST_PAST_HISTORY_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmMEDLIST_REPORT.resx">
      <DependentUpon>frmMEDLIST_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmMEDLIST_VIS_REPORT.resx">
      <DependentUpon>frmMEDLIST_VIS_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmMEDREQ_REPORT.resx">
      <DependentUpon>frmMEDREQ_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmMEDREQ_VIS_REPORT.resx">
      <DependentUpon>frmMEDREQ_VIS_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmMREP_VIS_REPORT.resx">
      <DependentUpon>frmMREP_VIS_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmSERLIST_REP.resx">
      <DependentUpon>frmSERLIST_REP.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmSERLIST_VIS_REPORT.resx">
      <DependentUpon>frmSERLIST_VIS_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmTRANSACTION_LIST_DATA_REPORT.resx">
      <DependentUpon>frmTRANSACTION_LIST_DATA_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmTRANSACTION_REPORT.resx">
      <DependentUpon>frmTRANSACTION_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmVISLIST_REPORT.resx">
      <DependentUpon>frmVISLIST_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports_Form\frmVIS_SER_PRICE_REPORT.resx">
      <DependentUpon>frmVIS_SER_PRICE_REPORT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="STOCK_DATA\frmSTOCK.resx">
      <DependentUpon>frmSTOCK.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="STOCK_DATA\frmStock_AddMoney.resx">
      <DependentUpon>frmStock_AddMoney.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="STOCK_DATA\frm_Stock_AddMoney_List.resx">
      <DependentUpon>frm_Stock_AddMoney_List.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="STOCK_DATA\Frm_Stock_PullMoney.resx">
      <DependentUpon>Frm_Stock_PullMoney.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="STOCK_DATA\Frm_Stock_PullMoney_List.resx">
      <DependentUpon>Frm_Stock_PullMoney_List.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="User_Form\frmUSERS_DATA.resx">
      <DependentUpon>frmUSERS_DATA.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="User_Form\frmUSER_LIST.resx">
      <DependentUpon>frmUSER_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="User_Form\frmUSER_PER.resx">
      <DependentUpon>frmUSER_PER.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="User_Form\frmUSER_TYPE.resx">
      <DependentUpon>frmUSER_TYPE.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="User_Form\frmUSER_TYPE_LIST.resx">
      <DependentUpon>frmUSER_TYPE_LIST.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="App.config" />
    <None Include="CLINIC_DATASET.xsc">
      <DependentUpon>CLINIC_DATASET.xsd</DependentUpon>
    </None>
    <None Include="CLINIC_DATASET.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>CLINIC_DATASET.Designer.cs</LastGenOutput>
    </None>
    <None Include="CLINIC_DATASET.xss">
      <DependentUpon>CLINIC_DATASET.xsd</DependentUpon>
    </None>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\accordionControlElement14.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\accordionControlElement17.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\accordionControlElement19.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\accordionControlElement20.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\accordionControlElement21.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\استقبال_المرضى.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\اعدادات_النظام.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\الاجازات_والتقارير.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\الاجراءات.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\الادوية.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\الاطباء.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\الاعدادات.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\التامين_الصحي.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\التشخيص.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\التقارير.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\الحسابات.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\العيادات.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\الفحوصات.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\القسم_الطبي.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\الكشوفات.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\المرضى.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\المستخدمين.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\المواعيد.ImageOptions.Image.png" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <None Include="Resources\logo.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\images.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Inventory-icon.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>