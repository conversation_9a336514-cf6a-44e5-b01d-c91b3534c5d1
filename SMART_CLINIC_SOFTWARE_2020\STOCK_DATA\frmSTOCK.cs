﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;
namespace SMART_CLINIC_SOFTWARE_2020.STOCK_DATA
{
    public partial class frmSTOCK : DevExpress.XtraEditors.XtraForm
    {
        public frmSTOCK()
        {
            InitializeComponent();
        }

        Classes.clsSTOCK_DATA NclsSTOCK = new Classes.clsSTOCK_DATA();


        public void Clear_Date()
        {
            try
            {
                gridControl1.DataSource = NclsSTOCK.STOCK_List();

                gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
                gridView1.OptionsView.EnableAppearanceEvenRow = true;
                gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
                gridView1.OptionsView.EnableAppearanceOddRow = true;
                gridView1.OptionsBehavior.Editable = false;
               
                gridView1.Columns.Remove(gridView1.Columns["Stock_ID"]);
                gridView1.Columns["Stock_Code"].Caption = "كود الخزنة";
                gridView1.Columns["Stock_Name"].Caption = "اسم الخزنة";
                gridView1.Columns["Money"].Caption = "الرصيد";
                gridView1.BestFitColumns();

                txtSTOCK_CODE.Text = Classes.clsSTOCK_DATA.STOCK_DATATABLE.maxStock_Code().Rows[0]["Stock_Code"].ToString();
                txtSTOCK_Name.Text = "";
                txtMONEY.Text = "0";
                txtSTOCK_Name.Focus();
               
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

        }

        private void frmSTOCK_Load(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void btnSAVE_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (txtSTOCK_Name.Text != "" && txtSTOCK_CODE.Text != "")
                {
                    Classes.clsSTOCK_DATA.STOCK_DATATABLE.InsertSTOCK(Convert.ToInt64(txtSTOCK_CODE.Text), txtSTOCK_Name.Text,Convert.ToDecimal(txtMONEY.Text));
                    Clear_Date();
                    MessageBox.Show("تم حفظ البيانات بشكل صحيح");
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("STOCK_NAME_IDEX"))
                {
                    MessageBox.Show("اسم الخزنة موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (gridView1.RowCount > 0)
                {
                    NclsSTOCK.Select_STOCK(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["Stock_Code"]).ToString()));
                    txtSTOCK_CODE.Text = Classes.clsSTOCK_DATA.Stock_Code.ToString();
                    txtSTOCK_Name.Text = Classes.clsSTOCK_DATA.Stock_Name;
                    txtMONEY.Text = Classes.clsSTOCK_DATA.Money.ToString();
                    
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void btnCLEAR_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Clear_Date();
        }

        private void btnEDITE_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (MessageBox.Show("هل انت متاكد من تعديل البيانات", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    if (gridView1.RowCount > 0 && txtSTOCK_CODE.Text != "" && txtSTOCK_Name.Text != "")
                    {
                        Classes.clsSTOCK_DATA.STOCK_DATATABLE.UpdateSTOCK(Convert.ToInt64(txtSTOCK_CODE.Text), txtSTOCK_Name.Text, Convert.ToDecimal(txtMONEY.Text), Convert.ToInt64(txtSTOCK_CODE.Text));
                        Clear_Date();
                        MessageBox.Show("تم تعديل البيانات بشكل صحيح");
                    }
                    else
                    {
                        Clear_Date();
                    }
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("STOCK_NAME_IDEX"))
                {
                    MessageBox.Show("اسم الخزنة موجودة مسبقا", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Clear_Date();
                    return;
                }
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
        }

        private void btnDELETE_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (MessageBox.Show("هل انت متاكد من حذف البيانات", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    if (gridView1.RowCount > 0 && txtSTOCK_CODE.Text != "" && txtSTOCK_Name.Text != "")
                    {
                        Classes.clsSTOCK_DATA.STOCK_DATATABLE.DeleteSTOCK(Convert.ToInt64(txtSTOCK_CODE.Text));
                        Clear_Date();
                        MessageBox.Show("تم حذف البيانات بشكل صحيح");
                    }
                    else
                    {
                        Clear_Date();
                    }
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
    }
}