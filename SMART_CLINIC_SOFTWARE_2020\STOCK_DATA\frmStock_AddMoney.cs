﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;


namespace SMART_CLINIC_SOFTWARE_2020.STOCK_DATA
{
    public partial class frmStock_AddMoney : DevExpress.XtraEditors.XtraForm
    {
        public frmStock_AddMoney()
        {
            InitializeComponent();
        }

        Classes.clsStock_AddMoney NclsSTOCK_INSERT = new Classes.clsStock_AddMoney();
        Classes.clsSTOCK_DATA NclsSTOCK = new Classes.clsSTOCK_DATA();
        DataTable dt_STOCK = new DataTable();

        public void Clear_Date()
        {
            try
            {
                cmbSTOCK_ID.DataSource = NclsSTOCK.STOCK_List();
                cmbSTOCK_ID.DisplayMember = "Stock_ID";
                cmbSTOCK_NAME.DataSource = cmbSTOCK_ID.DataSource;
                cmbSTOCK_NAME.DisplayMember = "Stock_Name";

                //cmbCurrentMoney.DataSource = NclsSTOCK.STOCK_List();
                //cmbCurrentMoney.DisplayMember = "Money";

                txtMONEY.Text = "0";
                txtItemName.Text = "";
                txtReason.Text = "";
                DtbDate.Value = DateTime.Now;
                txtItemName.Focus();

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

        }

        private void frmStock_AddMoney_Load(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void btnCLEAR_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Clear_Date();

        }

        private void btnSAVE_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (txtMONEY.Text != "0" && txtItemName.Text != "")
                {
                    if (MessageBox.Show("هل انت متاكد سيتم اضافة هذا الرصيد للخزنه", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                    {
                        Classes.clsSTOCK_DATA.STOCK_DATATABLE.UpdateSTOCK_MONEY(cmbSTOCK_NAME.Text, Convert.ToDecimal(txtTOTAL.Text), cmbSTOCK_NAME.Text);
                        Classes.clsStock_AddMoney.STOCK_INSERT_DATATABLE.InsertSTOCK_ADDMONEY(Convert.ToInt64(cmbSTOCK_ID.Text), Convert.ToDecimal(txtMONEY.Text), string.Format(DtbDate.Value.ToString(), "yyyy/MM/dd"), txtItemName.Text, "رصيد اضافى", txtReason.Text);
                        Clear_Date();
                        MessageBox.Show("تم حفظ البيانات بشكل صحيح");
                    }
                       
                }
                else
                {
                    MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                    Clear_Date();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                Clear_Date();
            }
        }

        private void txtMONEY_EditValueChanged(object sender, EventArgs e)
        {
            txtTOTAL.Text = (Convert.ToDecimal(cmbCurrentMoney.Text) + Convert.ToDecimal(txtMONEY.Text)).ToString();

        }

        private void cmbSTOCK_NAME_SelectedIndexChanged(object sender, EventArgs e)
        {
            dt_STOCK = NclsSTOCK.Select_STOCK_NAME(cmbSTOCK_NAME.Text);
            if (dt_STOCK.Rows.Count > 0)
            {
                cmbCurrentMoney.Text = dt_STOCK.Rows[0]["Money"].ToString();
            }
        }
    }
}