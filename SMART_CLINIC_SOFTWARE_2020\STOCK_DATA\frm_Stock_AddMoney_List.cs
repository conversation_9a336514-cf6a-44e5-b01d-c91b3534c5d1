﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SMART_CLINIC_SOFTWARE_2020.CLINIC_DATASETTableAdapters;


namespace SMART_CLINIC_SOFTWARE_2020.STOCK_DATA
{
    public partial class frm_Stock_AddMoney_List : DevExpress.XtraEditors.XtraForm
    {
        public frm_Stock_AddMoney_List()
        {
            InitializeComponent();
        }

        Classes.clsSTOCK_DATA NclsSTOCK = new Classes.clsSTOCK_DATA();


        public void Clear_Date()
        {
            try
            {
                cmbSTOCK_ID.DataSource = NclsSTOCK.STOCK_List();
                cmbSTOCK_ID.ValueMember = "Stock_ID";
                cmbSTOCK_NAME.DataSource = cmbSTOCK_ID.DataSource;
                cmbSTOCK_NAME.ValueMember = "Stock_Name";
                txtTotalPhar.Text = "";
                txtDelete.Text = "";
                dtpF_DATE.Value = DateTime.Now;
                dtpS_DATE.Value = DateTime.Now;

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }

        }

        public void GRID_DATA(string F_DATE, string S_DATE, string Stock_Name)
        {
            gridControl1.DataSource = Classes.clsStock_AddMoney.STOCK_INSERT_DATATABLE.STOCK_INSERTbyDATE(F_DATE, S_DATE, Stock_Name);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["Stock_ID"]);

            gridView1.Columns["Order_ID"].Caption = "رقم الايداع";
            gridView1.Columns["Money"].Caption = "مبلغ الايداع";
            gridView1.Columns["Date"].Caption = "تاريخ الايداع";
            gridView1.Columns["Name"].Caption = "اسم المودع";
            gridView1.Columns["Type"].Caption = "نوع الايداع";
            gridView1.Columns["Reason"].Caption = "سبب الايداع";
            gridView1.Columns["Stock_Name"].Caption = "اسم الخزنة";
            gridView1.BestFitColumns();
        }

        private void frm_Stock_AddMoney_List_Load(object sender, EventArgs e)
        {
            GRID_DATA(string.Format(dtpF_DATE.Value.ToString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"), cmbSTOCK_NAME.Text);

            Clear_Date();

           
        }

        private void btnSEARCH_Click(object sender, EventArgs e)
        {
            GRID_DATA(string.Format(dtpF_DATE.Value.ToString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"), cmbSTOCK_NAME.Text);
            CALCMONEY();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            GRID_DATA(string.Format(dtpF_DATE.Value.ToString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"), cmbSTOCK_NAME.Text);
            Clear_Date();
        }

        void CALCMONEY()
        {
            decimal TOTAL = 0;

            for (int i = 0; i < gridView1.DataRowCount; i++)
            {
                TOTAL += Convert.ToDecimal(gridView1.GetRowCellValue(i, "Money"));
            }
            txtTotalPhar.Text = TOTAL.ToString();
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            try
            {
                if (MessageBox.Show("هل انت متاكد من حذف البيانات", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    if (gridView1.RowCount > 0)
                    {
                        Classes.clsStock_AddMoney.STOCK_INSERT_DATATABLE.DeleteSTOCK_INSERTbyDATE(string.Format(dtpF_DATE.Value.ToString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"));
                        Clear_Date();
                        GRID_DATA(string.Format(dtpF_DATE.Value.ToString(), "MM/dd/yyyy"), string.Format(dtpS_DATE.Value.ToString(), "MM/dd/yyyy"), cmbSTOCK_NAME.Text);
                        MessageBox.Show("تم حذف البيانات بشكل صحيح");
                    }
                    else
                    {
                        Clear_Date();
                    }
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void txtDelete_EditValueChanged(object sender, EventArgs e)
        {
            if (txtDelete.Text == "M&A")
            {
                btnDELETE.Visible = true;
            }
            else
            {
                btnDELETE.Visible = false;
            }
        }
    }
}