﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.User_Form
{
    public partial class frmUSERS_DATA : DevExpress.XtraEditors.XtraForm
    {
        public frmUSERS_DATA()
        {
            InitializeComponent();
        }
        Classes.clsUSERS NclsUSERS_DATA = new Classes.clsUSERS();
        Classes.clsUSER_TYPE NclsUSER_TYPE = new Classes.clsUSER_TYPE();


        public void Clear_Date()
        {
            txtUSER_CODE.Text = Classes.clsUSERS.USER_DATATABLE.maxUSER_CODE().ToString();
            txtUSER_NAME.Text = "";
            txtUSER_PASS.Text = "";
            txtUSER_PASS2.Text = "";
            txtUSER_MOBILE.Text = "";
            txtUSER_ADDRESS.Text = "";

            cmbUSER_T_CODE.DataSource = NclsUSER_TYPE.User_T_List();
            cmbUSER_T_CODE.DisplayMember = "USER_T_CODE";
            cmbUSER_T_TYPE.DataSource = cmbUSER_T_CODE.DataSource;
            cmbUSER_T_TYPE.DisplayMember = "USER_T_TYPE";
            txtSEARCH.Text = "";
            DATA_GRID();
            //txtUSER_NAME.Focus();
        }

        public void DATA_GRID()
        {
            gridControl1.DataSource = Classes.clsUSERS.USER_DATATABLE.USER_LISTbyName(txtSEARCH.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["USER_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["USER_PASSWORD"]);
            gridView1.Columns.Remove(gridView1.Columns["USER_T_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["USER_CODE"].Caption = "رقم المستخدم";
            gridView1.Columns["USER_NAME"].Caption = "أسم المستخدم";
            gridView1.Columns["USER_MOBILE"].Caption = "رقم الهاتف";
            gridView1.Columns["USER_ADDRESS"].Caption = "العنوان";
            gridView1.Columns["USER_T_TYPE"].Caption = "نوع المستخدم";
            gridView1.BestFitColumns();

        }

        private void frmUSERS_DATA_Load(object sender, EventArgs e)
        {
            foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            Clear_Date();
        }

        private void txtSEARCH_EditValueChanged(object sender, EventArgs e)
        {
            DATA_GRID();
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            txtSEARCH.Text = "";
            txtSEARCH.Focus();
        }

        private void lblUSER_T_LIST_Click(object sender, EventArgs e)
        {
            frmUSER_TYPE_LIST frmUSER_T_LIST = new frmUSER_TYPE_LIST();
            frmUSER_T_LIST.ShowDialog();
            if(Classes.clsUSER_TYPE.USER_T_CODE != 0)
            {
                cmbUSER_T_CODE.Text = Classes.clsUSER_TYPE.USER_T_CODE.ToString();
                cmbUSER_T_TYPE.Text = Classes.clsUSER_TYPE.USER_T_TYPE;
            }
            else
            {
                MessageBox.Show("لم يتم اختيار نوع المستخدم" , " !! تنبيه" , MessageBoxButtons.OK , MessageBoxIcon.Warning);
                cmbUSER_T_CODE.Text = "0";
                cmbUSER_T_TYPE.Text = "";
            }
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            if (txtUSER_CODE.Text != "" && txtUSER_NAME.Text != "" && txtUSER_PASS.Text != "" && txtUSER_PASS2.Text != "" && cmbUSER_T_CODE.Text != "0" && cmbUSER_T_TYPE.Text != "")
            {
                if(txtUSER_PASS.Text == txtUSER_PASS2.Text)
                {
                    try
                    {
                        Classes.clsUSERS.USER_DATATABLE.InsertUSERS(Convert.ToInt64(txtUSER_CODE.Text), txtUSER_NAME.Text, txtUSER_PASS.Text, Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID), txtUSER_MOBILE.Text, txtUSER_ADDRESS.Text, Convert.ToInt64(cmbUSER_T_CODE.Text));
                        Clear_Date();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.Message);
                    }
                }
                else
                {
                    MessageBox.Show("كلمة المرور غير متطابقة", " !! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
               
            }
            else
            {
                MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                Clear_Date();
            }
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0 && txtUSER_CODE.Text != "" && txtUSER_NAME.Text != "" && txtUSER_PASS.Text != "" && txtUSER_PASS2.Text != "" && cmbUSER_T_CODE.Text != "0" && cmbUSER_T_TYPE.Text != "")
            {
                if (txtUSER_PASS.Text == txtUSER_PASS2.Text)
                {
                    try
                    {
                        if (Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0]["تعديل_مستخدم"]) == 1 && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0]["تعديل_كلمة_المرور"]) == 1 && txtUSER_NAME.Text == Classes.clsUSERS.USER_NAME && txtUSER_CODE.Text == Classes.clsUSERS.USER_CODE.ToString() && cmbUSER_T_CODE.Text == Classes.clsUSERS.USER_T_CODE.ToString())
                        {
                            Classes.clsUSERS.USER_DATATABLE.UpdateUSERS(Convert.ToInt64(txtUSER_CODE.Text), txtUSER_NAME.Text, txtUSER_PASS.Text, Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID), txtUSER_MOBILE.Text, txtUSER_ADDRESS.Text, Convert.ToInt64(cmbUSER_T_CODE.Text), Classes.clsUSERS.USER_ID, Classes.clsUSERS.USER_CODE, Classes.clsUSERS.USER_NAME);
                            Clear_Date();
                        }
                        else
                        {
                            Clear_Date();
                            return;
                        }
                        //Classes.clsUSERS.USER_DATATABLE.UpdateUSERS(Convert.ToInt64(txtUSER_CODE.Text), txtUSER_NAME.Text, txtUSER_PASS.Text, Convert.ToInt64(SMART_CLINIC_SOFTWARE_2020.Properties.Settings.Default.L_CLI_ID), txtUSER_MOBILE.Text, txtUSER_ADDRESS.Text, Convert.ToInt64(cmbUSER_T_CODE.Text), Classes.clsUSERS.USER_ID, Classes.clsUSERS.USER_CODE, Classes.clsUSERS.USER_NAME);
                        //Clear_Date();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.Message);
                    }
                }
                else
                {
                    MessageBox.Show("كلمة المرور غير متطابقة", " !! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

            }
            else
            {
                MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                Clear_Date();
            }
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0 && txtUSER_CODE.Text != "" && txtUSER_NAME.Text != "" && txtUSER_PASS.Text != "" && txtUSER_PASS2.Text != "" && cmbUSER_T_CODE.Text != "0" && cmbUSER_T_TYPE.Text != "")
            {
                try
                {
                    Classes.clsUSERS.USER_DATATABLE.DeleteUSERS(Classes.clsUSERS.USER_ID, Classes.clsUSERS.USER_CODE, Classes.clsUSERS.USER_NAME);
                    Clear_Date();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
            else
            {
                Clear_Date();
            }
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void btnSHOW_MouseDown(object sender, MouseEventArgs e)
        {
            if(txtUSER_PASS.Properties.PasswordChar != '*')
            {
                txtUSER_PASS.Properties.PasswordChar = '*';
            }
            else
            {
                txtUSER_PASS.Properties.PasswordChar = '\0';
            }
        }

        private void btnSHOW_MouseUp(object sender, MouseEventArgs e)
        {
            if (txtUSER_PASS.Properties.PasswordChar != '*')
            {
                txtUSER_PASS.Properties.PasswordChar = '*';
            }
            else
            {
                txtUSER_PASS.Properties.PasswordChar = '\0';
            }
        }

        private void btnSHOW2_MouseDown(object sender, MouseEventArgs e)
        {
            if (txtUSER_PASS2.Properties.PasswordChar != '*')
            {
                txtUSER_PASS2.Properties.PasswordChar = '*';
            }
            else
            {
                txtUSER_PASS2.Properties.PasswordChar = '\0';
            }
        }

        private void btnSHOW2_MouseUp(object sender, MouseEventArgs e)
        {
            if (txtUSER_PASS2.Properties.PasswordChar != '*')
            {
                txtUSER_PASS2.Properties.PasswordChar = '*';
            }
            else
            {
                txtUSER_PASS2.Properties.PasswordChar = '\0';
            }
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                NclsUSERS_DATA.Select_User(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["USER_CODE"])), gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["USER_NAME"]).ToString());
                if (Classes.clsUSERS.USER_ID != 0)
                {
                    txtUSER_CODE.Text = Classes.clsUSERS.USER_CODE.ToString();
                    txtUSER_NAME.Text = Classes.clsUSERS.USER_NAME;
                    txtUSER_PASS.Text = Classes.clsUSERS.USER_PASSWORD;
                    txtUSER_PASS2.Text = Classes.clsUSERS.USER_PASSWORD;
                    txtUSER_MOBILE.Text = Classes.clsUSERS.USER_MOBILE;
                    txtUSER_ADDRESS.Text = Classes.clsUSERS.USER_ADDRESS;
                    cmbUSER_T_CODE.Text = Classes.clsUSERS.USER_T_CODE.ToString();
                    txtUSER_NAME.Focus();
                }
                else
                {
                    Clear_Date();
                }

            }
        }
    }
}