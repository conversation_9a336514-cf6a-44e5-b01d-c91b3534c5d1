﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.LIST_FORM
{
    public partial class frmUSER_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmUSER_LIST()
        {
            InitializeComponent();
        }
        Classes.clsUSERS NclsUser = new Classes.clsUSERS();
        public void USER_GRID_LIST()
        {
            gridControl1.DataSource = Classes.clsUSERS.USER_DATATABLE.USER_LISTbyName(txtUserName.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["USER_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["USER_PASSWORD"]);
            gridView1.Columns.Remove(gridView1.Columns["USER_MOBILE"]);
            gridView1.Columns.Remove(gridView1.Columns["USER_ADDRESS"]);
            gridView1.Columns.Remove(gridView1.Columns["USER_T_CODE"]);
            gridView1.Columns.Remove(gridView1.Columns["CLI_ID"]);
            gridView1.Columns["USER_CODE"].Caption = "رقم المستخدم";
            gridView1.Columns["USER_NAME"].Caption = "أسم المستخدم";
            gridView1.Columns["USER_T_TYPE"].Caption = "نوع المستخدم";

        }
        private void frmUSER_LIST_Load(object sender, EventArgs e)
        {
            txtUserName.Text = "";
            USER_GRID_LIST();
        }

        private void txtUserName_EditValueChanged(object sender, EventArgs e)
        {
            USER_GRID_LIST();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtUserName.Text = "";
            txtUserName.Focus();
        }

      
        private void gridView1_DoubleClick_1(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsUser.Select_User(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["USER_CODE"])), gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["USER_NAME"]).ToString()).Rows.Count == 1)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

            }
        }
    }
}