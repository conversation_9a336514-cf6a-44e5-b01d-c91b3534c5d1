﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.User_Form
{
    public partial class frmUSER_PER : DevExpress.XtraEditors.XtraForm
    {
        public frmUSER_PER()
        {
            InitializeComponent();
        }

        Classes.clsUSER_TYPE NclsUSER_TYPE = new Classes.clsUSER_TYPE();
        DataTable PERDT = new DataTable();

        public void Clear_Date()
        {
            cmbUSER_T_CODE.DataSource = NclsUSER_TYPE.User_T_List();
            cmbUSER_T_CODE.DisplayMember = "USER_T_CODE";
            cmbUSER_T_TYPE.DataSource = cmbUSER_T_CODE.DataSource;
            cmbUSER_T_TYPE.DisplayMember = "USER_T_TYPE";

            cmbUSER_T_CODE1.DataSource = NclsUSER_TYPE.User_T_List();
            cmbUSER_T_CODE1.DisplayMember = "USER_T_CODE";
            cmbUSER_T_TYPE1.DataSource = cmbUSER_T_CODE1.DataSource;
            cmbUSER_T_TYPE1.DisplayMember = "USER_T_TYPE";
            USER_PER_DATA(Convert.ToInt64(cmbUSER_T_CODE.Text));
        }
        private void USER_PER_DATA(long S_USER_T_CODE)
        {
            DataTable dt = new DataTable();
            dt = Classes.clsUSER_PER.USER_P_NAME_DATATABLE.SELECT_USER_PER_NAME(S_USER_T_CODE);
            PERDT = dt;
            clbUSER_PER.Items.Clear();
            for(int i = 0; i < dt.Columns.Count; i++)
            {
                if(dt.Rows.Count > 0)
                {
                    if(dt.Rows[0][i] != DBNull.Value && dt.Rows[0][i].ToString() != "-1")
                    {
                        clbUSER_PER.Items.Add(dt.Columns[i].ColumnName);
                    }
                }
            }
            for (int i = 0; i <= (clbUSER_PER.Items.Count - 1); i++)
            {
                if (dt.Rows.Count > 0)
                {
                    if (dt.Rows[0][i] != DBNull.Value && dt.Rows[0][i].ToString() != "-1")
                    {
                        clbUSER_PER.SetItemChecked(i , Convert.ToBoolean(dt.Rows[0][i]));
                    }
                }
            }

        }
        private void frmUSER_PER_Load(object sender, EventArgs e)
        {
            foreach (var item in groupControl2.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            Clear_Date();
        }

        private void lblUSER_T_LIST_Click(object sender, EventArgs e)
        {
            frmUSER_TYPE_LIST frmUSER_T_LIST = new frmUSER_TYPE_LIST();
            frmUSER_T_LIST.ShowDialog();
            if (Classes.clsUSER_TYPE.USER_T_CODE != 0)
            {
                cmbUSER_T_CODE1.Text = Classes.clsUSER_TYPE.USER_T_CODE.ToString();
                cmbUSER_T_TYPE1.Text = Classes.clsUSER_TYPE.USER_T_TYPE;
            }
            else
            {
                MessageBox.Show("لم يتم اختيار نوع المستخدم", " !! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbUSER_T_CODE1.Text = "0";
                cmbUSER_T_TYPE1.Text = "";
            }
        }

        private void cmbUSER_T_CODE_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(cmbUSER_T_CODE.Text != "" && cmbUSER_T_TYPE.Text != "")
            {
                USER_PER_DATA(Convert.ToInt64(cmbUSER_T_CODE.Text));
            }
        }

        private void cmbUSER_T_CODE1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbUSER_T_CODE1.Text != "" && cmbUSER_T_TYPE1.Text != "")
            {
                USER_PER_DATA(Convert.ToInt64(cmbUSER_T_CODE1.Text));
            }
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            if(cmbUSER_T_CODE1.Text != "" && cmbUSER_T_TYPE1.Text != "" && clbUSER_PER.Items.Count > 0)
            {
                for (int i = 0; i <= (clbUSER_PER.Items.Count - 1); i++)
                {
                    PERDT.Rows[0][i] = Convert.ToInt32(clbUSER_PER.GetItemChecked(i));
                }
                Classes.clsUSER_PER.USER_P_DATATABLE.UpdateUSER_PER(Convert.ToInt32(PERDT.Rows[0][0]), Convert.ToInt32(PERDT.Rows[0][1]), Convert.ToInt32(PERDT.Rows[0][2]), Convert.ToInt32(PERDT.Rows[0][3]), Convert.ToInt32(PERDT.Rows[0][4]), Convert.ToInt32(PERDT.Rows[0][5]), Convert.ToInt32(PERDT.Rows[0][6]), Convert.ToInt32(PERDT.Rows[0][7]), Convert.ToInt32(PERDT.Rows[0][8]), Convert.ToInt32(PERDT.Rows[0][9]), Convert.ToInt32(PERDT.Rows[0][10]),
                                                                    Convert.ToInt32(PERDT.Rows[0][11]), Convert.ToInt32(PERDT.Rows[0][12]), Convert.ToInt32(PERDT.Rows[0][13]), Convert.ToInt32(PERDT.Rows[0][14]), Convert.ToInt32(PERDT.Rows[0][15]), Convert.ToInt32(PERDT.Rows[0][16]), Convert.ToInt32(PERDT.Rows[0][17]), Convert.ToInt32(PERDT.Rows[0][18]), Convert.ToInt32(PERDT.Rows[0][19]), Convert.ToInt32(PERDT.Rows[0][20]),
                                                                    Convert.ToInt32(PERDT.Rows[0][21]), Convert.ToInt32(PERDT.Rows[0][22]), Convert.ToInt32(PERDT.Rows[0][23]), Convert.ToInt32(PERDT.Rows[0][24]), Convert.ToInt32(PERDT.Rows[0][25]), Convert.ToInt32(PERDT.Rows[0][26]), Convert.ToInt32(PERDT.Rows[0][27]), Convert.ToInt32(PERDT.Rows[0][28]), Convert.ToInt32(PERDT.Rows[0][29]), Convert.ToInt32(PERDT.Rows[0][30]),
                                                                    Convert.ToInt32(PERDT.Rows[0][31]), Convert.ToInt32(PERDT.Rows[0][32]), Convert.ToInt32(PERDT.Rows[0][33]), Convert.ToInt32(PERDT.Rows[0][34]), Convert.ToInt32(PERDT.Rows[0][35]), Convert.ToInt32(PERDT.Rows[0][36]), Convert.ToInt32(PERDT.Rows[0][37]), Convert.ToInt32(PERDT.Rows[0][38]), Convert.ToInt32(PERDT.Rows[0][39]), Convert.ToInt32(PERDT.Rows[0][40]),
                                                                    Convert.ToInt32(PERDT.Rows[0][41]), Convert.ToInt32(PERDT.Rows[0][42]), Convert.ToInt32(PERDT.Rows[0][43]), Convert.ToInt32(PERDT.Rows[0][44]), Convert.ToInt32(PERDT.Rows[0][45]), Convert.ToInt32(PERDT.Rows[0][46]), Convert.ToInt32(PERDT.Rows[0][47]), Convert.ToInt32(PERDT.Rows[0][48]), Convert.ToInt32(PERDT.Rows[0][49]), Convert.ToInt32(PERDT.Rows[0][50]),
                                                                    Convert.ToInt32(PERDT.Rows[0][51]), Convert.ToInt32(PERDT.Rows[0][52]), Convert.ToInt32(PERDT.Rows[0][53]), Convert.ToInt32(PERDT.Rows[0][54]), Convert.ToInt32(PERDT.Rows[0][55]), Convert.ToInt32(PERDT.Rows[0][56]), Convert.ToInt32(PERDT.Rows[0][57]), Convert.ToInt32(PERDT.Rows[0][58]), Convert.ToInt32(PERDT.Rows[0][59]), Convert.ToInt32(PERDT.Rows[0][60]),
                                                                    Convert.ToInt32(PERDT.Rows[0][61]), Convert.ToInt32(PERDT.Rows[0][62]), Convert.ToInt32(PERDT.Rows[0][63]), Convert.ToInt32(PERDT.Rows[0][64]), Convert.ToInt32(PERDT.Rows[0][65]), Convert.ToInt32(PERDT.Rows[0][66]), Convert.ToInt32(PERDT.Rows[0][67]), Convert.ToInt32(PERDT.Rows[0][68]), Convert.ToInt32(PERDT.Rows[0][69]), Convert.ToInt32(PERDT.Rows[0][70]),
                                                                    Convert.ToInt32(PERDT.Rows[0][71]), Convert.ToInt32(PERDT.Rows[0][72]), Convert.ToInt32(PERDT.Rows[0][73]), Convert.ToInt32(PERDT.Rows[0][74]), Convert.ToInt32(PERDT.Rows[0][75]), Convert.ToInt32(PERDT.Rows[0][76]), Convert.ToInt32(PERDT.Rows[0][77]), Convert.ToInt32(PERDT.Rows[0][78]), Convert.ToInt32(PERDT.Rows[0][79]), Convert.ToInt32(PERDT.Rows[0][80]),
                                                                    Convert.ToInt32(PERDT.Rows[0][81]), Convert.ToInt32(PERDT.Rows[0][82]), Convert.ToInt32(PERDT.Rows[0][83]), Convert.ToInt32(PERDT.Rows[0][84]), Convert.ToInt32(PERDT.Rows[0][85]), Convert.ToInt32(PERDT.Rows[0][86]), Convert.ToInt32(PERDT.Rows[0][87]), Convert.ToInt32(PERDT.Rows[0][88]), Convert.ToInt32(PERDT.Rows[0][89]), Convert.ToInt32(PERDT.Rows[0][90]),
                                                                    Convert.ToInt32(PERDT.Rows[0][91]), Convert.ToInt32(PERDT.Rows[0][92]), Convert.ToInt32(PERDT.Rows[0][93]), Convert.ToInt32(PERDT.Rows[0][94]), Convert.ToInt32(PERDT.Rows[0][95]), Convert.ToInt32(PERDT.Rows[0][96]), Convert.ToInt32(PERDT.Rows[0][97]), Convert.ToInt32(PERDT.Rows[0][98]), Convert.ToInt32(PERDT.Rows[0][99]), Convert.ToInt32(PERDT.Rows[0][100]),
                                                                    Convert.ToInt32(PERDT.Rows[0][101]), Convert.ToInt32(PERDT.Rows[0][102]), Convert.ToInt32(PERDT.Rows[0][103]), Convert.ToInt32(PERDT.Rows[0][104]), Convert.ToInt32(PERDT.Rows[0][105]), Convert.ToInt32(PERDT.Rows[0][106]), Convert.ToInt32(PERDT.Rows[0][107]), Convert.ToInt32(PERDT.Rows[0][108]), Convert.ToInt32(PERDT.Rows[0][109]), Convert.ToInt32(PERDT.Rows[0][110]),
                                                                    Convert.ToInt32(PERDT.Rows[0][111]), Convert.ToInt32(PERDT.Rows[0][112]), Convert.ToInt32(PERDT.Rows[0][113]), Convert.ToInt32(PERDT.Rows[0][114]), Convert.ToInt32(PERDT.Rows[0][115]), Convert.ToInt32(PERDT.Rows[0][116]), Convert.ToInt32(PERDT.Rows[0][117]), Convert.ToInt32(PERDT.Rows[0][118]), Convert.ToInt32(PERDT.Rows[0][119]), Convert.ToInt32(PERDT.Rows[0][120]),
                                                                    Convert.ToInt32(PERDT.Rows[0][121]), Convert.ToInt32(PERDT.Rows[0][122]), Convert.ToInt32(PERDT.Rows[0][123]), Convert.ToInt64(cmbUSER_T_CODE1.Text));
                Clear_Date();
            }
            else
            {
                Clear_Date();
            }
            
        }

        private void btnCHKALL_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clbUSER_PER.Items.Count; i++)
            {
                clbUSER_PER.SetItemChecked(i, true);
            }
        }

        private void btnUNCHKALL_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clbUSER_PER.Items.Count; i++)
            {
                clbUSER_PER.SetItemChecked(i, false);
            }
        }

        private void lblUSER_T_LIST1_Click(object sender, EventArgs e)
        {
            frmUSER_TYPE_LIST frmUSER_T_LIST = new frmUSER_TYPE_LIST();
            frmUSER_T_LIST.ShowDialog();
            if (Classes.clsUSER_TYPE.USER_T_CODE != 0)
            {
                cmbUSER_T_CODE.Text = Classes.clsUSER_TYPE.USER_T_CODE.ToString();
                cmbUSER_T_TYPE.Text = Classes.clsUSER_TYPE.USER_T_TYPE;
            }
            else
            {
                MessageBox.Show("لم يتم اختيار نوع المستخدم", " !! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbUSER_T_CODE.Text = "0";
                cmbUSER_T_TYPE.Text = "";
            }
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (cmbUSER_T_CODE1.Text != "" && cmbUSER_T_TYPE1.Text != "" && clbUSER_PER.Items.Count > 0)
            {
                for (int i = 0; i <= (clbUSER_PER.Items.Count - 1); i++)
                {
                    PERDT.Rows[0][i] = Convert.ToInt32(clbUSER_PER.GetItemChecked(i));
                }
                Classes.clsUSER_PER.USER_P_DATATABLE.UpdateUSER_PER(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,Convert.ToInt64(cmbUSER_T_CODE1.Text));
                Clear_Date();
            }
            else
            {
                Clear_Date();
            }
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
        }
    }
}