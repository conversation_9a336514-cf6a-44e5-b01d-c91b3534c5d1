﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.User_Form
{
    public partial class frmUSER_TYPE : DevExpress.XtraEditors.XtraForm
    {
        public frmUSER_TYPE()
        {
            InitializeComponent();
        }
        Classes.clsUSER_TYPE NclsUSER_T = new Classes.clsUSER_TYPE();
        Classes.clsCLINIC NclsClinic = new Classes.clsCLINIC();


        public void Clear_Date()
        {
            txtT_CODE.Text = Classes.clsUSER_TYPE.USER_T_DATATABLE.maxUSER_T_CODE().ToString();
            txtT_NOTE.Text = "";
            txtT_TYPE.Text = "";
            cmbT_STATE.Text = "";

            cmbCLI_ID.DataSource = NclsClinic.CLINIC_LIST();
            cmbCLI_ID.DisplayMember = "CLI_ID";
            cmbCLI_NAME.DataSource = cmbCLI_ID.DataSource;
            cmbCLI_NAME.DisplayMember = "CLI_NAME";
            txtSEARCH.Text = "";
            DATA_GRID();
            txtT_TYPE.Focus();

        }

        public void DATA_GRID()
        {
            gridControl1.DataSource = NclsUSER_T.SELECT_USER_T_LIST(txtSEARCH.Text, txtSEARCH.Text , txtSEARCH.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["USER_T_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["USER_T_NOTE"]);
            gridView1.Columns["USER_T_CODE"].Caption = "رقم النوع";
            gridView1.Columns["USER_T_TYPE"].Caption = "نوع المستخدم";
            gridView1.Columns["USER_T_STATE"].Caption = "حالة المستخدم";
            gridView1.Columns["CLI_ID"].Caption = "رقم العيادة";
            gridView1.Columns["CLI_NAME"].Caption = "اسم العيادة";
            gridView1.BestFitColumns();

        }
        private void frmUSER_TYPE_Load(object sender, EventArgs e)
        {
            foreach (var item in this.Controls.OfType<DevExpress.XtraEditors.SimpleButton>())
            {
                if (Classes.clsUSER_PER.PER_DT.Columns.Contains(item.Tag.ToString()) && Convert.ToInt32(Classes.clsUSER_PER.PER_DT.Rows[0][item.Tag.ToString()]) == 0)
                {
                    item.Enabled = false;
                }
            }
            Clear_Date();
        }

        private void txtSEARCH_EditValueChanged(object sender, EventArgs e)
        {
            DATA_GRID();
        }

        private void btnSAVE_Click(object sender, EventArgs e)
        {
            if (txtT_CODE.Text != "" && txtT_TYPE.Text != "")
            {
                try
                {
                    Classes.clsUSER_TYPE.USER_T_DATATABLE.InsertUSER_TYPE(Convert.ToInt64(txtT_CODE.Text), txtT_TYPE.Text, txtT_NOTE.Text,cmbT_STATE.Text == "" ? "فعال" : cmbT_STATE.Text, Convert.ToInt64(cmbCLI_ID.Text));
                    Classes.clsUSER_PER.USER_P_DATATABLE.InsertUSER_PER(Classes.clsUSER_PER.USER_P_DATATABLE.maxUSER_P_CODE(),0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,Convert.ToInt64(txtT_CODE.Text));
                    Clear_Date();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
            else
            {
                MessageBox.Show("الرجاء ادخال البيانات بشكل صحيح");
                Clear_Date();
            }
        }

        private void btnEDITE_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0 && txtT_CODE.Text != "" && txtT_TYPE.Text != "" && cmbCLI_ID.Text != "")
            {
                try
                {
                    Classes.clsUSER_TYPE.USER_T_DATATABLE.UpdateUSER_TYPE(Convert.ToInt64(txtT_CODE.Text), txtT_TYPE.Text, txtT_NOTE.Text, cmbT_STATE.Text == "" ? "فعال" : cmbT_STATE.Text, Convert.ToInt64(cmbCLI_ID.Text), Classes.clsUSER_TYPE.USER_T_CODE);
                    Clear_Date();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
            else
            {
                Clear_Date();
            }
        }

        private void btnDELETE_Click(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0 && txtT_CODE.Text != "" && txtT_TYPE.Text != "" && cmbCLI_ID.Text != "")
            {
                try
                {
                    Classes.clsUSER_TYPE.USER_T_DATATABLE.DeleteUSER_TYPE(Classes.clsUSER_TYPE.USER_T_CODE);
                    Clear_Date();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                } 
            }
            else
            {
                Clear_Date();
            }
        }


        private void lblCLINIC_LIST_Click(object sender, EventArgs e)
        {
            LIST_FORM.frmCLINIC_LIST frmCLI_LIST = new LIST_FORM.frmCLINIC_LIST();
            frmCLI_LIST.ShowDialog();
            cmbCLI_ID.Text = Classes.clsCLINIC.CLI_ID.ToString();
            cmbCLI_NAME.Text = Classes.clsCLINIC.CLI_NAME;
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            Clear_Date();
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            txtSEARCH.Text = "";
            txtSEARCH.Focus();
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                NclsUSER_T.SELECT_USER_T(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["USER_T_CODE"]).ToString()));
                if (Classes.clsUSER_TYPE.USER_T_CODE != 0)
                {
                    txtT_CODE.Text = Classes.clsUSER_TYPE.USER_T_CODE.ToString();
                    txtT_TYPE.Text = Classes.clsUSER_TYPE.USER_T_TYPE;
                    cmbT_STATE.Text = Classes.clsUSER_TYPE.USER_T_STATE;
                    txtT_NOTE.Text = Classes.clsUSER_TYPE.USER_T_NOTE;
                    cmbCLI_ID.Text = Classes.clsUSER_TYPE.CLI_ID.ToString();
                    txtT_TYPE.Focus();
                }
                else
                {
                    Clear_Date();
                }

            }
        }
    }
}