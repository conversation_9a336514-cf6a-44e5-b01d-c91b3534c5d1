﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SMART_CLINIC_SOFTWARE_2020.User_Form
{
    public partial class frmUSER_TYPE_LIST : DevExpress.XtraEditors.XtraForm
    {
        public frmUSER_TYPE_LIST()
        {
            InitializeComponent();
        }
        Classes.clsUSER_TYPE NclsUSER_T = new Classes.clsUSER_TYPE();

        public void GRID_DATA()
        {
            gridControl1.DataSource = Classes.clsUSER_TYPE.USER_T_DATATABLE.GetDataByActiveUSER_TYPE(txtSEARCH.Text, txtSEARCH.Text);
            gridView1.Appearance.EvenRow.BackColor = Color.FromArgb(200, 255, 249, 196);
            gridView1.OptionsView.EnableAppearanceEvenRow = true;
            gridView1.Appearance.OddRow.BackColor = Color.WhiteSmoke;
            gridView1.OptionsView.EnableAppearanceOddRow = true;
            gridView1.OptionsBehavior.Editable = false;
            gridView1.Columns.Remove(gridView1.Columns["USER_T_ID"]);
            gridView1.Columns.Remove(gridView1.Columns["USER_T_NOTE"]);
            gridView1.Columns["USER_T_CODE"].Caption = "رقم النوع";
            gridView1.Columns["USER_T_TYPE"].Caption = "نوع المستخدم";
            gridView1.Columns["USER_T_STATE"].Caption = "حالة المستخدم";
            gridView1.Columns["CLI_ID"].Caption = "رقم العيادة";
            gridView1.Columns["CLI_NAME"].Caption = "اسم العيادة";
            gridView1.BestFitColumns();
        }
        private void frmUSER_TYPE_LIST_Load(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void txtSEARCH_EditValueChanged(object sender, EventArgs e)
        {
            GRID_DATA();
        }

        private void btnCLEAR_Click(object sender, EventArgs e)
        {
            txtSEARCH.Text = "";
            txtSEARCH.Focus();
        }

        private void gridView1_DoubleClick_1(object sender, EventArgs e)
        {
            if (gridView1.RowCount > 0)
            {
                if (NclsUSER_T.SELECT_USER_T(Convert.ToInt64(gridView1.GetRowCellValue(gridView1.FocusedRowHandle, gridView1.Columns["USER_T_CODE"]))).Rows.Count == 1)
                {
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لا يوجد بيانات لهذا العنصر ", "!! تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

            }
        }
    }
}