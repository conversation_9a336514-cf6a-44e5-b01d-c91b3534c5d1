﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="09/28/2022 22:45:37" ReportInfo.Modified="10/01/2022 23:15:57" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }


    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
      if(((String)Report.GetParameterValue(&quot;CUST_NAME&quot;)) != &quot;&quot;)
      {
        txtCN.Visible = true;
        txtCNAME.Visible = true;
      }
      else
      {
        txtCN.Visible = false;
        txtCNAME.Visible = false;
      }
      if(((String)Report.GetParameterValue(&quot;DOC_NAME&quot;)) != &quot;&quot;)
      {
        txtDN.Visible = true;
        txtDNAME.Visible = true;
      }
      else
      {
        txtDN.Visible = false;
        txtDNAME.Visible = false;
      }
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHE29Kexr2G35FaNrIEaEHqy">
      <TableDataSource Name="Table" Alias="APOLIST_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        APO_TBL.APO_ID, APO_TBL.APO_CODE,convert(varchar ,APO_TBL.APO_DATE, 103) as APO_DATE,convert(varchar(5) , APO_TBL.APO_TIME, 108) as APO_TIME, APO_TBL.APO_NAME, APO_TBL.APO_NOTE, APO_TBL.CLI_ID, APO_TBL.CUST_ID, APO_TBL.DOC_ID, APO_TBL.VIS_ID, &#13;&#10;                         CLINC_TBL.CLI_NAME, DOCTORS_TBL.DOC_NAME, DOCTORS_TBL.DOC_MAJOR, DOCTORS_TBL.DOC_MOBILE, DOCTORS_TBL.DOC_ADDRESS, CLINIC_TITLE_TBL.CLI_T_ID, CLINIC_TITLE_TBL.CLI_T_NAME, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_TITLE, CLINIC_TITLE_TBL.CLI_T_ADDRESS, CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE, CUST_TBL.CUST_AGE, CUST_TBL.CUST_BD, CUST_TBL.CUST_MOBILE1, &#13;&#10;                         CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_GENDER, CUST_TBL.CUST_NATION, CUST_TBL.CUST_AGE_MONTH, VISIT_TBL.VIS_NAME, VISIT_TBL.VIS_DATE, VISIT_TBL.VIS_TIME, &#13;&#10;                         CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME&#13;&#10;FROM            APO_TBL INNER JOIN&#13;&#10;                         CLINC_TBL ON APO_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         CLINIC_TITLE_TBL ON APO_TBL.CLI_ID = CLINIC_TITLE_TBL.CLI_ID INNER JOIN&#13;&#10;                         CUST_TBL ON APO_TBL.CUST_ID = CUST_TBL.CUST_ID INNER JOIN&#13;&#10;                         DOCTORS_TBL ON APO_TBL.DOC_ID = DOCTORS_TBL.DOC_ID LEFT OUTER JOIN&#13;&#10;                         VISIT_TBL ON APO_TBL.VIS_ID = VISIT_TBL.VIS_ID &#13;&#10;WHERE        (APO_TBL.APO_DATE BETWEEN @F_DATE AND @S_DATE) AND (DOCTORS_TBL.DOC_NAME LIKE '%' + @DOC_NAME + '%') AND &#13;&#10;                         (CUST_TBL.CUST_F_NAME + CUST_TBL.CUST_S_NAME + CUST_TBL.CUST_T_NAME + CUST_TBL.CUST_L_NAME LIKE '%' + REPLACE(@CUST_NAME, ' ', '%') + '%') AND (APO_TBL.CLI_ID = @CLI_ID)">
        <Column Name="APO_ID" DataType="System.Decimal"/>
        <Column Name="APO_CODE" DataType="System.Decimal"/>
        <Column Name="APO_DATE" DataType="System.String"/>
        <Column Name="APO_TIME" DataType="System.String"/>
        <Column Name="APO_NAME" DataType="System.String"/>
        <Column Name="APO_NOTE" DataType="System.String"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="DOC_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="DOC_NAME" DataType="System.String"/>
        <Column Name="DOC_MAJOR" DataType="System.String"/>
        <Column Name="DOC_MOBILE" DataType="System.String"/>
        <Column Name="DOC_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_ID" DataType="System.Decimal"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="VIS_NAME" DataType="System.String"/>
        <Column Name="VIS_DATE" DataType="System.DateTime"/>
        <Column Name="VIS_TIME" DataType="System.TimeSpan"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <CommandParameter Name="F_DATE" DataType="22" Size="200" Expression="[F_DATE]"/>
        <CommandParameter Name="S_DATE" DataType="22" Size="200" Expression="[S_DATE]"/>
        <CommandParameter Name="CUST_NAME" DataType="22" Size="200" Expression="[CUST_NAME]" DefaultValue="0"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="0"/>
        <CommandParameter Name="DOC_NAME" DataType="22" Size="200" Expression="[DOC_NAME]" DefaultValue="0"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="F_DATE" DataType="System.String"/>
    <Parameter Name="S_DATE" DataType="System.String"/>
    <Parameter Name="CUST_NAME" DataType="System.String"/>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="DOC_NAME" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
    <Total Name="APO_COUNT" TotalType="Count" Evaluator="Data1" PrintOn="ReportSummary1"/>
  </Dictionary>
  <ReportPage Name="Page1" Landscape="true" PaperWidth="297" PaperHeight="210" RawPaperSize="9" StartPageEvent="Page1_StartPage">
    <ReportTitleBand Name="ReportTitle1" Width="1047.06" Height="292.95">
      <PictureObject Name="Picture1" Left="3.78" Width="226.8" Height="189" SizeMode="StretchImage"/>
      <PictureObject Name="Picture2" Left="788.13" Width="255.15" Height="179.55" SizeMode="StretchImage"/>
      <TextObject Name="Text35" Left="334.53" Top="170.1" Width="340.2" Height="28.35" Text="قائمة المواعيد" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Underline"/>
      <LineObject Name="Line1" Left="28.35" Top="207.9" Width="982.8"/>
      <TextObject Name="Text43" Left="52.92" Top="217.35" Width="198.45" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": تاريخ الطباعة" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text22" Left="52.92" Top="236.25" Width="198.45" Height="28.35" Border.Lines="All" Fill.Color="White" Text="[Date]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text36" Left="255.15" Top="18.9" Width="510.3" Height="56.7" Text="[APOLIST_REPORT.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt"/>
      <TextObject Name="Text37" Left="255.15" Top="75.6" Width="510.3" Height="47.25" Text="[APOLIST_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <TextObject Name="Text45" Left="718.2" Top="217.35" Width="207.9" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[F_DATE]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text18" Left="718.2" Top="245.7" Width="207.9" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[S_DATE]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtCNAME" Left="330.75" Top="217.35" Width="283.5" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[CUST_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtDNAME" Left="330.75" Top="245.7" Width="283.5" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[DOC_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text21" Left="926.1" Top="217.35" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": من تاريخ" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text40" Left="926.1" Top="245.7" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": الى تاريخ" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtCN" Left="614.25" Top="217.35" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم المريض" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtDN" Left="614.25" Top="245.7" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم الطبيب" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="296.28" Width="1047.06" Height="18.9">
      <TextObject Name="Text2" Left="973.35" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="رقم الموعد" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text4" Left="869.4" Width="103.95" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="تاريخ الموعد" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text6" Left="774.9" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="وقت الموعد" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text8" Left="699.3" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="رقم المريض" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text10" Left="500.85" Width="198.45" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="اسم المريض" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text12" Left="255.15" Width="245.7" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="رقم الهاتف" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text14" Left="75.6" Width="179.55" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="اسم الطبيب" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text16" Left="9.45" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="رقم الزيارة" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
    </PageHeaderBand>
    <DataBand Name="Data1" Top="318.52" Width="1047.06" Height="18.9" DataSource="Table" KeepTogether="true" KeepDetail="true">
      <TextObject Name="Text1" Left="973.35" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="WhiteSmoke" Text="[APOLIST_REPORT.APO_CODE]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text3" Left="869.4" Width="103.95" Height="18.9" Border.Lines="All" Fill.Color="WhiteSmoke" Text="[APOLIST_REPORT.APO_DATE]" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text5" Left="774.9" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="WhiteSmoke" Text="[APOLIST_REPORT.APO_TIME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text7" Left="699.3" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="WhiteSmoke" Text="[APOLIST_REPORT.CUST_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text9" Left="500.85" Width="198.45" Height="18.9" Border.Lines="All" Fill.Color="WhiteSmoke" Text="[APOLIST_REPORT.CUST_NAME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text11" Left="255.15" Width="245.7" Height="18.9" Border.Lines="All" Fill.Color="WhiteSmoke" Text="[APOLIST_REPORT.CUST_MOBILE2] / [APOLIST_REPORT.CUST_MOBILE1]" HorzAlign="Right" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text13" Left="75.6" Width="179.55" Height="18.9" Border.Lines="All" Fill.Color="WhiteSmoke" Text="[APOLIST_REPORT.DOC_NAME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text15" Left="9.45" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="WhiteSmoke" Text="[APOLIST_REPORT.VIS_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
    </DataBand>
    <ReportSummaryBand Name="ReportSummary1" Top="340.75" Width="1047.06" Height="37.8">
      <TextObject Name="Text17" Left="774.9" Width="170.1" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="[APO_COUNT]" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text44" Left="945" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text=": عدد المواعيد" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </ReportSummaryBand>
    <PageFooterBand Name="PageFooter1" Top="381.88" Width="1047.06" Height="94.5">
      <TextObject Name="Text46" Left="9.45" Top="18.9" Width="926.1" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APOLIST_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="935.55" Top="18.9" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text41" Left="9.45" Top="47.25" Width="926.1" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APOLIST_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="935.55" Top="47.25" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
