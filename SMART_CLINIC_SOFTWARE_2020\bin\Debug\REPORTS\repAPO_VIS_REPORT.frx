﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="05/04/2021 15:20:32" ReportInfo.Modified="01/01/2023 17:25:54" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHEOjWCJ2luoIJ3FarB24sHA">
      <TableDataSource Name="Table" Alias="APO_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        APO_TBL.APO_ID, APO_TBL.APO_CODE,convert(varchar , APO_TBL.APO_DATE, 103) as APO_DATE,convert(varchar(5) , APO_TBL.APO_TIME, 108) as APO_TIME, APO_TBL.APO_NAME, APO_TBL.APO_NOTE, APO_TBL.CLI_ID, &#13;&#10;                         APO_TBL.CUST_ID, APO_TBL.DOC_ID, APO_TBL.VIS_ID, CLINC_TBL.CLI_NAME, DOCTORS_TBL.DOC_CODE, DOCTORS_TBL.DOC_NAME, &#13;&#10;                         DOCTORS_TBL.DOC_MOBILE, DOCTORS_TBL.DOC_ADDRESS, CUST_TBL.CUST_CODE, CUST_TBL.CUST_AGE, CUST_TBL.CUST_BD, CUST_TBL.CUST_MOBILE1, &#13;&#10;                         CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_GENDER, CUST_TBL.CUST_NATION, CUST_TBL.CUST_AGE_MONTH, &#13;&#10;                         CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_ID, CLINIC_TITLE_TBL.CLI_T_NAME, CLINIC_TITLE_TBL.CLI_T_TITLE, CLINIC_TITLE_TBL.CLI_T_ADDRESS, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE&#13;&#10;FROM            APO_TBL INNER JOIN&#13;&#10;                         CLINC_TBL ON APO_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         CUST_TBL ON APO_TBL.CUST_ID = CUST_TBL.CUST_ID INNER JOIN&#13;&#10;                         DOCTORS_TBL ON APO_TBL.DOC_ID = DOCTORS_TBL.DOC_ID INNER JOIN&#13;&#10;                         CLINIC_TITLE_TBL ON APO_TBL.CLI_ID = CLINIC_TITLE_TBL.CLI_ID&#13;&#10;WHERE        (APO_TBL.CUST_ID = @CUST_ID) AND (APO_TBL.CLI_ID = @CLI_ID) AND (APO_TBL.APO_CODE = @APO_CODE) AND (APO_TBL.VIS_ID = @VIS_ID)">
        <Column Name="APO_ID" DataType="System.Decimal"/>
        <Column Name="APO_CODE" DataType="System.Decimal"/>
        <Column Name="APO_DATE" DataType="System.DateTime"/>
        <Column Name="APO_TIME" DataType="System.TimeSpan"/>
        <Column Name="APO_NAME" DataType="System.String"/>
        <Column Name="APO_NOTE" DataType="System.String"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="DOC_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="DOC_CODE" DataType="System.Decimal"/>
        <Column Name="DOC_NAME" DataType="System.String"/>
        <Column Name="DOC_MOBILE" DataType="System.String"/>
        <Column Name="DOC_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_CODE" DataType="System.Decimal"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <Column Name="CLI_T_ID" DataType="System.Decimal"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="0"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="0"/>
        <CommandParameter Name="APO_CODE" DataType="22" Size="200" Expression="[APO_CODE]" DefaultValue="0"/>
        <CommandParameter Name="VIS_ID" DataType="22" Size="200" Expression="[VIS_ID]" DefaultValue="0"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="CUST_ID" DataType="System.String"/>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="VIS_ID" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
    <Parameter Name="APO_CODE" DataType="System.String"/>
  </Dictionary>
  <ReportPage Name="Page1" PaperHeight="150" LeftMargin="0" TopMargin="0" RightMargin="0" BottomMargin="0" FirstPageSource="15" OtherPagesSource="15" StartPageEvent="Page1_StartPage">
    <ReportTitleBand Name="ReportTitle1" Width="793.8" Height="425.25">
      <ShapeObject Name="Shape1" Width="793.8" Height="179.55" Fill="Glass" Fill.Color="White" Fill.Blend="0.2" Fill.Hatch="true" Shape="RoundRectangle"/>
      <TextObject Name="Text36" Left="151.2" Top="18.9" Width="491.4" Height="47.25" Text="[APO_REPORT.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt"/>
      <TextObject Name="Text37" Left="151.2" Top="66.15" Width="491.4" Height="47.25" Text="[APO_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <PictureObject Name="Picture1" Left="9.45" Top="18.9" Width="132.3" Height="141.75" SizeMode="StretchImage" Image=""/>
      <PictureObject Name="Picture2" Left="652.05" Top="18.9" Width="132.3" Height="141.75" SizeMode="StretchImage" Image=""/>
      <TextObject Name="Text35" Left="217.35" Top="151.2" Width="340.2" Height="28.35" Text="بطاقة موعد مراجعة" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold"/>
      <LineObject Name="Line1" Left="9.45" Top="189" Width="774.9"/>
      <TextObject Name="Text12" Left="623.7" Top="198.45" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.CUST_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text13" Left="387.45" Top="198.45" Width="236.25" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.CUST_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text15" Left="217.35" Top="198.45" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.CUST_GENDER]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text16" Left="18.9" Top="198.45" Width="151.2" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.CUST_AGE_MONTH] / [APO_REPORT.CUST_AGE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text17" Left="689.85" Top="198.45" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="224, 224, 224" Text=": اسم المريض" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text41" Left="311.85" Top="198.45" Width="56.7" Height="18.9" Border.Lines="All" Fill.Color="224, 224, 224" Text=": الجنس" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text42" Left="170.1" Top="198.45" Width="47.25" Height="18.9" Border.Lines="All" Fill.Color="224, 224, 224" Text=": العمر" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text14" Left="387.45" Top="226.8" Width="302.4" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.CLI_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text40" Left="689.85" Top="226.8" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="224, 224, 224" Text=": اسم العيادة" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line2" Left="9.45" Top="349.65" Width="774.9"/>
      <TextObject Name="Text1" Left="387.45" Top="255.15" Width="302.4" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.DOC_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text43" Left="689.85" Top="255.15" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="224, 224, 224" Text=": اسم الطبيب" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text2" Left="387.45" Top="283.5" Width="302.4" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.APO_DATE]" Format="Date" Format.Format="d" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text3" Left="387.45" Top="321.3" Width="302.4" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.APO_TIME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text44" Left="689.85" Top="283.5" Width="85.05" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": تاريخ الموعد" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text45" Left="689.85" Top="321.3" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="224, 224, 224" Text=": وقت الموعد" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text4" Left="18.9" Top="226.8" Width="349.65" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.VIS_ID] : رقم الزيارة" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text5" Left="18.9" Top="283.5" Width="255.15" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[Date]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text52" Left="274.05" Top="283.5" Width="94.5" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": تاريخ الطباعة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text53" Left="18.9" Top="396.9" Width="245.7" Height="28.35" Text="[APO_REPORT.DOC_NAME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text46" Left="85.05" Top="359.1" Width="122.85" Height="28.35" Text="أسم و توقيع الطبيب" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text6" Left="18.9" Top="255.15" Width="255.15" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.APO_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text56" Left="274.05" Top="255.15" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="224, 224, 224" Text=": نوع الجلسة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </ReportTitleBand>
    <PageFooterBand Name="PageFooter1" Top="428.58" Width="793.8" Height="85.05">
      <ShapeObject Name="Shape2" Width="793.8" Height="75.6" Shape="RoundRectangle"/>
      <TextObject Name="Text55" Left="18.9" Top="37.8" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="670.95" Top="37.8" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="670.95" Top="9.45" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text54" Left="18.9" Top="9.45" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[APO_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
