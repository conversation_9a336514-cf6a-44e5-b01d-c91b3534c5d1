﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="08/07/2021 12:35:06" ReportInfo.Modified="10/11/2022 22:12:10" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection1" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHH7ZaixvxfnfUUaRqboyJSV">
      <TableDataSource Name="Table" Alias="DIGLIST_VIS_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        DIGLIST_TBL.DIGLIST_ID, DIGLIST_TBL.DIGLIST_CODE, DIGLIST_TBL.DIGLIST_NAME, DIGLIST_TBL.DIGLIST_DATE, DIGLIST_TBL.DIGLIST_TIME, &#13;&#10;                         DIGLIST_TBL.DIG_ID, DIGLIST_TBL.CUST_ID, DIGLIST_TBL.VIS_ID, DIGLIST_TBL.CLI_ID, DIAGNOIS_TBL.DIG_CODE, DIAGNOIS_TBL.DIG_NAME, &#13;&#10;                         DIAGNOIS_TBL.DIG_TYPE, DIAGNOIS_TBL.DIG_NOTE&#13;&#10;FROM            DIGLIST_TBL INNER JOIN&#13;&#10;                         DIAGNOIS_TBL ON DIGLIST_TBL.DIG_ID = DIAGNOIS_TBL.DIG_ID&#13;&#10;WHERE&#13;&#10; DIGLIST_TBL.CUST_ID= @CUST_ID&#13;&#10;AND&#13;&#10; DIGLIST_TBL.CLI_ID= @CLI_ID&#13;&#10;AND&#13;&#10; DIGLIST_TBL.VIS_ID= @VIS_ID">
        <Column Name="DIGLIST_ID" DataType="System.Decimal"/>
        <Column Name="DIGLIST_CODE" DataType="System.Decimal"/>
        <Column Name="DIGLIST_NAME" DataType="System.String"/>
        <Column Name="DIGLIST_DATE" DataType="System.DateTime"/>
        <Column Name="DIGLIST_TIME" DataType="System.TimeSpan"/>
        <Column Name="DIG_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="DIG_CODE" DataType="System.Decimal"/>
        <Column Name="DIG_NAME" DataType="System.String"/>
        <Column Name="DIG_TYPE" DataType="System.String"/>
        <Column Name="DIG_NOTE" DataType="System.String"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="1"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="11"/>
        <CommandParameter Name="VIS_ID" DataType="22" Size="200" Expression="[CUST_PAST_HISTORY_REPORT.VIS_ID]" DefaultValue="4"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <MsSqlDataConnection Name="Connection2" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHFvCF1BGCnWMBoO85GpaK5m">
      <TableDataSource Name="Table1" Alias="SERLIST_VIS_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        SERLIST_TBL.SERLIST_ID, SERLIST_TBL.SERLIST_CODE, SERLIST_TBL.SERLIST_NAME, SERLIST_TBL.SERLIST_DATE, SERLIST_TBL.SERLIST_TIME, &#13;&#10;                         SERLIST_TBL.SER_PRICE_TOTAL, SERLIST_TBL.SERLIST_NOTE, SERLIST_TBL.SER_ID, SERLIST_TBL.CLI_ID, SERLIST_TBL.CUST_ID, SERLIST_TBL.VIS_ID, &#13;&#10;                         SERVICE_TBL.SER_CODE, SERVICE_TBL.SER_NAME, SERVICE_TBL.SER_TYPE, SERVICE_TBL.SER_PRICE, SERVICE_TBL.SER_NOTE&#13;&#10;FROM            SERLIST_TBL INNER JOIN&#13;&#10;                         SERVICE_TBL ON SERLIST_TBL.SER_ID = SERVICE_TBL.SER_ID&#13;&#10;WHERE&#13;&#10;SERLIST_TBL.CUST_ID = @CUST_ID&#13;&#10;AND&#13;&#10;SERLIST_TBL.CLI_ID = @CLI_ID&#13;&#10;AND&#13;&#10;SERLIST_TBL.VIS_ID = @VIS_ID">
        <Column Name="SERLIST_ID" DataType="System.Decimal"/>
        <Column Name="SERLIST_CODE" DataType="System.Decimal"/>
        <Column Name="SERLIST_NAME" DataType="System.String"/>
        <Column Name="SERLIST_DATE" DataType="System.DateTime"/>
        <Column Name="SERLIST_TIME" DataType="System.TimeSpan"/>
        <Column Name="SER_PRICE_TOTAL" DataType="System.Decimal"/>
        <Column Name="SERLIST_NOTE" DataType="System.String"/>
        <Column Name="SER_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="SER_CODE" DataType="System.Decimal"/>
        <Column Name="SER_NAME" DataType="System.String"/>
        <Column Name="SER_TYPE" DataType="System.String"/>
        <Column Name="SER_PRICE" DataType="System.Decimal"/>
        <Column Name="SER_NOTE" DataType="System.String"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="11"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="1"/>
        <CommandParameter Name="VIS_ID" DataType="22" Size="200" Expression="[CUST_PAST_HISTORY_REPORT.VIS_ID]" DefaultValue="4"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <MsSqlDataConnection Name="Connection3" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHEoqZJOqv9y7BhI8eZni9tc">
      <TableDataSource Name="Table2" Alias="MEDLIST_VIS_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        MEDLIST_TBL.MEDLIST_ID, MEDLIST_TBL.MEDLIST_CODE, MEDLIST_TBL.MEDLIST_NAME, MEDLIST_TBL.MEDLIST_DATE, MEDLIST_TBL.MEDLIST_TIME, &#13;&#10;                         MEDLIST_TBL.DOS_NAME, MEDLIST_TBL.MED_ID, MEDLIST_TBL.CUST_ID, MEDLIST_TBL.VIS_ID, MEDLIST_TBL.MED_SOURSE, MEDLIST_TBL.CLI_ID, &#13;&#10;                         MEDCIN_TBL.MED_CODE, MEDCIN_TBL.MED_NAME, MEDCIN_TBL.MED_S_NAME, MEDCIN_TBL.MED_SOURSE AS Expr1, MEDCIN_TBL.MED_PRICE&#13;&#10;FROM            MEDLIST_TBL INNER JOIN&#13;&#10;                         MEDCIN_TBL ON MEDLIST_TBL.MED_ID = MEDCIN_TBL.MED_ID&#13;&#10;WHERE&#13;&#10;MEDLIST_TBL.CUST_ID = @CUST_ID&#13;&#10;AND&#13;&#10;MEDLIST_TBL.CLI_ID = @CLI_ID&#13;&#10;AND&#13;&#10;MEDLIST_TBL.VIS_ID = @VIS_ID">
        <Column Name="MEDLIST_ID" DataType="System.Decimal"/>
        <Column Name="MEDLIST_CODE" DataType="System.Decimal"/>
        <Column Name="MEDLIST_NAME" DataType="System.String"/>
        <Column Name="MEDLIST_DATE" DataType="System.DateTime"/>
        <Column Name="MEDLIST_TIME" DataType="System.TimeSpan"/>
        <Column Name="DOS_NAME" DataType="System.String"/>
        <Column Name="MED_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="MED_SOURSE" DataType="System.String"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="MED_CODE" DataType="System.Decimal"/>
        <Column Name="MED_NAME" DataType="System.String"/>
        <Column Name="MED_S_NAME" DataType="System.String"/>
        <Column Name="Expr1" DataType="System.String"/>
        <Column Name="MED_PRICE" DataType="System.Decimal"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="11"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="1"/>
        <CommandParameter Name="VIS_ID" DataType="22" Size="200" Expression="[CUST_PAST_HISTORY_REPORT.VIS_ID]" DefaultValue="4"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <MsSqlDataConnection Name="Connection4" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHH4crqzew0ZhXodBXxQrkwW">
      <TableDataSource Name="Table3" Alias="MEDREQ_VIS_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        MEDREQ_TBL.MEDREQ_ID, MEDREQ_TBL.MEDREQ_CODE, MEDREQ_TBL.MEDREQ_NAME, MEDREQ_TBL.MEDREQ_DATE, MEDREQ_TBL.MEDREQ_TIME, &#13;&#10;                         MEDREQ_TBL.MEDREQ_RESULT, MEDREQ_TBL.MEDREQ_NOTE, MEDREQ_TBL.MEDCHEK_ID, MEDREQ_TBL.CUST_ID, MEDREQ_TBL.CLI_ID, &#13;&#10;                         MEDREQ_TBL.VIS_ID, MEDREQ_TBL.MEDREQ_STATE, MEDCHEK_TBL.MEDCHEK_CODE, MEDCHEK_TBL.MEDCHEK_NAME, MEDCHEK_TBL.MEDCHEK_TYPE, &#13;&#10;                         MEDCHEK_TBL.MEDCHEK_PRICE, MEDCHEK_TBL.MEDCHEK_NOTE&#13;&#10;FROM            MEDREQ_TBL INNER JOIN&#13;&#10;                         MEDCHEK_TBL ON MEDREQ_TBL.MEDCHEK_ID = MEDCHEK_TBL.MEDCHEK_ID&#13;&#10;WHERE&#13;&#10;MEDREQ_TBL.CUST_ID = @CUST_ID&#13;&#10;AND&#13;&#10;MEDREQ_TBL.CLI_ID = @CLI_ID&#13;&#10;AND&#13;&#10;MEDREQ_TBL.VIS_ID = @VIS_ID">
        <Column Name="MEDREQ_ID" DataType="System.Decimal"/>
        <Column Name="MEDREQ_CODE" DataType="System.Decimal"/>
        <Column Name="MEDREQ_NAME" DataType="System.String"/>
        <Column Name="MEDREQ_DATE" DataType="System.DateTime"/>
        <Column Name="MEDREQ_TIME" DataType="System.TimeSpan"/>
        <Column Name="MEDREQ_RESULT" DataType="System.String"/>
        <Column Name="MEDREQ_NOTE" DataType="System.String"/>
        <Column Name="MEDCHEK_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="MEDREQ_STATE" DataType="System.String"/>
        <Column Name="MEDCHEK_CODE" DataType="System.Decimal"/>
        <Column Name="MEDCHEK_NAME" DataType="System.String"/>
        <Column Name="MEDCHEK_TYPE" DataType="System.String"/>
        <Column Name="MEDCHEK_PRICE" DataType="System.Decimal"/>
        <Column Name="MEDCHEK_NOTE" DataType="System.String"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="11"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="1"/>
        <CommandParameter Name="VIS_ID" DataType="22" Size="200" Expression="[CUST_PAST_HISTORY_REPORT.VIS_ID]" DefaultValue="4"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <MsSqlDataConnection Name="Connection5" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHEFhZFy2SSX+rFsdjH9GRZb">
      <TableDataSource Name="Table4" Alias="APO_VIS_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        APO_ID, APO_CODE, APO_DATE, APO_TIME, APO_NAME, APO_NOTE, CLI_ID, CUST_ID, DOC_ID, VIS_ID&#13;&#10;FROM            APO_TBL&#13;&#10;WHERE&#13;&#10;APO_TBL.CUST_ID = @CUST_ID&#13;&#10;AND&#13;&#10;APO_TBL.CLI_ID = @CLI_ID&#13;&#10;AND&#13;&#10;APO_TBL.VIS_ID = @VIS_ID">
        <Column Name="APO_ID" DataType="System.Decimal"/>
        <Column Name="APO_CODE" DataType="System.Decimal"/>
        <Column Name="APO_DATE" DataType="System.DateTime"/>
        <Column Name="APO_TIME" DataType="System.TimeSpan"/>
        <Column Name="APO_NAME" DataType="System.String"/>
        <Column Name="APO_NOTE" DataType="System.String"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="DOC_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="11"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="1"/>
        <CommandParameter Name="VIS_ID" DataType="22" Size="200" Expression="[CUST_PAST_HISTORY_REPORT.VIS_ID]" DefaultValue="4"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <MsSqlDataConnection Name="Connection6" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHGbhuV2BcRPwPb6pQiYTEpW">
      <TableDataSource Name="Table5" Alias="DES_CUST_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        DES_ID, DES_CODE, DES_NAME, DES_TYPE, DES_NOTE, DES_STATE, CLI_ID, CUST_ID&#13;&#10;FROM            DES_TBL&#13;&#10;WHERE&#13;&#10;DES_TBL.CUST_ID = @CUST_ID&#13;&#10;AND&#13;&#10;DES_TBL.CLI_ID = @CLI_ID">
        <Column Name="DES_ID" DataType="System.Decimal"/>
        <Column Name="DES_CODE" DataType="System.Decimal"/>
        <Column Name="DES_NAME" DataType="System.String"/>
        <Column Name="DES_TYPE" DataType="System.String"/>
        <Column Name="DES_NOTE" DataType="System.String"/>
        <Column Name="DES_STATE" DataType="System.String"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="11"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="1"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHFv4BlwxuFki0WvkQksekjz">
      <TableDataSource Name="Table7" Alias="CUST_PAST_HISTORY_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        VISIT_TBL.VIS_ID, VISIT_TBL.VIS_CODE, VISIT_TBL.VIS_NAME, convert(varchar , VISIT_TBL.VIS_DATE, 103) as VIS_DATE, convert(varchar(5) , VISIT_TBL.VIS_TIME, 108) as VIS_TIME, VISIT_TBL.VIS_TYPE, VISIT_TBL.CUST_ID, &#13;&#10;                         VISIT_TBL.CLI_ID, VISIT_TBL.DOC_ID, VISIT_TBL.VIS_PRICE, VISIT_TBL.VIS_DISCOUNT, VISIT_TBL.VIS_TOTAL, VISIT_TBL.VIS_NOTE, VISIT_TBL.VIS_PAY_TYPE, &#13;&#10;                         VISIT_TBL.VIS_UNPAY, CLINC_TBL.CLI_CODE, CLINC_TBL.CLI_NAME, CUST_TBL.CUST_CODE, CUST_TBL.CUST_AGE, CUST_TBL.CUST_BD, &#13;&#10;                         CUST_TBL.CUST_MOBILE1, CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_SAVE_STATE, CUST_TBL.CUST_GENDER, &#13;&#10;                         CUST_TBL.CARD_ID, CUST_TBL.CUST_NATION, CUST_TBL.CUST_AGE_MONTH, &#13;&#10;                         CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_NAME, CLINIC_TITLE_TBL.CLI_T_TITLE, CLINIC_TITLE_TBL.CLI_T_ADDRESS, CLINIC_TITLE_TBL.CLI_T_MOBILE, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_NOTE, DOCTORS_TBL.DOC_NAME, DOCTORS_TBL.DOC_MOBILE&#13;&#10;FROM            VISIT_TBL INNER JOIN&#13;&#10;                         CLINC_TBL ON VISIT_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         CUST_TBL ON VISIT_TBL.CUST_ID = CUST_TBL.CUST_ID INNER JOIN&#13;&#10;                         CLINIC_TITLE_TBL ON CLINC_TBL.CLI_ID = CLINIC_TITLE_TBL.CLI_ID  INNER JOIN&#13;&#10;                         DOCTORS_TBL ON VISIT_TBL.DOC_ID = DOCTORS_TBL.DOC_ID&#13;&#10;WHERE&#13;&#10;VISIT_TBL.CLI_ID = @CLI_ID&#13;&#10;AND&#13;&#10; VISIT_TBL.CUST_ID = @CUST_ID&#13;&#10;ORDER BY VIS_ID DESC">
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="VIS_CODE" DataType="System.Decimal"/>
        <Column Name="VIS_NAME" DataType="System.String"/>
        <Column Name="VIS_DATE" DataType="System.DateTime"/>
        <Column Name="VIS_TIME" DataType="System.TimeSpan"/>
        <Column Name="VIS_TYPE" DataType="System.String"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="DOC_ID" DataType="System.Decimal"/>
        <Column Name="VIS_PRICE" DataType="System.Decimal"/>
        <Column Name="VIS_DISCOUNT" DataType="System.Decimal"/>
        <Column Name="VIS_TOTAL" DataType="System.Decimal"/>
        <Column Name="VIS_NOTE" DataType="System.String"/>
        <Column Name="VIS_PAY_TYPE" DataType="System.String"/>
        <Column Name="VIS_UNPAY" DataType="System.Decimal"/>
        <Column Name="CLI_CODE" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="CUST_CODE" DataType="System.Decimal"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_SAVE_STATE" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CARD_ID" DataType="System.Decimal"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <Column Name="DOC_NAME" DataType="System.String"/>
        <Column Name="DOC_MOBILE" DataType="System.String"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="1"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="11"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="CUST_ID" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
  </Dictionary>
  <ReportPage Name="Page1" Landscape="true" PaperWidth="297" PaperHeight="210" RawPaperSize="9" LeftMargin="0" TopMargin="0" RightMargin="0" FirstPageSource="15" OtherPagesSource="15" StartPageEvent="Page1_StartPage">
    <ReportTitleBand Name="ReportTitle1" Width="1122.66" Height="189">
      <TextObject Name="Text36" Left="202.23" Top="9.45" Width="708.75" Height="56.7" Text="[CUST_PAST_HISTORY_REPORT.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt"/>
      <PictureObject Name="Picture1" Left="32.13" Top="9.45" Width="151.2" Height="141.75" SizeMode="StretchImage" Image=""/>
      <PictureObject Name="Picture2" Left="939.33" Top="9.45" Width="151.2" Height="141.75" SizeMode="StretchImage" Image=""/>
      <TextObject Name="Text37" Left="202.23" Top="66.15" Width="708.75" Height="47.25" Text="[CUST_PAST_HISTORY_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt"/>
      <TextObject Name="Text35" Left="391.23" Top="151.2" Width="340.2" Height="28.35" Text="بطاقة المريض العلاجية" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Underline"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="192.33" Width="1122.66" Height="75.6">
      <TextObject Name="Text12" Left="935.55" Top="18.9" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[CUST_PAST_HISTORY_REPORT.CUST_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text13" Left="699.3" Top="18.9" Width="236.25" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[CUST_PAST_HISTORY_REPORT.CUST_NAME]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text15" Left="510.3" Top="18.9" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[CUST_PAST_HISTORY_REPORT.CUST_GENDER]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text16" Left="359.1" Top="18.9" Width="103.95" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[CUST_PAST_HISTORY_REPORT.CUST_AGE_MONTH]/ [CUST_PAST_HISTORY_REPORT.CUST_AGE]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text17" Left="1001.7" Top="18.9" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم المريض" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text41" Left="604.8" Top="18.9" Width="56.7" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": الجنس" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text42" Left="463.05" Top="18.9" Width="47.25" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": العمر" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text14" Left="699.3" Top="47.25" Width="302.4" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[CUST_PAST_HISTORY_REPORT.CLI_NAME]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text40" Left="1001.7" Top="47.25" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم العيادة" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text21" Left="453.6" Top="47.25" Width="122.85" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[CUST_PAST_HISTORY_REPORT.VIS_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text45" Left="576.45" Top="47.25" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": رقم الزيارة" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text47" Left="264.6" Top="18.9" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": رقم الهاتف" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text1" Left="28.35" Top="18.9" Width="226.8" Height="18.9" Border.Lines="All" Text="[CUST_PAST_HISTORY_REPORT.CUST_MOBILE1] / [CUST_PAST_HISTORY_REPORT.CUST_MOBILE2]" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text48" Left="264.6" Top="47.25" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": تاريخ الميلاد" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text2" Left="28.35" Top="47.25" Width="226.8" Height="18.9" Border.Lines="All" Text="[CUST_PAST_HISTORY_REPORT.CUST_BD]" Format="Date" Format.Format="d" HorzAlign="Right" Font="Arial, 10pt, style=Bold"/>
      <LineObject Name="Line2" Left="8.51" Top="75.6" Width="1096.2" Border.Width="2"/>
      <LineObject Name="Line1" Left="8.51" Width="1096.2" Border.Width="2"/>
    </PageHeaderBand>
    <DataBand Name="Data2" Top="331.3" Width="1122.66" Height="18.9" DataSource="Table5" KeepTogether="true" KeepDetail="true">
      <TextObject Name="Text4" Left="756" Width="330.75" Height="18.9" Border.Lines="All" Text="[DES_CUST_REPORT.DES_NAME]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text6" Left="595.35" Width="160.65" Height="18.9" Border.Lines="All" Text="[DES_CUST_REPORT.DES_TYPE]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text8" Left="472.5" Width="122.85" Height="18.9" Border.Lines="All" Text="[DES_CUST_REPORT.DES_STATE]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text10" Left="28.35" Width="444.15" Height="18.9" Border.Lines="All" Text="[DES_CUST_REPORT.DES_NOTE]" HorzAlign="Right" Font="Arial, 11pt"/>
      <DataHeaderBand Name="DataHeader1" Top="271.27" Width="1122.66" Height="56.7" KeepWithData="true">
        <TextObject Name="Text5" Left="756" Top="37.8" Width="330.75" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="اسم المرض / الملاحظ" HorzAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text7" Left="595.35" Top="37.8" Width="160.65" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="نوع المرض / الملاحظ" HorzAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text9" Left="472.5" Top="37.8" Width="122.85" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="حالة المرض" HorzAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text11" Left="28.35" Top="37.8" Width="444.15" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="الملاحظات" HorzAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text49" Left="391.23" Width="340.2" Height="28.35" Text="الامراض المزمنة و الملاحظات الطبية" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Underline"/>
      </DataHeaderBand>
    </DataBand>
    <GroupHeaderBand Name="GroupHeader1" Top="353.53" Width="1122.66" Height="56.7" StartNewPage="true" KeepWithData="true" RepeatOnEveryPage="true" Condition="[CUST_PAST_HISTORY_REPORT.VIS_ID]" KeepTogether="true">
      <TextObject Name="Text3" Left="992.25" Top="18.9" Width="94.5" Height="28.35" Border.Lines="All" Fill.Color="LightGray" Text="[CUST_PAST_HISTORY_REPORT.VIS_ID] : رقم الزيارة" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <LineObject Name="Line3" Left="9.45" Top="9.45" Width="1096.2" Border.Width="2"/>
      <TextObject Name="Text26" Left="869.4" Top="18.9" Width="122.85" Height="28.35" Border.Lines="All" Fill.Color="LightGray" Text="[CUST_PAST_HISTORY_REPORT.VIS_DATE]" Format="Date" Format.Format="d" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text27" Left="756" Top="18.9" Width="113.4" Height="28.35" Border.Lines="All" Fill.Color="LightGray" Text="[CUST_PAST_HISTORY_REPORT.VIS_TIME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text28" Left="453.6" Top="18.9" Width="302.4" Height="28.35" Border.Lines="All" Fill.Color="LightGray" Text="[CUST_PAST_HISTORY_REPORT.DOC_NAME] : اسم الطبيب" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text30" Left="28.35" Top="18.9" Width="274.05" Height="28.35" Border.Lines="All" Fill.Color="LightGray" Text="[APO_VIS_REPORT.APO_DATE] : تاريخ موعد المراجعة&#13;&#10;" Format="Date" Format.Format="d" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text32" Left="302.4" Top="18.9" Width="151.2" Height="28.35" Border.Lines="All" Fill.Color="LightGray" Text="[CUST_PAST_HISTORY_REPORT.VIS_PRICE] : مبلغ الزيارة" Format="Number" Format.UseLocale="false" Format.DecimalDigits="3" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <DataBand Name="Data1" Top="454.7" Width="1122.66" Height="28.35" KeepTogether="true" KeepDetail="true">
        <SubreportObject Name="SubDIGLIST" Left="916.65" Width="189" Height="28.35" ReportPage="Page2"/>
        <SubreportObject Name="SubMEDLIST" Left="292.95" Width="434.7" Height="28.35" ReportPage="Page4"/>
        <SubreportObject Name="SubSERLIST" Left="727.65" Width="189" Height="28.35" ReportPage="Page3"/>
        <SubreportObject Name="SubMEDREQ" Left="9.45" Width="283.5" Height="28.35" ReportPage="Page5"/>
        <DataHeaderBand Name="DataHeader2" Top="413.57" Width="1122.66" Height="37.8" KeepWithData="true">
          <TextObject Name="Text50" Left="916.65" Top="9.45" Width="189" Height="28.35" Border.Lines="All" Border.Width="2" Fill.Color="LightGray" Text="التشخيص" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text51" Left="727.65" Top="9.45" Width="189" Height="28.35" Border.Lines="All" Border.Width="2" Fill.Color="LightGray" Text="الاجراءات" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text52" Left="292.95" Top="9.45" Width="434.7" Height="28.35" Border.Lines="All" Border.Width="2" Fill.Color="LightGray" Text="العلاجات" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text53" Left="9.45" Top="9.45" Width="283.5" Height="28.35" Border.Lines="All" Border.Width="2" Fill.Color="LightGray" Text="الفحوصات" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        </DataHeaderBand>
      </DataBand>
      <GroupFooterBand Name="GroupFooter1" Top="486.38" Width="1122.66" Height="37.8" KeepWithData="true">
        <TextObject Name="Text29" Left="9.45" Top="9.45" Width="1096.2" Height="28.35" Text="[CUST_PAST_HISTORY_REPORT.VIS_NOTE] : ملاحظات الطبيب" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      </GroupFooterBand>
    </GroupHeaderBand>
    <PageFooterBand Name="PageFooter1" Top="527.52" Width="1122.66" Height="122.85">
      <TextObject Name="Text31" Left="452.66" Top="103.95" Width="160.65" Height="18.9" Text="[Page#] / [TotalPages#]" HorzAlign="Right" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text54" Left="9.45" Top="18.9" Width="992.25" Height="37.8" Border.Lines="All" Fill.Color="255, 255, 192" Text="[CUST_PAST_HISTORY_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="1001.7" Top="18.9" Width="103.95" Height="37.8" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text55" Left="9.45" Top="56.7" Width="992.25" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[CUST_PAST_HISTORY_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="1001.7" Top="56.7" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
  <ReportPage Name="Page2">
    <DataBand Name="Data3" Width="718.2" Height="28.35">
      <TextObject Name="Text18" Width="189" Height="28.35" Border.Lines="All" Border.LeftLine.Width="2" Border.RightLine.Width="2" Border.BottomLine.Width="2" Text="[DIGLIST_VIS_REPORT.DIGLIST_NAME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
    </DataBand>
  </ReportPage>
  <ReportPage Name="Page3">
    <DataBand Name="Data4" Width="718.2" Height="28.35">
      <TextObject Name="Text19" Width="189" Height="28.35" Border.Lines="All" Border.LeftLine.Width="2" Border.RightLine.Width="2" Border.BottomLine.Width="2" Text="[SERLIST_VIS_REPORT.SER_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
    </DataBand>
  </ReportPage>
  <ReportPage Name="Page4">
    <DataBand Name="Data5" Width="718.2" Height="28.35">
      <TextObject Name="Text20" Width="56.7" Height="28.35" Border.Lines="All" Border.LeftLine.Width="2" Border.BottomLine.Width="2" Text="[MEDLIST_VIS_REPORT.MED_SOURSE]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text23" Left="56.7" Width="189" Height="28.35" Border.Lines="All" Border.BottomLine.Width="2" Text="[MEDLIST_VIS_REPORT.DOS_NAME]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text24" Left="245.7" Width="189" Height="28.35" Border.Lines="All" Border.RightLine.Width="2" Border.BottomLine.Width="2" Text="[MEDLIST_VIS_REPORT.MED_NAME]" Font="Arial, 10pt, style=Bold"/>
    </DataBand>
  </ReportPage>
  <ReportPage Name="Page5">
    <DataBand Name="Data6" Width="718.2" Height="28.35">
      <TextObject Name="Text22" Left="132.3" Width="132.3" Height="28.35" Border.Lines="All" Border.RightLine.Width="2" Border.BottomLine.Width="2" Text="[MEDREQ_VIS_REPORT.MEDCHEK_NAME]" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text25" Width="132.3" Height="28.35" Border.Lines="All" Border.LeftLine.Width="2" Border.BottomLine.Width="2" Text="[MEDREQ_VIS_REPORT.MEDREQ_NOTE] / [MEDREQ_VIS_REPORT.MEDREQ_RESULT]" Font="Arial, 10pt, style=Bold"/>
    </DataBand>
  </ReportPage>
</Report>
