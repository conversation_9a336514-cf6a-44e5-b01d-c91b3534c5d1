﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="05/04/2021 02:07:25" ReportInfo.Modified="10/17/2022 22:21:41" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHGScKmexFdWF2GMyLQz2yUB">
      <TableDataSource Name="Table" Alias="MEDLIST_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        MEDLIST_TBL.MEDLIST_ID, MEDLIST_TBL.MEDLIST_CODE, MEDLIST_TBL.MEDLIST_NAME,convert(varchar ,MEDLIST_TBL.MEDLIST_DATE, 103) as MEDLIST_DATE,convert(varchar(5) , MEDLIST_TBL.MEDLIST_TIME, 108) as MEDLIST_TIME, &#13;&#10;                         MEDLIST_TBL.DOS_NAME, MEDLIST_TBL.MED_ID, MEDLIST_TBL.CUST_ID, MEDLIST_TBL.VIS_ID, MEDLIST_TBL.MED_SOURSE, MEDLIST_TBL.CLI_ID, &#13;&#10;                         CLINC_TBL.CLI_NAME, MEDCIN_TBL.MED_NAME, MEDCIN_TBL.MED_S_NAME, MEDCIN_TBL.MED_SOURSE , MEDCIN_TBL.MED_PRICE, &#13;&#10;                         CUST_TBL.CUST_AGE, CUST_TBL.CUST_BD, CUST_TBL.CUST_MOBILE1, CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_GENDER, &#13;&#10;                         CUST_TBL.CUST_NATION, CUST_TBL.CUST_AGE_MONTH, CLINIC_TITLE_TBL.CLI_T_NAME, CLINIC_TITLE_TBL.CLI_T_TITLE, CLINIC_TITLE_TBL.CLI_T_ADDRESS, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE, VISIT_TBL.VIS_NAME, VISIT_TBL.VIS_DATE, VISIT_TBL.VIS_TIME, VISIT_TBL.VIS_TYPE, &#13;&#10;                         CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME&#13;&#10;FROM            MEDLIST_TBL INNER JOIN&#13;&#10;                         CLINC_TBL ON MEDLIST_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         CUST_TBL ON MEDLIST_TBL.CUST_ID = CUST_TBL.CUST_ID INNER JOIN&#13;&#10;                         MEDCIN_TBL ON MEDLIST_TBL.MED_ID = MEDCIN_TBL.MED_ID AND CLINC_TBL.CLI_ID = MEDCIN_TBL.CLI_ID INNER JOIN&#13;&#10;                         CLINIC_TITLE_TBL ON MEDLIST_TBL.CLI_ID = CLINIC_TITLE_TBL.CLI_ID LEFT OUTER JOIN&#13;&#10;                         VISIT_TBL ON MEDLIST_TBL.VIS_ID = VISIT_TBL.VIS_ID&#13;&#10;WHERE&#13;&#10;MEDLIST_TBL.CLI_ID = @CLI_ID&#13;&#10;AND&#13;&#10;MEDLIST_TBL.CUST_ID = @CUST_ID&#13;&#10;&#13;&#10;ORDER BY MEDLIST_TBL.MEDLIST_ID">
        <Column Name="MEDLIST_ID" DataType="System.Decimal"/>
        <Column Name="MEDLIST_CODE" DataType="System.Decimal"/>
        <Column Name="MEDLIST_NAME" DataType="System.String"/>
        <Column Name="MEDLIST_DATE" DataType="System.String"/>
        <Column Name="MEDLIST_TIME" DataType="System.String"/>
        <Column Name="DOS_NAME" DataType="System.String"/>
        <Column Name="MED_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="MED_SOURSE" DataType="System.String"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="MED_NAME" DataType="System.String"/>
        <Column Name="MED_S_NAME" DataType="System.String"/>
        <Column Name="MED_SOURSE1" DataType="System.String"/>
        <Column Name="MED_PRICE" DataType="System.Decimal"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <Column Name="VIS_NAME" DataType="System.String"/>
        <Column Name="VIS_DATE" DataType="System.DateTime"/>
        <Column Name="VIS_TIME" DataType="System.TimeSpan"/>
        <Column Name="VIS_TYPE" DataType="System.String"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="0"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="0"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="CUST_ID" DataType="System.String"/>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
    <Total Name="COUNT_MED" TotalType="Count" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_PRICE" Expression="[MEDLIST_REPORT.MED_PRICE]" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="COUNT_MED_VIS" TotalType="Count" Evaluator="Data1" PrintOn="GroupFooter1"/>
    <Total Name="SUM_PRICE_VIS" Expression="[MEDLIST_REPORT.MED_PRICE]" Evaluator="Data1" PrintOn="GroupFooter1"/>
  </Dictionary>
  <ReportPage Name="Page1" RawPaperSize="9" LeftMargin="0" TopMargin="0" RightMargin="0" BottomMargin="0" StartPageEvent="Page1_StartPage">
    <ReportTitleBand Name="ReportTitle1" Width="793.8" Height="255.15">
      <TextObject Name="Text36" Left="151.2" Top="9.45" Width="491.4" Height="47.25" Text="[MEDLIST_REPORT.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt"/>
      <TextObject Name="Text37" Left="151.2" Top="56.7" Width="491.4" Height="37.8" Text="[MEDLIST_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <PictureObject Name="Picture1" Left="9.45" Top="9.45" Width="132.3" Height="141.75" SizeMode="StretchImage"/>
      <PictureObject Name="Picture2" Left="652.05" Top="9.45" Width="132.3" Height="141.75" SizeMode="StretchImage"/>
      <TextObject Name="Text35" Left="217.35" Top="141.75" Width="340.2" Height="28.35" Text="الوصفة الطبية" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Underline"/>
      <LineObject Name="Line1" Left="9.45" Top="179.55" Width="774.9"/>
      <TextObject Name="Text12" Left="623.7" Top="189" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDLIST_REPORT.CUST_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text13" Left="387.45" Top="189" Width="236.25" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDLIST_REPORT.CUST_NAME]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text15" Left="226.8" Top="189" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDLIST_REPORT.CUST_GENDER]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text16" Left="18.9" Top="189" Width="141.75" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDLIST_REPORT.CUST_AGE_MONTH] / [MEDLIST_REPORT.CUST_AGE]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text17" Left="689.85" Top="189" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": اسم المريض" Font="Arial, 11pt"/>
      <TextObject Name="Text41" Left="321.3" Top="189" Width="56.7" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": الجنس" Font="Arial, 11pt"/>
      <TextObject Name="Text42" Left="160.65" Top="189" Width="47.25" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": العمر" Font="Arial, 11pt"/>
      <TextObject Name="Text14" Left="387.45" Top="217.35" Width="302.4" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDLIST_REPORT.CLI_NAME]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text40" Left="689.85" Top="217.35" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": اسم العيادة" Font="Arial, 11pt"/>
      <LineObject Name="Line2" Left="9.45" Top="245.7" Width="774.9"/>
    </ReportTitleBand>
    <GroupHeaderBand Name="GroupHeader1" Top="258.48" Width="793.8" Height="28.35" KeepWithData="true" Condition="[MEDLIST_REPORT.VIS_ID]" KeepTogether="true">
      <TextObject Name="Text1" Left="18.9" Width="623.7" Height="28.35" Border.Lines="All" Fill.Color="LightGray" Text="[MEDLIST_REPORT.MEDLIST_TIME] / [MEDLIST_REPORT.MEDLIST_DATE] / [[MEDLIST_REPORT.VIS_ID]] " HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text52" Left="642.6" Width="132.3" Height="28.35" Border.Lines="All" Fill.Color="LightGray" Text=": رقم الزيارة و تاريخها" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <DataBand Name="Data1" Top="312.4" Width="793.8" Height="28.35" DataSource="Table" KeepTogether="true">
        <TextObject Name="Text2" Left="680.4" Width="94.5" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDLIST_REPORT.MED_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
        <TextObject Name="Text4" Left="453.6" Width="226.8" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDLIST_REPORT.MED_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt"/>
        <TextObject Name="Text6" Left="292.95" Width="160.65" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDLIST_REPORT.MED_SOURSE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt"/>
        <TextObject Name="Text7" Left="18.9" Width="274.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDLIST_REPORT.DOS_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt"/>
        <DataHeaderBand Name="DataHeader1" Top="290.17" Width="793.8" Height="18.9" KeepWithData="true">
          <TextObject Name="Text5" Left="453.6" Width="226.8" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="اسم العلاج" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text9" Left="292.95" Width="160.65" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="مصدر العلاج" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text11" Left="18.9" Width="274.05" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="الجرعة" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text3" Left="680.4" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="رقم العلاج" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        </DataHeaderBand>
      </DataBand>
      <GroupFooterBand Name="GroupFooter1" Top="344.08" Width="793.8" Height="18.9" KeepWithData="true">
        <TextObject Name="Text20" Left="453.6" Width="226.8" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="[COUNT_MED_VIS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt"/>
        <TextObject Name="Text45" Left="680.4" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text=": عدد العلاجات" Font="Arial, 11pt, style=Bold"/>
      </GroupFooterBand>
    </GroupHeaderBand>
    <ReportSummaryBand Name="ReportSummary1" Top="366.32" Width="793.8" Height="37.8">
      <TextObject Name="Text18" Left="453.6" Top="9.45" Width="226.8" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="[COUNT_MED]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text43" Left="680.4" Top="9.45" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text=": اجمالي العلاجات" Font="Arial, 10pt, style=Bold"/>
    </ReportSummaryBand>
    <PageFooterBand Name="PageFooter1" Top="407.45" Width="793.8" Height="85.05">
      <TextObject Name="Text53" Left="18.9" Top="9.45" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDLIST_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="670.95" Top="9.45" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text54" Left="18.9" Top="37.8" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDLIST_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="670.95" Top="37.8" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
