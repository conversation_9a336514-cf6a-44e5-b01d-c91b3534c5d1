﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="09/26/2022 00:01:44" ReportInfo.Modified="02/21/2023 16:53:36" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }


    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHEE4vztXIKHIXryBiyq1BOk">
      <TableDataSource Name="Table" Alias="MEDLIST_VIS_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        MEDLIST_TBL.MEDLIST_ID, MEDLIST_TBL.MEDLIST_CODE, MEDLIST_TBL.MEDLIST_NAME, MEDLIST_TBL.MEDLIST_DATE, MEDLIST_TBL.MEDLIST_TIME, MEDLIST_TBL.DOS_NAME, MEDLIST_TBL.MED_ID, &#13;&#10;                         MEDLIST_TBL.CUST_ID, MEDLIST_TBL.VIS_ID, MEDLIST_TBL.MED_SOURSE, MEDLIST_TBL.CLI_ID, CLINC_TBL.CLI_CODE, CLINC_TBL.CLI_NAME, CLINC_TBL.CLI_LOC, CLINC_TBL.CLI_NOTE, CUST_TBL.CUST_CODE, &#13;&#10;                         CUST_TBL.CUST_AGE, CUST_TBL.CUST_BD, CUST_TBL.CUST_MOBILE1, CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_NOTE, CUST_TBL.CUST_GENDER, CUST_TBL.CUST_NATION, &#13;&#10;                         CUST_TBL.CUST_AGE_MONTH, &#13;&#10;                         CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME, MEDCIN_TBL.MED_CODE, MEDCIN_TBL.MED_NAME, MEDCIN_TBL.MED_S_NAME, MEDCIN_TBL.MED_PRICE, CLINIC_TITLE_TBL.CLI_T_ID, CLINIC_TITLE_TBL.CLI_T_NAME, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_TITLE, CLINIC_TITLE_TBL.CLI_T_ADDRESS, CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE, VISIT_TBL.VIS_CODE, VISIT_TBL.VIS_NAME, VISIT_TBL.VIS_DATE, &#13;&#10;                         VISIT_TBL.VIS_TIME, VISIT_TBL.VIS_TYPE, VISIT_TBL.VIS_PRICE, VISIT_TBL.VIS_DISCOUNT, VISIT_TBL.VIS_TOTAL, VISIT_TBL.VIS_NOTE, VISIT_TBL.VIS_PAY_TYPE, VISIT_TBL.VIS_UNPAY&#13;&#10;FROM            MEDLIST_TBL INNER JOIN&#13;&#10;                         CLINC_TBL ON MEDLIST_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         CUST_TBL ON MEDLIST_TBL.CUST_ID = CUST_TBL.CUST_ID INNER JOIN&#13;&#10;                         MEDCIN_TBL ON MEDLIST_TBL.MED_ID = MEDCIN_TBL.MED_ID INNER JOIN&#13;&#10;                         CLINIC_TITLE_TBL ON CLINC_TBL.CLI_ID = CLINIC_TITLE_TBL.CLI_ID LEFT OUTER JOIN&#13;&#10;                         VISIT_TBL ON MEDLIST_TBL.VIS_ID = VISIT_TBL.VIS_ID&#13;&#10;WHERE        (MEDLIST_TBL.VIS_ID = @VIS_ID) AND (MEDLIST_TBL.CLI_ID = @CLI_ID) AND (MEDLIST_TBL.CUST_ID = @CUST_ID) AND (MEDLIST_TBL.MEDLIST_CODE = @MEDLIST_CODE) AND (MEDLIST_TBL.MED_SOURSE = 'خارجي')&#13;&#10;ORDER BY MEDLIST_TBL.MEDLIST_ID">
        <Column Name="MEDLIST_ID" DataType="System.Decimal"/>
        <Column Name="MEDLIST_CODE" DataType="System.Decimal"/>
        <Column Name="MEDLIST_NAME" DataType="System.String"/>
        <Column Name="MEDLIST_DATE" DataType="System.DateTime"/>
        <Column Name="MEDLIST_TIME" DataType="System.TimeSpan"/>
        <Column Name="DOS_NAME" DataType="System.String"/>
        <Column Name="MED_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="MED_SOURSE" DataType="System.String"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="CLI_CODE" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="CLI_LOC" DataType="System.String"/>
        <Column Name="CLI_NOTE" DataType="System.String"/>
        <Column Name="CUST_CODE" DataType="System.Decimal"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_NOTE" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="MED_CODE" DataType="System.Decimal"/>
        <Column Name="MED_NAME" DataType="System.String"/>
        <Column Name="MED_S_NAME" DataType="System.String"/>
        <Column Name="MED_PRICE" DataType="System.Decimal"/>
        <Column Name="CLI_T_ID" DataType="System.Decimal"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <Column Name="VIS_CODE" DataType="System.Decimal"/>
        <Column Name="VIS_NAME" DataType="System.String"/>
        <Column Name="VIS_DATE" DataType="System.DateTime"/>
        <Column Name="VIS_TIME" DataType="System.TimeSpan"/>
        <Column Name="VIS_TYPE" DataType="System.String"/>
        <Column Name="VIS_PRICE" DataType="System.Decimal"/>
        <Column Name="VIS_DISCOUNT" DataType="System.Decimal"/>
        <Column Name="VIS_TOTAL" DataType="System.Decimal"/>
        <Column Name="VIS_NOTE" DataType="System.String"/>
        <Column Name="VIS_PAY_TYPE" DataType="System.String"/>
        <Column Name="VIS_UNPAY" DataType="System.Decimal"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="11"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="1"/>
        <CommandParameter Name="VIS_ID" DataType="22" Size="200" Expression="[VIS_ID]" DefaultValue="18"/>
        <CommandParameter Name="MEDLIST_CODE" DataType="22" Size="200" Expression="[MEDLIST_CODE]" DefaultValue="5"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="CUST_ID" DataType="System.String"/>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="VIS_ID" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
    <Parameter Name="MEDLIST_CODE" DataType="System.String"/>
  </Dictionary>
  <ReportPage Name="Page1" PaperHeight="150" LeftMargin="0" TopMargin="0" RightMargin="0" BottomMargin="0" StartPageEvent="Page1_StartPage">
    <ReportTitleBand Name="ReportTitle1" Width="793.8" Height="264.6">
      <TextObject Name="Text36" Left="141.75" Top="18.9" Width="510.3" Height="56.7" Text="[MEDLIST_VIS_REPORT.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt"/>
      <TextObject Name="Text37" Left="141.75" Top="75.6" Width="510.3" Height="47.25" Text="[MEDLIST_VIS_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <PictureObject Name="Picture1" Left="9.45" Top="18.9" Width="132.3" Height="141.75" SizeMode="StretchImage"/>
      <PictureObject Name="Picture2" Left="652.05" Top="18.9" Width="132.3" Height="141.75" SizeMode="StretchImage"/>
      <TextObject Name="Text35" Left="217.35" Top="141.75" Width="340.2" Height="28.35" Text="الوصفة الطبية" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Underline"/>
      <LineObject Name="Line1" Left="9.45" Top="170.1" Width="774.9"/>
      <TextObject Name="Text12" Left="623.7" Top="179.55" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDLIST_VIS_REPORT.CUST_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text13" Left="387.45" Top="179.55" Width="236.25" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDLIST_VIS_REPORT.CUST_NAME]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text15" Left="236.25" Top="179.55" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDLIST_VIS_REPORT.CUST_GENDER]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text16" Left="28.35" Top="179.55" Width="141.75" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDLIST_VIS_REPORT.CUST_AGE_MONTH] / [MEDLIST_VIS_REPORT.CUST_AGE]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text17" Left="689.85" Top="179.55" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="LemonChiffon" Text=": اسم المريض" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text41" Left="330.75" Top="179.55" Width="56.7" Height="18.9" Border.Lines="All" Fill.Color="LemonChiffon" Text=": الجنس" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text42" Left="170.1" Top="179.55" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="LemonChiffon" Text=": العمر" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text14" Left="387.45" Top="207.9" Width="302.4" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDLIST_VIS_REPORT.CLI_NAME]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text40" Left="689.85" Top="207.9" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="LemonChiffon" Text=": اسم العيادة" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line2" Left="9.45" Top="236.25" Width="774.9"/>
      <TextObject Name="Text47" Left="18.9" Top="236.25" Width="47.25" Height="28.35" Text="Rx:-" VertAlign="Bottom" Font="Arial, 14pt, style=Underline"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="267.93" Width="793.8" Height="18.9">
      <TextObject Name="Text2" Left="47.25" Width="349.65" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="اسم العلاج" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text4" Left="396.9" Width="368.55" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="تفاصيل العلاج" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageHeaderBand>
    <DataBand Name="Data1" Top="290.17" Width="793.8" Height="18.9" DataSource="Table">
      <TextObject Name="Text1" Left="47.25" Width="349.65" Height="18.9" Border.Lines="All" Text="[MEDLIST_VIS_REPORT.MED_NAME]" VertAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text3" Left="396.9" Width="368.55" Height="18.9" Border.Lines="All" Text="[MEDLIST_VIS_REPORT.DOS_NAME]" VertAlign="Center" Font="Arial, 11pt"/>
    </DataBand>
    <PageFooterBand Name="PageFooter1" Top="312.4" Width="793.8" Height="189">
      <TextObject Name="Text44" Left="37.8" Top="47.25" Width="302.4" Height="28.35" Text="دكتور محمد عبدالكاظم رحيم الخماسي" HorzAlign="Center" VertAlign="Center" Font="Bahnschrift Light, 14pt, style=Bold"/>
      <TextObject Name="Text45" Left="37.8" Top="18.9" Width="302.4" Height="18.9" Text="أسم و توقيع الطبيب" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text48" Left="18.9" Top="103.95" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDLIST_VIS_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="670.95" Top="103.95" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text49" Left="18.9" Top="132.3" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDLIST_VIS_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="670.95" Top="132.3" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
