﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="05/05/2021 01:47:25" ReportInfo.Modified="10/01/2022 22:14:04" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHH8csw3zWSyLw88dpWUGLai">
      <TableDataSource Name="Table" Alias="MREP_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        MEDREP_TBL.MREP_ID, MEDREP_TBL.MREP_CODE,convert(varchar , MEDREP_TBL.MREP_DATE, 103) as MREP_DATE,convert(varchar(5) , MEDREP_TBL.MREP_TIME, 108) as MREP_TIME, MEDREP_TBL.MREP_NAME, &#13;&#10;                         MEDREP_TBL.MREP_TEXT, MEDREP_TBL.MREP_NOTE, MEDREP_TBL.CUST_ID, MEDREP_TBL.CLI_ID, MEDREP_TBL.VIS_ID, CLINC_TBL.CLI_NAME, &#13;&#10;                         VISIT_TBL.VIS_CODE, VISIT_TBL.VIS_NAME, VISIT_TBL.VIS_DATE, VISIT_TBL.VIS_TIME, CLINIC_TITLE_TBL.CLI_T_NAME, CLINIC_TITLE_TBL.CLI_T_TITLE, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_ADDRESS, CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE, CUST_TBL.CUST_CODE, CUST_TBL.CUST_AGE, &#13;&#10;                         CUST_TBL.CUST_BD, CUST_TBL.CUST_MOBILE1, CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_GENDER, CUST_TBL.CUST_NATION, &#13;&#10;                         CUST_TBL.CUST_AGE_MONTH, &#13;&#10;                         CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME&#13;&#10;FROM            MEDREP_TBL INNER JOIN&#13;&#10;                         CLINC_TBL ON MEDREP_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         CLINIC_TITLE_TBL ON MEDREP_TBL.CLI_ID = CLINIC_TITLE_TBL.CLI_ID INNER JOIN&#13;&#10;                         CUST_TBL ON MEDREP_TBL.CUST_ID = CUST_TBL.CUST_ID LEFT OUTER JOIN&#13;&#10;                         VISIT_TBL ON MEDREP_TBL.VIS_ID = VISIT_TBL.VIS_ID&#13;&#10;WHERE&#13;&#10;MEDREP_TBL.CUST_ID = @CUST_ID	&#13;&#10;AND&#13;&#10;MEDREP_TBL.CLI_ID = @CLI_ID&#13;&#10;AND&#13;&#10;MEDREP_TBL.MREP_CODE = @MREP_CODE&#13;&#10;AND&#13;&#10;MEDREP_TBL.MREP_DATE = @MREP_DATE">
        <Column Name="MREP_ID" DataType="System.Decimal"/>
        <Column Name="MREP_CODE" DataType="System.Decimal"/>
        <Column Name="MREP_DATE" DataType="System.String"/>
        <Column Name="MREP_TIME" DataType="System.String"/>
        <Column Name="MREP_NAME" DataType="System.String"/>
        <Column Name="MREP_TEXT" DataType="System.String"/>
        <Column Name="MREP_NOTE" DataType="System.String"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="VIS_CODE" DataType="System.Decimal"/>
        <Column Name="VIS_NAME" DataType="System.String"/>
        <Column Name="VIS_DATE" DataType="System.DateTime"/>
        <Column Name="VIS_TIME" DataType="System.TimeSpan"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <Column Name="CUST_CODE" DataType="System.Decimal"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]"/>
        <CommandParameter Name="MREP_CODE" DataType="22" Size="200" Expression="[MREP_CODE]"/>
        <CommandParameter Name="MREP_DATE" DataType="22" Size="200" Expression="[MREP_DATE]"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="CUST_ID" DataType="System.String"/>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="MREP_CODE" DataType="System.String"/>
    <Parameter Name="MREP_DATE" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
  </Dictionary>
  <ReportPage Name="Page1" PaperHeight="150" LeftMargin="0" TopMargin="0" RightMargin="0" FirstPageSource="15" OtherPagesSource="15" StartPageEvent="Page1_StartPage">
    <PageHeaderBand Name="PageHeader1" Width="793.8" Height="387.45">
      <TextObject Name="Text36" Left="151.2" Top="9.45" Width="491.4" Height="47.25" Text="[MREP_REPORT.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt"/>
      <TextObject Name="Text37" Left="151.2" Top="66.15" Width="491.4" Height="47.25" Text="[MREP_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <PictureObject Name="Picture1" Left="9.45" Top="9.45" Width="132.3" Height="141.75" SizeMode="StretchImage" Image=""/>
      <PictureObject Name="Picture2" Left="652.05" Top="9.45" Width="132.3" Height="141.75" SizeMode="StretchImage" Image=""/>
      <TextObject Name="Text35" Left="217.35" Top="151.2" Width="340.2" Height="28.35" Text="تقرير طبي" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Bold, Underline"/>
      <LineObject Name="Line1" Left="9.45" Top="189" Width="774.9"/>
      <TextObject Name="Text12" Left="623.7" Top="198.45" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MREP_REPORT.CUST_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text13" Left="387.45" Top="198.45" Width="236.25" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MREP_REPORT.CUST_NAME]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text15" Left="207.9" Top="198.45" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MREP_REPORT.CUST_GENDER]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text16" Left="18.9" Top="198.45" Width="141.75" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MREP_REPORT.CUST_AGE_MONTH] / [MREP_REPORT.CUST_AGE]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text17" Left="689.85" Top="198.45" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم المريض" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text41" Left="302.4" Top="198.45" Width="56.7" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": الجنس" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text42" Left="160.65" Top="198.45" Width="47.25" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": العمر" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text14" Left="387.45" Top="226.8" Width="302.4" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MREP_REPORT.CLI_NAME]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text40" Left="689.85" Top="226.8" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم العيادة" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text4" Left="18.9" Top="226.8" Width="340.2" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MREP_REPORT.VIS_ID] : رقم الزيارة" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text2" Left="595.35" Top="255.15" Width="94.5" Height="18.9" Border.Lines="All" Text="[MREP_REPORT.MREP_DATE] " Format="Date" Format.Format="d" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text44" Left="689.85" Top="255.15" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": تاريخ التقرير" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line2" Left="9.45" Top="283.5" Width="774.9"/>
      <TextObject Name="Text48" Left="500.85" Top="255.15" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": وقت التقرير" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text3" Left="406.35" Top="255.15" Width="94.5" Height="18.9" Border.Lines="All" Text="[MREP_REPORT.MREP_TIME]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text1" Left="18.9" Top="292.95" Width="756" Height="85.05" Text="[MREP_REPORT.MREP_TEXT]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text5" Left="18.9" Top="255.15" Width="245.7" Height="18.9" Border.Lines="All" Text="[Date]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text56" Left="264.6" Top="255.15" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": تاريخ الطباعة" Font="Arial, 11pt, style=Bold"/>
    </PageHeaderBand>
    <PageFooterBand Name="PageFooter1" Top="390.78" Width="793.8" Height="198.45" CanBreak="true">
      <TextObject Name="Text46" Left="28.35" Top="37.8" Width="292.95" Height="28.35" Text="أسم و توقيع الطبيب" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text47" Left="28.35" Top="66.15" Width="292.95" Height="37.8" Text="............................................................" HorzAlign="Center" VertAlign="Bottom" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text55" Left="18.9" Top="151.2" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MREP_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="670.95" Top="151.2" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="670.95" Top="122.85" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text54" Left="18.9" Top="122.85" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MREP_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <LineObject Name="Line3" Left="9.45" Top="9.45" Width="774.9"/>
    </PageFooterBand>
  </ReportPage>
</Report>
