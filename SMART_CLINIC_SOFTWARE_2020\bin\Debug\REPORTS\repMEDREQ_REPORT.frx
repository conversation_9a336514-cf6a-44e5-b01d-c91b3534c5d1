﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="05/01/2021 22:00:02" ReportInfo.Modified="09/25/2022 21:30:04" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqIVd0v8qjIG7dW856t8miNlYuokVlLWrw103KuTjD34SQjH7VU9T+P7O7O3P+qD7c08H72S9mcGumsuecH/KePvoaA48urNcqYu+oVpDVNVTrph+LeULMGdwRQngIMkg45KutDabPejdKfsq1y+MKZmAB/fujv81RawUKqjpXtjW+oyBW0DPVzr0miB84JmU7IUSb4lzCrz+A8IP/ZvLP+3rSzUK0w2wtKs9QlOMWNd3AaAtLHGV0Qjx1B2i/nLUyzdxKYaQj/PWdXbhtH5viHw==">
      <TableDataSource Name="Table" Alias="MEDREQ_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        MEDREQ_TBL.MEDREQ_ID, MEDREQ_TBL.MEDREQ_CODE, MEDREQ_TBL.MEDREQ_NAME,convert(varchar , MEDREQ_TBL.MEDREQ_DATE, 103) as MEDREQ_DATE,convert(varchar(5) , MEDREQ_TBL.MEDREQ_TIME, 108) as MEDREQ_TIME, &#13;&#10;                         MEDREQ_TBL.MEDREQ_RESULT, MEDREQ_TBL.MEDREQ_NOTE, MEDREQ_TBL.MEDCHEK_ID, MEDREQ_TBL.CUST_ID, MEDREQ_TBL.CLI_ID, &#13;&#10;                         MEDREQ_TBL.VIS_ID, MEDREQ_TBL.MEDREQ_STATE, MEDCHEK_TBL.MEDCHEK_CODE, MEDCHEK_TBL.MEDCHEK_NAME, MEDCHEK_TBL.MEDCHEK_TYPE, &#13;&#10;                         MEDCHEK_TBL.MEDCHEK_PRICE, CLINC_TBL.CLI_NAME, CUST_TBL.CUST_CODE, CUST_TBL.CUST_AGE, CUST_TBL.CUST_BD, CUST_TBL.CUST_MOBILE1, &#13;&#10;                         CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_GENDER, CUST_TBL.CUST_NATION, CUST_TBL.CUST_AGE_MONTH, &#13;&#10;                         VISIT_TBL.VIS_NAME, VISIT_TBL.VIS_DATE, VISIT_TBL.VIS_TIME, VISIT_TBL.VIS_TYPE, VISIT_TBL.VIS_CODE, CLINIC_TITLE_TBL.CLI_T_NAME, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_TITLE, CLINIC_TITLE_TBL.CLI_T_ADDRESS, CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE, &#13;&#10;                         CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME&#13;&#10;FROM            MEDREQ_TBL INNER JOIN&#13;&#10;                         MEDCHEK_TBL ON MEDREQ_TBL.MEDCHEK_ID = MEDCHEK_TBL.MEDCHEK_ID INNER JOIN&#13;&#10;                         CLINC_TBL ON MEDCHEK_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;						 CLINIC_TITLE_TBL ON CLINIC_TITLE_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         CUST_TBL ON MEDREQ_TBL.CUST_ID = CUST_TBL.CUST_ID LEFT OUTER JOIN&#13;&#10;                         VISIT_TBL ON MEDREQ_TBL.VIS_ID = VISIT_TBL.VIS_ID&#13;&#10;WHERE &#13;&#10;MEDREQ_TBL.CUST_ID = @CUST_ID&#13;&#10;AND&#13;&#10;MEDREQ_TBL.CLI_ID = @CLI_ID&#13;&#10;AND&#13;&#10;MEDREQ_STATE = 'CLOSE'&#13;&#10;">
        <Column Name="MEDREQ_ID" DataType="System.Decimal"/>
        <Column Name="MEDREQ_CODE" DataType="System.Decimal"/>
        <Column Name="MEDREQ_NAME" DataType="System.String"/>
        <Column Name="MEDREQ_DATE" DataType="System.String"/>
        <Column Name="MEDREQ_TIME" DataType="System.String"/>
        <Column Name="MEDREQ_RESULT" DataType="System.String"/>
        <Column Name="MEDREQ_NOTE" DataType="System.String"/>
        <Column Name="MEDCHEK_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="MEDREQ_STATE" DataType="System.String"/>
        <Column Name="MEDCHEK_CODE" DataType="System.Decimal"/>
        <Column Name="MEDCHEK_NAME" DataType="System.String"/>
        <Column Name="MEDCHEK_TYPE" DataType="System.String"/>
        <Column Name="MEDCHEK_PRICE" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="CUST_CODE" DataType="System.Decimal"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="VIS_NAME" DataType="System.String"/>
        <Column Name="VIS_DATE" DataType="System.DateTime"/>
        <Column Name="VIS_TIME" DataType="System.TimeSpan"/>
        <Column Name="VIS_TYPE" DataType="System.String"/>
        <Column Name="VIS_CODE" DataType="System.Decimal"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="0"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="0"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="CUST_ID" DataType="System.String"/>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
    <Total Name="MEDREQ_COUNT" TotalType="Count" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="PRICE_SUM" Expression="[MEDREQ_REPORT.MEDCHEK_PRICE]" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="COUNT_VIS" TotalType="Count" Evaluator="Data1" PrintOn="GroupFooter1"/>
    <Total Name="SUM_VIS" Expression="[MEDREQ_REPORT.MEDCHEK_PRICE]" Evaluator="Data1" PrintOn="GroupFooter1"/>
  </Dictionary>
  <ReportPage Name="Page1" RawPaperSize="9" LeftMargin="0" TopMargin="0" RightMargin="0" BottomMargin="0" StartPageEvent="Page1_StartPage">
    <ReportTitleBand Name="ReportTitle1" Width="793.8" Height="236.25">
      <TextObject Name="Text36" Left="151.2" Top="9.45" Width="491.4" Height="47.25" Text="[MEDREQ_REPORT.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt"/>
      <TextObject Name="Text37" Left="151.2" Top="56.7" Width="491.4" Height="37.8" Text="[MEDREQ_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <PictureObject Name="Picture1" Left="9.45" Top="9.45" Width="132.3" Height="141.75" SizeMode="StretchImage"/>
      <PictureObject Name="Picture2" Left="652.05" Top="9.45" Width="132.3" Height="141.75" SizeMode="StretchImage"/>
      <TextObject Name="Text35" Left="217.35" Top="132.3" Width="340.2" Height="28.35" Text="فحوصات المختبر" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Underline"/>
      <LineObject Name="Line1" Left="9.45" Top="170.1" Width="774.9"/>
      <TextObject Name="Text12" Left="633.15" Top="179.55" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDREQ_REPORT.CUST_ID]" Format="Currency" Format.UseLocale="true" HorzAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text13" Left="396.9" Top="179.55" Width="236.25" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDREQ_REPORT.CUST_NAME]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text14" Left="396.9" Top="207.9" Width="302.4" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDREQ_REPORT.CLI_NAME]" HorzAlign="Right" Font="Arial, 11pt"/>
      <LineObject Name="Line2" Left="9.45" Top="236.25" Width="774.9"/>
      <TextObject Name="Text15" Left="236.25" Top="179.55" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDREQ_REPORT.CUST_GENDER]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text16" Left="9.45" Top="179.55" Width="141.75" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[MEDREQ_REPORT.CUST_AGE_MONTH] / [MEDREQ_REPORT.CUST_AGE]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text17" Left="699.3" Top="179.55" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": اسم المريض" Font="Arial, 11pt"/>
      <TextObject Name="Text40" Left="699.3" Top="207.9" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": اسم العيادة" Font="Arial, 11pt"/>
      <TextObject Name="Text41" Left="311.85" Top="179.55" Width="56.7" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": الجنس" Font="Arial, 11pt"/>
      <TextObject Name="Text42" Left="151.2" Top="179.55" Width="47.25" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": العمر" Font="Arial, 11pt"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="239.58" Width="793.8"/>
    <GroupHeaderBand Name="GroupHeader1" Top="242.92" Width="793.8" Height="18.9" KeepWithData="true" Condition="[MEDREQ_REPORT.VIS_ID]" KeepTogether="true">
      <TextObject Name="Text1" Left="18.9" Width="765.45" Height="18.9" Border.Lines="All" Fill.Color="LemonChiffon" Text="[MEDREQ_REPORT.MEDREQ_TIME] / [MEDREQ_REPORT.MEDREQ_DATE] / [MEDREQ_REPORT.VIS_ID]: رقم الزيارة" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <DataBand Name="Data1" Top="287.38" Width="793.8" Height="18.9" DataSource="Table" KeepTogether="true">
        <TextObject Name="Text2" Left="680.4" Width="103.95" Height="18.9" Border.Lines="All" Text="[MEDREQ_REPORT.MEDREQ_ID]" Format="Currency" Format.UseLocale="true" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
        <TextObject Name="Text4" Left="453.6" Width="226.8" Height="18.9" Border.Lines="All" Text="[MEDREQ_REPORT.MEDREQ_NAME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt"/>
        <TextObject Name="Text6" Left="359.1" Width="94.5" Height="18.9" Border.Lines="All" Text="[MEDREQ_REPORT.MEDCHEK_PRICE]" Format="Currency" Format.UseLocale="true" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
        <TextObject Name="Text8" Left="217.35" Width="141.75" Height="18.9" Border.Lines="All" Text="[MEDREQ_REPORT.MEDCHEK_TYPE]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt"/>
        <TextObject Name="Text10" Left="18.9" Width="198.45" Height="18.9" Border.Lines="All" Text="[MEDREQ_REPORT.MEDREQ_RESULT]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt"/>
        <DataHeaderBand Name="DataHeader1" Top="265.15" Width="793.8" Height="18.9" KeepWithData="true">
          <TextObject Name="Text3" Left="680.4" Width="103.95" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="رقم الفحص" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text5" Left="453.6" Width="226.8" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="اسم الفحص" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text7" Left="359.1" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="سعر الفحص" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text9" Left="217.35" Width="141.75" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="نوع الفحص" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text11" Left="18.9" Width="198.45" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="نتيجة الفحص" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        </DataHeaderBand>
      </DataBand>
      <GroupFooterBand Name="GroupFooter1" Top="309.62" Width="793.8" Height="18.9" KeepWithData="true">
        <TextObject Name="Text20" Left="217.35" Width="236.25" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="[SUM_VIS]" HorzAlign="Right" Font="Arial, 11pt"/>
        <TextObject Name="Text21" Left="557.55" Width="122.85" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="[COUNT_VIS]" HorzAlign="Right" Font="Arial, 11pt"/>
        <TextObject Name="Text45" Left="680.4" Width="103.95" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": فحوصات الزيارة" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text46" Left="453.6" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": مجموع السعر" Font="Arial, 11pt, style=Bold"/>
      </GroupFooterBand>
    </GroupHeaderBand>
    <ReportSummaryBand Name="ReportSummary1" Top="331.85" Width="793.8" Height="28.35" KeepWithData="true">
      <TextObject Name="Text18" Left="557.55" Width="122.85" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="[MEDREQ_COUNT]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text19" Left="217.35" Width="236.25" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="[PRICE_SUM]" HorzAlign="Right" Font="Arial, 11pt"/>
      <TextObject Name="Text43" Left="680.4" Width="103.95" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": عدد الفحوصات" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text44" Left="453.6" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="White" Text=": اجمالي السعر" Font="Arial, 11pt, style=Bold"/>
    </ReportSummaryBand>
    <PageFooterBand Name="PageFooter1" Top="363.53" Width="793.8" Height="103.95">
      <TextObject Name="Text47" Left="28.35" Top="9.45" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDREQ_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="680.4" Top="9.45" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text48" Left="28.35" Top="37.8" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDREQ_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="680.4" Top="37.8" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
