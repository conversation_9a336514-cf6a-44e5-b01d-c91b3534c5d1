﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="04/30/2021 16:03:32" ReportInfo.Modified="10/24/2022 22:04:08" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }
    

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqIVd0v8qjIG7dW856t8miNlYuokVlLWrw103KuTjD34SQjH7VU9T+P7O7O3P+qD7c08H72S9mcGumsuecH/KePvoaA48urNcqYu+oVpDVNVTrph+LeULMGdwRQngIMkg45KutDabPejdKfsq1y+MKZmAB/fujv81RawUKqjpXtjW+oyBW0DPVzr0miB84JmU7IUSb4lzCrz+A8IP/ZvLP+3rSzUK0w2wtKs9QlOMWNd3AaAtLHGV0Qjx1B2i/nLUyBBctBUyiO3gcy0vzjAj19A==">
      <TableDataSource Name="Table" Alias="MEDREQ_VIS_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        MEDREQ_TBL.MEDREQ_ID, MEDREQ_TBL.MEDREQ_CODE, MEDREQ_TBL.MEDREQ_NAME,convert(varchar , MEDREQ_TBL.MEDREQ_DATE, 103) as MEDREQ_DATE,convert(varchar(5) , MEDREQ_TBL.MEDREQ_TIME, 108) as MEDREQ_TIME, &#13;&#10;                         MEDREQ_TBL.MEDREQ_RESULT, MEDREQ_TBL.MEDREQ_NOTE, MEDREQ_TBL.MEDCHEK_ID, MEDREQ_TBL.CUST_ID, MEDREQ_TBL.CLI_ID, &#13;&#10;                         MEDREQ_TBL.VIS_ID, MEDREQ_TBL.MEDREQ_STATE, MEDCHEK_TBL.MEDCHEK_CODE, MEDCHEK_TBL.MEDCHEK_NAME, MEDCHEK_TBL.MEDCHEK_TYPE, &#13;&#10;                         MEDCHEK_TBL.MEDCHEK_PRICE, CLINC_TBL.CLI_NAME, CUST_TBL.CUST_CODE, CUST_TBL.CUST_AGE, CUST_TBL.CUST_BD, CUST_TBL.CUST_MOBILE1, &#13;&#10;                         CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_GENDER, CUST_TBL.CUST_NATION, CUST_TBL.CUST_AGE_MONTH, &#13;&#10;                         VISIT_TBL.VIS_NAME, VISIT_TBL.VIS_DATE, VISIT_TBL.VIS_TIME, VISIT_TBL.VIS_TYPE, VISIT_TBL.VIS_CODE, CLINIC_TITLE_TBL.CLI_T_NAME, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_TITLE, CLINIC_TITLE_TBL.CLI_T_ADDRESS, CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE, &#13;&#10;                         CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME&#13;&#10;FROM            MEDREQ_TBL INNER JOIN&#13;&#10;                         MEDCHEK_TBL ON MEDREQ_TBL.MEDCHEK_ID = MEDCHEK_TBL.MEDCHEK_ID INNER JOIN&#13;&#10;                         CLINC_TBL ON MEDCHEK_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;						 CLINIC_TITLE_TBL ON CLINIC_TITLE_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         CUST_TBL ON MEDREQ_TBL.CUST_ID = CUST_TBL.CUST_ID LEFT OUTER JOIN&#13;&#10;                         VISIT_TBL ON MEDREQ_TBL.VIS_ID = VISIT_TBL.VIS_ID&#13;&#10;WHERE &#13;&#10;MEDREQ_TBL.CUST_ID = @CUST_ID&#13;&#10;AND&#13;&#10;MEDREQ_TBL.CLI_ID = @CLI_ID&#13;&#10;AND&#13;&#10;MEDREQ_TBL.MEDREQ_CODE = @MEDREQ_CODE&#13;&#10;AND&#13;&#10;MEDREQ_TBL.VIS_ID = @VIS_ID&#13;&#10;">
        <Column Name="MEDREQ_ID" DataType="System.Decimal"/>
        <Column Name="MEDREQ_CODE" DataType="System.Decimal"/>
        <Column Name="MEDREQ_NAME" DataType="System.String"/>
        <Column Name="MEDREQ_DATE" DataType="System.DateTime"/>
        <Column Name="MEDREQ_TIME" DataType="System.TimeSpan"/>
        <Column Name="MEDREQ_RESULT" DataType="System.String"/>
        <Column Name="MEDREQ_NOTE" DataType="System.String"/>
        <Column Name="MEDCHEK_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="MEDREQ_STATE" DataType="System.String"/>
        <Column Name="MEDCHEK_CODE" DataType="System.Decimal"/>
        <Column Name="MEDCHEK_NAME" DataType="System.String"/>
        <Column Name="MEDCHEK_TYPE" DataType="System.String"/>
        <Column Name="MEDCHEK_PRICE" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="CUST_CODE" DataType="System.Decimal"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="VIS_NAME" DataType="System.String"/>
        <Column Name="VIS_DATE" DataType="System.DateTime"/>
        <Column Name="VIS_TIME" DataType="System.TimeSpan"/>
        <Column Name="VIS_TYPE" DataType="System.String"/>
        <Column Name="VIS_CODE" DataType="System.Decimal"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="0"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="0"/>
        <CommandParameter Name="MEDREQ_CODE" DataType="22" Size="200" Expression="[MEDREQ_CODE]" DefaultValue="0"/>
        <CommandParameter Name="VIS_ID" DataType="22" Size="200" Expression="[VIS_ID]" DefaultValue="0"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="CUST_ID" DataType="System.String"/>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="VIS_ID" DataType="System.String"/>
    <Parameter Name="MEDREQ_CODE" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
  </Dictionary>
  <ReportPage Name="Page1" PaperHeight="150" LeftMargin="0" TopMargin="0" RightMargin="0" BottomMargin="0" StartPageEvent="Page1_StartPage">
    <ReportTitleBand Name="ReportTitle1" Width="793.8" Height="226.8">
      <TextObject Name="Text1" Left="122.85" Top="18.9" Width="548.1" Height="47.25" Text="[MEDREQ_VIS_REPORT.CLI_T_NAME]" HorzAlign="Center" Font="Times New Roman, 18pt"/>
      <TextObject Name="Text2" Left="122.85" Top="66.15" Width="548.1" Height="47.25" Text="[MEDREQ_VIS_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <PictureObject Name="Picture1" Left="9.45" Top="18.9" Width="113.4" Height="103.95" SizeMode="StretchImage"/>
      <PictureObject Name="Picture2" Left="670.95" Top="18.9" Width="113.4" Height="103.95" SizeMode="StretchImage"/>
      <TextObject Name="Text35" Left="245.7" Top="113.4" Width="340.2" Height="28.35" Text="فحوصات المختبر" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Italic, Underline"/>
      <LineObject Name="Line1" Top="141.75" Width="793.8"/>
      <TextObject Name="Text10" Left="647.32" Top="151.2" Width="47.25" Height="28.35" Border.Lines="All" Text="[MEDREQ_VIS_REPORT.CUST_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text11" Left="420.52" Top="151.2" Width="226.8" Height="28.35" Border.Lines="All" Text="[MEDREQ_VIS_REPORT.CUST_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text12" Left="694.57" Top="151.2" Width="66.15" Height="28.35" Border.Lines="All" Fill.Color="Gainsboro" Text=": المريض" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text13" Left="694.57" Top="189" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text=": العيادة" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text14" Left="420.52" Top="189" Width="274.05" Height="18.9" Border.Lines="All" Text="[MEDREQ_VIS_REPORT.CLI_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text17" Left="326.03" Top="151.2" Width="56.7" Height="28.35" Border.Lines="All" Fill.Color="Gainsboro" Text=": الجنس" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text18" Left="278.77" Top="151.2" Width="47.25" Height="28.35" Border.Lines="All" Text="[MEDREQ_VIS_REPORT.CUST_GENDER]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text19" Left="231.52" Top="151.2" Width="47.25" Height="28.35" Border.Lines="All" Fill.Color="Gainsboro" Text=": العمر" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text20" Left="193.72" Top="151.2" Width="37.8" Height="28.35" Border.Lines="All" Text="[MEDREQ_VIS_REPORT.CUST_AGE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text21" Left="155.92" Top="151.2" Width="37.8" Height="28.35" Border.Lines="All" Text="[MEDREQ_VIS_REPORT.CUST_AGE_MONTH]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text22" Left="288.22" Top="189" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text=": تاريخ الطباعة" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text23" Left="70.87" Top="189" Width="217.35" Height="18.9" Border.Lines="All" Text="[Date]" Format="Date" Format.Format="d" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line2" Top="223.02" Width="793.8"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="230.13" Width="793.8" Height="18.9">
      <TextObject Name="Text6" Left="666.22" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="رقم الفحص" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text8" Left="335.47" Width="330.75" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="اسم الفحص" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text26" Left="23.62" Width="311.85" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="الملاحظات" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
    </PageHeaderBand>
    <DataBand Name="Data1" Top="252.37" Width="793.8" Height="18.9" DataSource="Table" KeepTogether="true">
      <TextObject Name="Text5" Left="666.22" Width="94.5" Height="18.9" Border.Lines="All" Text="[MEDREQ_VIS_REPORT.MEDCHEK_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text7" Left="335.47" Width="330.75" Height="18.9" Border.Lines="All" Text="[MEDREQ_VIS_REPORT.MEDCHEK_NAME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text25" Left="23.62" Width="311.85" Height="18.9" Border.Lines="All" Text="[MEDREQ_VIS_REPORT.MEDREQ_NOTE]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt"/>
    </DataBand>
    <PageFooterBand Name="PageFooter1" Top="274.6" Width="793.8" Height="198.45">
      <TextObject Name="Text36" Left="9.45" Top="28.35" Width="302.4" Height="18.9" Text="أسم و توقيع الطبيب" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text37" Left="9.45" Top="56.7" Width="302.4" Height="18.9" Text="..............................................................." HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <LineObject Name="Line3" Top="9.45" Width="793.8"/>
      <TextObject Name="Text44" Left="18.9" Top="94.5" Width="652.05" Height="47.25" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDREQ_VIS_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text46" Left="670.95" Top="94.5" Width="103.95" Height="47.25" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text45" Left="18.9" Top="141.75" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[MEDREQ_VIS_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text47" Left="670.95" Top="141.75" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
