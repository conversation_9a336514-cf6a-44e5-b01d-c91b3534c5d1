﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="04/27/2021 19:18:00" ReportInfo.Modified="10/17/2022 14:17:44" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqIVd0v8qjIG7dW856t8miNlYuokVlLWrw103KuTjD34SQjH7VU9T+P7O7O3P+qD7c08H72S9mcGumsuecH/KePvoaA48urNcqYu+oVpDVNVTrph+LeULMGdwRQngIMkg45KutDabPejdKfsq1y+MKZmAB/fujv81RawUKqjpXtjW+oyBW0DPVzr0miB84JmU7IUSb4lzCrz+A8IP/ZvLP+3rSzUK0w2wtKs9QlOMWNd3AaAtLHGV0Qjx1B2i/nLUybesYtxub0OpCC6bnuJjhYA==">
      <TableDataSource Name="Table" Alias="repSERLIST" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        SERLIST_TBL.SERLIST_ID, SERLIST_TBL.SERLIST_CODE, SERLIST_TBL.SERLIST_NAME, SERLIST_TBL.SERLIST_DATE, SERLIST_TBL.SERLIST_TIME, &#13;&#10;                         SERLIST_TBL.SER_PRICE_TOTAL, SERLIST_TBL.SERLIST_NOTE, SERLIST_TBL.SER_ID, SERLIST_TBL.CUST_ID, SERLIST_TBL.VIS_ID, VISIT_TBL.VIS_NAME, &#13;&#10;                         convert(varchar , VISIT_TBL.VIS_DATE, 103) as VIS_DATE,convert(varchar(5) , VISIT_TBL.VIS_TIME, 108) as VIS_TIME, VISIT_TBL.VIS_TYPE, VISIT_TBL.VIS_PRICE, VISIT_TBL.VIS_DISCOUNT, VISIT_TBL.VIS_TOTAL, VISIT_TBL.VIS_NOTE, &#13;&#10;                         VISIT_TBL.VIS_PAY_TYPE, VISIT_TBL.VIS_UNPAY, DOCTORS_TBL.DOC_NAME, DOCTORS_TBL.DOC_MAJOR, DOCTORS_TBL.DOC_CODE, VISIT_TBL.VIS_CODE, &#13;&#10;                         DOCTORS_TBL.DOC_EXP, DOCTORS_TBL.DOC_BD, DOCTORS_TBL.DOC_MOBILE, DOCTORS_TBL.DOC_ADDRESS, CLINC_TBL.CLI_CODE, CLINC_TBL.CLI_NAME, &#13;&#10;                         CLINC_TBL.CLI_LOC, CLINC_TBL.CLI_NOTE, CUST_TBL.CUST_CODE, CUST_TBL.CUST_AGE, CUST_TBL.CUST_BD, CUST_TBL.CUST_MOBILE1, &#13;&#10;                         CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_GENDER, CUST_TBL.CUST_NATION, CUST_TBL.CUST_AGE_MONTH, &#13;&#10;                         CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_ID, CLINIC_TITLE_TBL.CLI_T_NAME, CLINIC_TITLE_TBL.CLI_T_TITLE, CLINIC_TITLE_TBL.CLI_T_ADDRESS, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE, CLINC_TBL.CLI_ID, SERVICE_TBL.SER_CODE, SERVICE_TBL.SER_NAME, &#13;&#10;                         SERVICE_TBL.SER_TYPE, SERVICE_TBL.SER_PRICE&#13;&#10;FROM            SERLIST_TBL LEFT OUTER JOIN&#13;&#10;                         VISIT_TBL ON SERLIST_TBL.VIS_ID = VISIT_TBL.VIS_ID LEFT OUTER JOIN&#13;&#10;                         DOCTORS_TBL ON VISIT_TBL.DOC_ID = DOCTORS_TBL.DOC_ID LEFT OUTER JOIN&#13;&#10;                         CLINC_TBL ON SERLIST_TBL.CLI_ID = CLINC_TBL.CLI_ID LEFT OUTER JOIN&#13;&#10;                         CUST_TBL ON SERLIST_TBL.CUST_ID = CUST_TBL.CUST_ID INNER JOIN&#13;&#10;                         CLINIC_TITLE_TBL ON CLINC_TBL.CLI_ID = CLINIC_TITLE_TBL.CLI_ID INNER JOIN&#13;&#10;                         SERVICE_TBL ON SERLIST_TBL.SER_ID = SERVICE_TBL.SER_ID&#13;&#10;WHERE        (CUST_TBL.CUST_ID = @CUST_ID) AND (CLINC_TBL.CLI_ID = @CLI_ID)">
        <Column Name="SERLIST_ID" DataType="System.Decimal"/>
        <Column Name="SERLIST_CODE" DataType="System.Decimal"/>
        <Column Name="SERLIST_NAME" DataType="System.String"/>
        <Column Name="SERLIST_DATE" DataType="System.DateTime"/>
        <Column Name="SERLIST_TIME" DataType="System.TimeSpan"/>
        <Column Name="SER_PRICE_TOTAL" DataType="System.Decimal"/>
        <Column Name="SERLIST_NOTE" DataType="System.String"/>
        <Column Name="SER_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="VIS_NAME" DataType="System.String"/>
        <Column Name="VIS_DATE" DataType="System.DateTime"/>
        <Column Name="VIS_TIME" DataType="System.TimeSpan"/>
        <Column Name="VIS_TYPE" DataType="System.String"/>
        <Column Name="VIS_PRICE" DataType="System.Decimal"/>
        <Column Name="VIS_DISCOUNT" DataType="System.Decimal"/>
        <Column Name="VIS_TOTAL" DataType="System.Decimal"/>
        <Column Name="VIS_NOTE" DataType="System.String"/>
        <Column Name="VIS_PAY_TYPE" DataType="System.String"/>
        <Column Name="VIS_UNPAY" DataType="System.Decimal"/>
        <Column Name="DOC_NAME" DataType="System.String"/>
        <Column Name="DOC_MAJOR" DataType="System.String"/>
        <Column Name="DOC_CODE" DataType="System.Decimal"/>
        <Column Name="VIS_CODE" DataType="System.Decimal"/>
        <Column Name="DOC_EXP" DataType="System.String"/>
        <Column Name="DOC_BD" DataType="System.DateTime"/>
        <Column Name="DOC_MOBILE" DataType="System.String"/>
        <Column Name="DOC_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_CODE" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="CLI_LOC" DataType="System.String"/>
        <Column Name="CLI_NOTE" DataType="System.String"/>
        <Column Name="CUST_CODE" DataType="System.Decimal"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <Column Name="CLI_T_ID" DataType="System.Decimal"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="SER_CODE" DataType="System.Decimal"/>
        <Column Name="SER_NAME" DataType="System.String"/>
        <Column Name="SER_TYPE" DataType="System.String"/>
        <Column Name="SER_PRICE" DataType="System.String"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="0"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="0"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="CUST_ID" DataType="System.String"/>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
    <Total Name="SUM_PRICE" Expression="[repSERLIST.SER_PRICE]" Evaluator="Data1" PrintOn="ReportSummary1" ResetOnReprint="true"/>
    <Total Name="SER_COUNT" TotalType="Count" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_VIS_PRICE" Expression="[repSERLIST.SER_PRICE]" Evaluator="Data1" PrintOn="GroupFooter1" ResetOnReprint="true"/>
    <Total Name="COUNT_VIS_SER" TotalType="Count" Evaluator="Data1" PrintOn="GroupFooter1"/>
  </Dictionary>
  <ReportPage Name="Page1" RawPaperSize="9" LeftMargin="0" TopMargin="0" RightMargin="0" BottomMargin="0" StartPageEvent="Page1_StartPage">
    <ReportTitleBand Name="ReportTitle1" Width="793.8" Height="245.7">
      <PictureObject Name="Picture1" Left="9.45" Top="9.45" Width="151.2" Height="132.3"/>
      <PictureObject Name="Picture2" Left="633.15" Top="9.45" Width="151.2" Height="132.3"/>
      <TextObject Name="Text28" Left="165.37" Top="9.45" Width="463.05" Height="47.25" Text="[repSERLIST.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt, style=Bold"/>
      <TextObject Name="Text29" Left="165.37" Top="56.7" Width="463.05" Height="47.25" Text="[repSERLIST.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Bold, Italic"/>
      <TextObject Name="Text32" Left="240.97" Top="141.75" Width="302.4" Height="18.9" Text="تقرير اجراءات المريض الكامل" HorzAlign="Center" VertAlign="Center" Font="Arial, 14pt, style=Bold, Underline"/>
      <LineObject Name="Line1" Left="9.45" Top="170.1" Width="774.9"/>
      <TextObject Name="Text10" Left="675.67" Top="179.55" Width="37.8" Height="18.9" Border.Lines="All" Text="[repSERLIST.CUST_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text11" Left="477.22" Top="179.55" Width="198.45" Height="18.9" Border.Lines="All" Text="[repSERLIST.CUST_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text12" Left="713.47" Top="179.55" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": المريض" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text13" Left="713.47" Top="207.9" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": العيادة" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text14" Left="477.22" Top="207.9" Width="236.25" Height="18.9" Border.Lines="All" Text="[repSERLIST.CLI_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text17" Left="354.38" Top="179.55" Width="103.95" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": جنس المريض" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text18" Left="250.42" Top="179.55" Width="103.95" Height="18.9" Border.Lines="All" Text="[repSERLIST.CUST_GENDER]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text19" Left="155.92" Top="179.55" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": عمرالمريض" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text20" Left="80.32" Top="179.55" Width="75.6" Height="18.9" Border.Lines="All" Text="[repSERLIST.CUST_AGE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text21" Left="14.17" Top="179.55" Width="66.15" Height="18.9" Border.Lines="All" Text="[repSERLIST.CUST_AGE_MONTH]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text22" Left="193.72" Top="207.9" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": تاريخ الطباعة" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text23" Left="14.17" Top="207.9" Width="179.55" Height="18.9" Border.Lines="All" Text="[Date]" Format="Date" Format.Format="d" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line2" Left="9.45" Top="236.25" Width="774.9"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="249.03" Width="793.8"/>
    <GroupHeaderBand Name="GroupHeader1" Top="252.37" Width="793.8" Height="28.35" KeepWithData="true" Condition="[repSERLIST.VIS_ID]">
      <TextObject Name="Text1" Left="23.62" Top="9.45" Width="746.55" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[repSERLIST.VIS_TIME]/ [repSERLIST.VIS_DATE]/ [[repSERLIST.VIS_ID]] : رقم الزيارة" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <DataBand Name="Data1" Top="306.28" Width="793.8" Height="18.9" DataSource="Table" KeepTogether="true" KeepDetail="true">
        <TextObject Name="Text2" Left="675.68" Width="94.5" Height="18.9" Border.Lines="All" Text="[repSERLIST.SER_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
        <TextObject Name="Text4" Left="467.78" Width="207.9" Height="18.9" Border.Lines="All" Text="[repSERLIST.SER_NAME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text6" Left="316.57" Width="151.2" Height="18.9" Border.Lines="All" Text="[repSERLIST.SER_TYPE]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text8" Left="222.07" Width="94.5" Height="18.9" Border.Lines="All" Text="[repSERLIST.SER_PRICE]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="2" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text15" Left="23.62" Width="198.45" Height="18.9" Border.Lines="All" Text="[repSERLIST.DOC_NAME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <DataHeaderBand Name="DataHeader1" Top="284.05" Width="793.8" Height="18.9" KeepWithData="true">
          <TextObject Name="Text3" Left="675.68" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="رقم الاجراء" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text5" Left="467.78" Width="207.9" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="اسم الاجراء" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text7" Left="316.57" Width="151.2" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="نوع الاجراء" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text9" Left="222.07" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="سعر الاجراء" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
          <TextObject Name="Text16" Left="23.62" Width="198.45" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="اسم الطبيب" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        </DataHeaderBand>
      </DataBand>
      <GroupFooterBand Name="GroupFooter1" Top="328.52" Width="793.8" Height="34.02" KeepWithData="true">
        <TextObject Name="Text40" Left="23.06" Top="1.89" Width="292.95" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="[SUM_VIS_PRICE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
        <TextObject Name="Text41" Left="467.96" Top="1.89" Width="207.9" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="[COUNT_VIS_SER]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
        <TextObject Name="Text42" Left="675.86" Top="1.89" Width="94.5" Height="18.9" Text="عدد الاجراءات" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
        <TextObject Name="Text43" Left="316.39" Top="1.89" Width="151.2" Height="18.9" Text="مجموع السعر" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
        <LineObject Name="Line4" Left="9.45" Top="33.26" Width="774.9" Fill.Color="Gainsboro"/>
      </GroupFooterBand>
    </GroupHeaderBand>
    <ReportSummaryBand Name="ReportSummary1" Top="365.87" Width="793.8" Height="28.35">
      <TextObject Name="Text24" Left="23.06" Width="292.95" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="[SUM_PRICE]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="2" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text25" Left="467.96" Width="207.9" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="[SER_COUNT]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text26" Left="675.86" Width="94.5" Height="18.9" Text="عدد الكلي" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text27" Left="317.52" Width="151.2" Height="18.9" Text="اجمالي السعر" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <LineObject Name="Line3" Left="9.45" Top="26.46" Width="774.9"/>
    </ReportSummaryBand>
    <PageFooterBand Name="PageFooter1" Top="397.55" Width="793.8" Height="94.5">
      <TextObject Name="Text33" Left="302.4" Top="75.6" Width="94.5" Height="18.9" Text="[Page#]" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text34" Left="396.9" Top="75.6" Width="94.5" Height="18.9" Text="[TotalPages#]" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text44" Left="18.9" Top="9.45" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[repSERLIST.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="670.95" Top="9.45" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text45" Left="18.9" Top="37.8" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[repSERLIST.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="670.95" Top="37.8" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
