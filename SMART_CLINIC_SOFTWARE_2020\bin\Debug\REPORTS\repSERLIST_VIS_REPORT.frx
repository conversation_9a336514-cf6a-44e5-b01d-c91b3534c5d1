﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="09/25/2022 14:29:21" ReportInfo.Modified="03/25/2023 15:59:03" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHEIn0B2jWdRlGrYKmRSKaPG">
      <TableDataSource Name="Table" Alias="SER_LIST_REP" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        SERLIST_TBL.SERLIST_ID, SERLIST_TBL.SERLIST_CODE, SERLIST_TBL.SERLIST_NAME, SERLIST_TBL.SERLIST_DATE, SERLIST_TBL.SERLIST_TIME, SERLIST_TBL.SER_PRICE_TOTAL, SERLIST_TBL.SERLIST_NOTE, &#13;&#10;                         SERLIST_TBL.SER_ID, SERLIST_TBL.CLI_ID, SERLIST_TBL.CUST_ID, SERLIST_TBL.VIS_ID, VISIT_TBL.VIS_CODE, VISIT_TBL.VIS_NAME, VISIT_TBL.VIS_DATE, VISIT_TBL.VIS_TIME, VISIT_TBL.VIS_TYPE, VISIT_TBL.VIS_PRICE,&#13;&#10;                          VISIT_TBL.VIS_DISCOUNT, VISIT_TBL.VIS_TOTAL, VISIT_TBL.VIS_NOTE, VISIT_TBL.VIS_PAY_TYPE, VISIT_TBL.VIS_UNPAY, DOCTORS_TBL.DOC_CODE, DOCTORS_TBL.DOC_NAME, DOCTORS_TBL.DOC_MAJOR, &#13;&#10;                         DOCTORS_TBL.DOC_EXP, DOCTORS_TBL.DOC_BD, DOCTORS_TBL.DOC_MOBILE, DOCTORS_TBL.DOC_ADDRESS, CLINC_TBL.CLI_CODE, CLINC_TBL.CLI_NAME, CLINC_TBL.CLI_LOC, CLINC_TBL.CLI_NOTE, &#13;&#10;                         CUST_TBL.CUST_CODE, CUST_TBL.CUST_AGE, CUST_TBL.CUST_BD, CUST_TBL.CUST_MOBILE1, CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_GENDER, CUST_TBL.CUST_NATION, &#13;&#10;                         CUST_TBL.CUST_AGE_MONTH, CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME, CLINIC_TITLE_TBL.CLI_T_ID, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_NAME, CLINIC_TITLE_TBL.CLI_T_TITLE, CLINIC_TITLE_TBL.CLI_T_ADDRESS, CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE, SERVICE_TBL.SER_CODE, &#13;&#10;                         SERVICE_TBL.SER_NAME, SERVICE_TBL.SER_TYPE, SERVICE_TBL.SER_PRICE, SERVICE_TBL.SER_NOTE&#13;&#10;FROM            SERLIST_TBL LEFT OUTER JOIN&#13;&#10;                         VISIT_TBL ON SERLIST_TBL.VIS_ID = VISIT_TBL.VIS_ID LEFT OUTER JOIN&#13;&#10;                         DOCTORS_TBL ON VISIT_TBL.DOC_ID = DOCTORS_TBL.DOC_ID INNER JOIN&#13;&#10;                         CLINC_TBL ON SERLIST_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         CUST_TBL ON SERLIST_TBL.CUST_ID = CUST_TBL.CUST_ID INNER JOIN&#13;&#10;                         CLINIC_TITLE_TBL ON CLINC_TBL.CLI_ID = CLINIC_TITLE_TBL.CLI_ID INNER JOIN&#13;&#10;                         SERVICE_TBL ON SERLIST_TBL.SER_ID = SERVICE_TBL.SER_ID&#13;&#10;WHERE        (SERLIST_TBL.VIS_ID = @VIS_ID) AND (CUST_TBL.CUST_ID = @CUST_ID) AND (SERLIST_TBL.CLI_ID = @CLI_ID)&#13;&#10;ORDER BY SERLIST_TBL.SERLIST_CODE">
        <Column Name="SERLIST_ID" DataType="System.Decimal"/>
        <Column Name="SERLIST_CODE" DataType="System.Decimal"/>
        <Column Name="SERLIST_NAME" DataType="System.String"/>
        <Column Name="SERLIST_DATE" DataType="System.DateTime"/>
        <Column Name="SERLIST_TIME" DataType="System.TimeSpan"/>
        <Column Name="SER_PRICE_TOTAL" DataType="System.Decimal"/>
        <Column Name="SERLIST_NOTE" DataType="System.String"/>
        <Column Name="SER_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="VIS_CODE" DataType="System.Decimal"/>
        <Column Name="VIS_NAME" DataType="System.String"/>
        <Column Name="VIS_DATE" DataType="System.DateTime"/>
        <Column Name="VIS_TIME" DataType="System.TimeSpan"/>
        <Column Name="VIS_TYPE" DataType="System.String"/>
        <Column Name="VIS_PRICE" DataType="System.Decimal"/>
        <Column Name="VIS_DISCOUNT" DataType="System.Decimal"/>
        <Column Name="VIS_TOTAL" DataType="System.Decimal"/>
        <Column Name="VIS_NOTE" DataType="System.String"/>
        <Column Name="VIS_PAY_TYPE" DataType="System.String"/>
        <Column Name="VIS_UNPAY" DataType="System.Decimal"/>
        <Column Name="DOC_CODE" DataType="System.Decimal"/>
        <Column Name="DOC_NAME" DataType="System.String"/>
        <Column Name="DOC_MAJOR" DataType="System.String"/>
        <Column Name="DOC_EXP" DataType="System.String"/>
        <Column Name="DOC_BD" DataType="System.DateTime"/>
        <Column Name="DOC_MOBILE" DataType="System.String"/>
        <Column Name="DOC_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_CODE" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="CLI_LOC" DataType="System.String"/>
        <Column Name="CLI_NOTE" DataType="System.String"/>
        <Column Name="CUST_CODE" DataType="System.Decimal"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <Column Name="CLI_T_ID" DataType="System.Decimal"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <Column Name="SER_CODE" DataType="System.Decimal"/>
        <Column Name="SER_NAME" DataType="System.String"/>
        <Column Name="SER_TYPE" DataType="System.String"/>
        <Column Name="SER_PRICE" DataType="System.Decimal"/>
        <Column Name="SER_NOTE" DataType="System.String"/>
        <CommandParameter Name="VIS_ID " DataType="22" Size="200" Expression="[VIS_ID]" DefaultValue="0"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="0"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="0"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="VIS_ID" DataType="System.String"/>
    <Parameter Name="CUST_ID" DataType="System.String"/>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
    <Parameter Name="DOC_NAME" DataType="System.String"/>
    <Total Name="SER_COUNT" TotalType="Count" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_PRICE" Expression="[SER_LIST_REP.SER_PRICE]" Evaluator="Data1" PrintOn="ReportSummary1"/>
  </Dictionary>
  <ReportPage Name="Page1" PaperHeight="150" LeftMargin="0" TopMargin="0" RightMargin="0" BottomMargin="0" StartPageEvent="Page1_StartPage">
    <ReportTitleBand Name="ReportTitle1" Width="793.8" Height="245.7">
      <TextObject Name="Text1" Left="151.2" Top="18.9" Width="491.4" Height="56.7" Text="[SER_LIST_REP.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt, style=Bold"/>
      <TextObject Name="Text2" Left="151.2" Top="75.6" Width="491.4" Height="37.8" Text="[SER_LIST_REP.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <PictureObject Name="Picture1" Left="18.9" Top="18.9" Width="132.3" Height="122.85" SizeMode="StretchImage"/>
      <PictureObject Name="Picture2" Left="642.6" Top="18.9" Width="132.3" Height="122.85" SizeMode="StretchImage"/>
      <TextObject Name="Text35" Left="217.35" Top="132.3" Width="340.2" Height="28.35" Text="تقرير اجراءات المريض" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Underline"/>
      <LineObject Name="Line1" Left="9.45" Top="170.1" Width="774.9"/>
      <TextObject Name="Text5" Left="633.15" Top="179.55" Width="75.6" Height="18.9" Border.Lines="All" Text="[SER_LIST_REP.CUST_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text6" Left="387.45" Top="179.55" Width="245.7" Height="18.9" Border.Lines="All" Text="[SER_LIST_REP.CUST_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text7" Left="217.35" Top="179.55" Width="75.6" Height="18.9" Border.Lines="All" Text="[SER_LIST_REP.CUST_GENDER]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text8" Left="75.6" Top="179.55" Width="75.6" Height="18.9" Border.Lines="All" Text="[SER_LIST_REP.CUST_AGE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text9" Left="18.9" Top="179.55" Width="56.7" Height="18.9" Border.Lines="All" Text="[SER_LIST_REP.CUST_AGE_MONTH]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text10" Left="708.75" Top="179.55" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="255, 224, 192" Text=": المريض" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text11" Left="292.95" Top="179.55" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 224, 192" Text=": الجنس" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text12" Left="151.2" Top="179.55" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="255, 224, 192" Text=": العمر" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text13" Left="387.45" Top="207.9" Width="321.3" Height="18.9" Border.Lines="All" Text="[SER_LIST_REP.CLI_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text14" Left="708.75" Top="207.9" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="255, 224, 192" Text=": العيادة" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <LineObject Name="Line2" Left="9.45" Top="236.25" Width="774.9"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="249.03" Width="793.8" Height="18.9">
      <TextObject Name="Text22" Left="396.9" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="تاريخ الاجراء" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text24" Left="302.4" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="وقت الاجراء" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text26" Left="207.9" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="السعر" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text28" Left="94.5" Width="113.4" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="نوع الاجراء" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text30" Left="18.9" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="رقم الزيارة" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text20" Left="491.4" Width="207.9" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="اسم الاجراء" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text18" Left="699.3" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="Gainsboro" Text="رقم الاجراء" HorzAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageHeaderBand>
    <DataBand Name="Data1" Top="271.27" Width="793.8" Height="18.9" DataSource="Table" KeepTogether="true" KeepDetail="true">
      <TextObject Name="Text3" Left="699.3" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[SER_LIST_REP.SERLIST_CODE]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text17" Left="491.4" Width="207.9" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[SER_LIST_REP.SERLIST_NAME]" HorzAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text19" Left="396.9" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[SER_LIST_REP.SERLIST_DATE]" Format="Date" Format.Format="d" HorzAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text21" Left="302.4" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[SER_LIST_REP.SERLIST_TIME]" HorzAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text23" Left="207.9" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[SER_LIST_REP.SER_PRICE]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="3" Format.DecimalSeparator="." Format.GroupSeparator="," Format.CurrencySymbol="د.ع.‏" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text25" Left="94.5" Width="113.4" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[SER_LIST_REP.SER_TYPE]" HorzAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text27" Left="18.9" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text="[SER_LIST_REP.VIS_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
    </DataBand>
    <ReportSummaryBand Name="ReportSummary1" Top="293.5" Width="793.8" Height="128.52" KeepWithData="true">
      <TextObject Name="Text31" Left="576.45" Top="1.89" Width="94.5" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text="[SER_COUNT]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt"/>
      <TextObject Name="Text33" Left="670.95" Top="1.89" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عدد الاجراءات" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text34" Left="302.4" Top="1.89" Width="94.5" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": مجموع السعر" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text4" Left="207.9" Top="1.89" Width="94.5" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text="[SUM_PRICE]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt"/>
      <TextObject Name="Text16" Left="75.6" Top="75.6" Width="151.2" Height="18.9" Fill.Color="White" Text="اسم الطبيب وتوقيعه" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text15" Left="18.9" Top="103.95" Width="274.05" Height="18.9" Text="[DOC_NAME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </ReportSummaryBand>
    <PageFooterBand Name="PageFooter1" Top="425.35" Width="793.8" Height="132.3">
      <TextObject Name="Text36" Left="274.05" Top="94.5" Width="94.5" Height="18.9" Text="[Page#]" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text37" Left="368.55" Top="94.5" Width="122.85" Height="18.9" Text="[TotalPages#]" HorzAlign="Center" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text40" Left="18.9" Top="9.45" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[SER_LIST_REP.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="670.95" Top="9.45" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text41" Left="18.9" Top="37.8" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[SER_LIST_REP.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="670.95" Top="37.8" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
