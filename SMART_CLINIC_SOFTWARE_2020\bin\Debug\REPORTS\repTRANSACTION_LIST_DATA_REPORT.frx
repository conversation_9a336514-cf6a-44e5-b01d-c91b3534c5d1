﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="08/18/2021 13:30:58" ReportInfo.Modified="10/06/2022 00:04:05" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
      if(((String)Report.GetParameterValue(&quot;CUST_NAME&quot;)) == &quot;&quot;)
      {
        lblCUST_NAME.Visible = false;
        txtCUST_NAME.Visible = false;
      }
      else
      {
        lblCUST_NAME.Visible = true;
        txtCUST_NAME.Visible = true;
      }
      
      if(((String)Report.GetParameterValue(&quot;CLI_NAME&quot;)) == &quot;&quot;)
      {
        lblCLI_NAME.Visible = false;
        txtCLI_NAME.Visible = false;
      }
      else
      {
        lblCLI_NAME.Visible = true;
        txtCLI_NAME.Visible = true;
      }
      
      if(((String)Report.GetParameterValue(&quot;COM_NAME&quot;)) == &quot;&quot;)
      {
        lblCOM_NAME.Visible = false;
        txtCOM_NAME.Visible = false;
      }
      else
      {
        lblCOM_NAME.Visible = true;
        txtCOM_NAME.Visible = true;
      }
      
      if(((String)Report.GetParameterValue(&quot;T_TYPE&quot;)) == &quot;&quot;)
      {
        lblT_TYPE.Visible = false;
        txtT_TYPE.Visible = false;
      }
      else
      {
        lblT_TYPE.Visible = true;
        txtT_TYPE.Visible = true;
      }
      
      if(((String)Report.GetParameterValue(&quot;COM_NAME&quot;)) == &quot;&quot;)
      {
        lblCUST_PRICE.Visible = false;
        lblCOM_PRICE.Visible = false;
        txtCUST_PRICE.Visible = false;
        txtCOM_PRICE.Visible = false;
      }
      else
      {
        lblCUST_PRICE.Visible = true;
        lblCOM_PRICE.Visible = true;
        txtCUST_PRICE.Visible = true;
        txtCOM_PRICE.Visible = true;
      }
      
      if(((String)Report.GetParameterValue(&quot;VIS_ID&quot;)) == &quot;&quot;)
      {
        lblVIS_ID.Visible = false;
        txtVIS_ID.Visible = false;
      }
      else
      {
        lblVIS_ID.Visible = true;
        txtVIS_ID.Visible = true;
      }
      
      if(((String)Report.GetParameterValue(&quot;T_ID&quot;)) == &quot;&quot;)
      {
        lblT_ID.Visible = false;
        txtT_ID.Visible = false;
      }
      else
      {
        lblT_ID.Visible = true;
        txtT_ID.Visible = true;
      }
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqIVd0v8qjIG7dW856t8miNlYuokVlLWrw103KuTjD34SQjH7VU9T+P7O7O3P+qD7c08H72S9mcGumsuecH/KePvoaA48urNcqYu+oVpDVNVTrph+LeULMGdwRQngIMkg45KutDabPejdKfsq1y+MKZmAB/fujv81RawUKqjpXtjW+oyBW0DPVzr0miB84JmU7IUSb4lzCrz+A8IP/ZvLP+3rSzUK0w2wtKs9QlOMWNd3AaAtLHGV0Qjx1B2i/nLUyTcqeSgk4nAlJEWb2KLUpXw==">
      <TableDataSource Name="Table1" Alias="TRANSACTION_LIST_DATA_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="EXEC dbo.TRANSACTION_LIST_DATA_REPORT @CUST_NAME ,@COM_NAME ,@CLI_NAME ,@VIS_ID ,@T_ID ,@T_TYPE ,@F_DATE ,@S_DATE  ">
        <Column Name="T_ID" DataType="System.Decimal"/>
        <Column Name="T_CODE" DataType="System.Decimal"/>
        <Column Name="T_NAME" DataType="System.String"/>
        <Column Name="T_DATE" DataType="System.DateTime"/>
        <Column Name="T_TIME" DataType="System.TimeSpan"/>
        <Column Name="T_TYPE" DataType="System.String"/>
        <Column Name="T_PRICE" DataType="System.Decimal"/>
        <Column Name="T_DISCOUNT" DataType="System.Decimal"/>
        <Column Name="T_UNPAY" DataType="System.Decimal"/>
        <Column Name="T_TOTAL" DataType="System.Decimal"/>
        <Column Name="CUST_PRICE" DataType="System.Decimal"/>
        <Column Name="COMPANY_PRICE" DataType="System.Decimal"/>
        <Column Name="T_NOTE" DataType="System.String"/>
        <Column Name="T_STATE" DataType="System.String"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="COM_ID" DataType="System.Decimal"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <Column Name="CARD_ID" DataType="System.Decimal"/>
        <Column Name="CARD_NAME" DataType="System.String"/>
        <Column Name="COM_NAME" DataType="System.String"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="CLI_T_ID" DataType="System.Decimal"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <CommandParameter Name="VIS_ID" DataType="22" Size="200" Expression="[VIS_ID]"/>
        <CommandParameter Name="T_ID" DataType="22" Size="200" Expression="[T_ID]"/>
        <CommandParameter Name="T_TYPE" DataType="22" Size="200" Expression="[T_TYPE]"/>
        <CommandParameter Name="CUST_NAME" DataType="22" Size="200" Expression="[CUST_NAME]"/>
        <CommandParameter Name="COM_NAME" DataType="22" Size="200" Expression="[COM_NAME]"/>
        <CommandParameter Name="CLI_NAME" DataType="22" Size="200" Expression="[CLI_NAME]"/>
        <CommandParameter Name="F_DATE" DataType="22" Size="200" Expression="[F_DATE]" DefaultValue="01/01/1900"/>
        <CommandParameter Name="S_DATE" DataType="22" Size="200" Expression="[S_DATE]" DefaultValue="01/01/2900"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="VIS_ID" DataType="System.String"/>
    <Parameter Name="T_ID" DataType="System.String"/>
    <Parameter Name="T_TYPE" DataType="System.String"/>
    <Parameter Name="CUST_NAME" DataType="System.String"/>
    <Parameter Name="COM_NAME" DataType="System.String"/>
    <Parameter Name="CLI_NAME" DataType="System.String"/>
    <Parameter Name="F_DATE" DataType="System.String"/>
    <Parameter Name="S_DATE" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
    <Total Name="COUNT_T" TotalType="Count" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_PRICE" Expression="[TRANSACTION_LIST_DATA_REPORT.T_PRICE]" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_DISCOUNT" Expression="[TRANSACTION_LIST_DATA_REPORT.T_DISCOUNT]" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_UNPAY" Expression="[TRANSACTION_LIST_DATA_REPORT.T_UNPAY]" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_Total" Expression="[TRANSACTION_LIST_DATA_REPORT.T_TOTAL]" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_CUST_PRICE" Expression="[TRANSACTION_LIST_DATA_REPORT.CUST_PRICE]" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_COM_PRICE" Expression="[TRANSACTION_LIST_DATA_REPORT.COMPANY_PRICE]" Evaluator="Data1" PrintOn="ReportSummary1"/>
  </Dictionary>
  <ReportPage Name="Page1" Landscape="true" PaperWidth="297" PaperHeight="210" RawPaperSize="9" LeftMargin="0" TopMargin="0" RightMargin="0" FirstPageSource="15" OtherPagesSource="15" StartPageEvent="Page1_StartPage">
    <ReportTitleBand Name="ReportTitle1" Width="1122.66" Height="170.1">
      <TextObject Name="Text35" Left="391.23" Top="141.75" Width="340.2" Height="28.35" Text="تقرير القيود والحركات الحسابية" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Underline"/>
      <TextObject Name="Text36" Left="258.93" Top="9.45" Width="604.8" Height="37.8" Text="[TRANSACTION_LIST_DATA_REPORT.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt"/>
      <TextObject Name="Text37" Left="254.2" Top="56.7" Width="614.25" Height="47.25" Text="[TRANSACTION_LIST_DATA_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <PictureObject Name="Picture1" Left="37.8" Top="9.45" Width="141.75" Height="141.75" SizeMode="StretchImage"/>
      <PictureObject Name="Picture2" Left="935.55" Top="9.45" Width="141.75" Height="141.75" SizeMode="StretchImage"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="173.43" Width="1122.66" Height="94.5">
      <LineObject Name="Line1" Left="-0.94" Top="9.45" Width="1124.55" Border.Width="2"/>
      <TextObject Name="txtCLI_NAME" Left="599.13" Top="56.7" Width="217.35" Height="18.9" Border.Lines="All" Text="[CLI_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtCUST_NAME" Left="599.13" Top="28.35" Width="217.35" Height="18.9" Border.Lines="All" Text="[CUST_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtT_TYPE" Left="325.08" Top="28.35" Width="189" Height="18.9" Border.Lines="All" Text="[T_TYPE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtT_ID" Left="325.08" Top="56.7" Width="189" Height="18.9" Border.Lines="All" Text="[T_ID]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtF_DATE" Left="901.53" Top="28.35" Width="113.4" Height="18.9" Border.Lines="All" Text="[F_DATE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtS_DATE" Left="901.53" Top="56.7" Width="113.4" Height="18.9" Border.Lines="All" Text="[S_DATE]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line2" Left="-0.94" Top="85.05" Width="1124.55" Border.Width="2"/>
      <TextObject Name="lblF_DATE" Left="1014.93" Top="28.35" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": من تاريخ" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="lblS_DATE" Left="1014.93" Top="56.7" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": الى تاريخ" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="lblCUST_NAME" Left="816.48" Top="28.35" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم المريض" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="lblCLI_NAME" Left="816.48" Top="56.7" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم العيادة" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="lblT_TYPE" Left="514.08" Top="28.35" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": نوع القيد" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="lblT_ID" Left="514.08" Top="56.7" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": رقم القيد" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtCOM_NAME" Left="22.68" Top="28.35" Width="189" Height="18.9" Border.Lines="All" Text="[COM_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtVIS_ID" Left="22.68" Top="56.7" Width="189" Height="18.9" Border.Lines="All" Text="[VIS_ID]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="lblCOM_NAME" Left="211.68" Top="28.35" Width="113.4" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم شركة التامين" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="lblVIS_ID" Left="211.68" Top="56.7" Width="113.4" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": رقم الزيارة" Font="Arial, 11pt, style=Bold"/>
    </PageHeaderBand>
    <DataBand Name="Data1" Top="293.5" Width="1122.66" Height="18.9" DataSource="Table1">
      <TextObject Name="Text7" Left="1057.46" Width="47.25" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.T_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text11" Left="896.81" Width="160.65" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.T_DATE]" Format="Date" Format.Format="d" HorzAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text15" Left="736.16" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.T_PRICE]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="2" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text17" Left="660.56" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.T_DISCOUNT]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="2" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text19" Left="584.96" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.T_UNPAY]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="2" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text21" Left="499.91" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.T_TOTAL]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="2" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text23" Left="414.86" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.CUST_PRICE]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="2" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text25" Left="329.81" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.COMPANY_PRICE]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="2" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text9" Left="188.06" Width="141.75" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.CUST_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text27" Left="74.66" Width="113.4" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.COM_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text29" Left="8.51" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.VIS_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 10pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text1" Left="812.7" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[TRANSACTION_LIST_DATA_REPORT.T_TIME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <DataHeaderBand Name="DataHeader1" Top="271.27" Width="1122.66" Height="18.9">
        <TextObject Name="Text8" Left="1057.46" Width="47.25" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="رقم القيد" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text12" Left="896.81" Width="160.65" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="التاريخ" Format="Date" Format.Format="MMMM dd, yyyy" HorzAlign="Right" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text14" Left="811.76" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="الوقت" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text16" Left="736.16" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="القيمة" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text18" Left="660.56" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="الخصم" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text20" Left="584.96" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="الباقي" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text22" Left="499.91" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="المجموع" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text24" Left="414.86" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="قيمة المريض" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text26" Left="329.81" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="قيمة الشركة" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text10" Left="188.06" Width="141.75" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="اسم المريض" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text28" Left="74.66" Width="113.4" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="اسم الشركة" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
        <TextObject Name="Text30" Left="8.51" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="رقم الزيارة" HorzAlign="Center" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <ReportSummaryBand Name="ReportSummary1" Top="315.73" Width="1122.66" Height="132.3">
      <LineObject Name="Line3" Top="9.45" Width="1124.55" Border.Width="2"/>
      <TextObject Name="Text56" Left="812.7" Top="18.9" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text="[COUNT_T]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text57" Left="812.7" Top="37.8" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text="[SUM_PRICE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text58" Left="812.7" Top="56.7" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text="[SUM_DISCOUNT]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text59" Left="812.7" Top="75.6" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text="[SUM_UNPAY]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text31" Left="812.7" Top="94.5" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text="[SUM_Total]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text60" Left="1001.7" Top="18.9" Width="103.95" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": عدد القيود" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text61" Left="1001.7" Top="37.8" Width="103.95" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": مجموع السعر" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text62" Left="1001.7" Top="56.7" Width="103.95" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": مجموع الخصم" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text63" Left="1001.7" Top="75.6" Width="103.95" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": مجموع الباقي" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text55" Left="1001.7" Top="94.5" Width="103.95" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": مجموع الاجمالي" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line4" Top="122.85" Width="1124.55" Border.Width="2"/>
      <TextObject Name="txtCUST_PRICE" Left="340.2" Top="18.9" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text="[SUM_CUST_PRICE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="txtCOM_PRICE" Left="340.2" Top="37.8" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text="[SUM_COM_PRICE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="lblCUST_PRICE" Left="529.2" Top="18.9" Width="160.65" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": مجموع قيمة المريض" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="lblCOM_PRICE" Left="529.2" Top="37.8" Width="160.65" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": مجموع قيمة شركة التامين" Font="Arial, 11pt, style=Bold"/>
    </ReportSummaryBand>
    <PageFooterBand Name="PageFooter1" Top="451.37" Width="1122.66" Height="85.05">
      <TextObject Name="Text68" Left="414.86" Top="66.15" Width="160.65" Height="18.9" Text="[TotalPages#] / [Page#]" HorzAlign="Right" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text54" Left="18.9" Width="982.8" Height="37.8" Border.Lines="All" Fill.Color="255, 255, 192" Text="[TRANSACTION_LIST_DATA_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="1001.7" Width="103.95" Height="37.8" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text69" Left="18.9" Top="37.8" Width="982.8" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[TRANSACTION_LIST_DATA_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="1001.7" Top="37.8" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
