﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="08/09/2021 23:56:41" ReportInfo.Modified="10/05/2022 23:09:07" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHFBOgkBzOYAMeElw+578PZK">
      <TableDataSource Name="Table" Alias="repTRANSACTION_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        TRANSACTION_TBL.T_ID, TRANSACTION_TBL.T_CODE, TRANSACTION_TBL.T_NAME, convert(varchar , TRANSACTION_TBL.T_DATE, 103) as T_DATE, convert(varchar(5) , TRANSACTION_TBL.T_TIME, 108) as T_TIME, &#13;&#10;                         TRANSACTION_TBL.T_TYPE, TRANSACTION_TBL.T_PRICE, TRANSACTION_TBL.T_DISCOUNT, TRANSACTION_TBL.T_UNPAY, TRANSACTION_TBL.T_TOTAL, &#13;&#10;                         TRANSACTION_TBL.CUST_PRICE, TRANSACTION_TBL.COMPANY_PRICE, TRANSACTION_TBL.T_NOTE, TRANSACTION_TBL.T_STATE, TRANSACTION_TBL.VIS_ID, &#13;&#10;                         TRANSACTION_TBL.CLI_ID, TRANSACTION_TBL.COM_ID, CLINC_TBL.CLI_NAME, CLINIC_TITLE_TBL.CLI_T_ID, CLINIC_TITLE_TBL.CLI_T_NAME, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_TITLE, CLINIC_TITLE_TBL.CLI_T_ADDRESS, CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE&#13;&#10;FROM            TRANSACTION_TBL INNER JOIN&#13;&#10;                         CLINC_TBL ON TRANSACTION_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         CLINIC_TITLE_TBL ON CLINC_TBL.CLI_ID = CLINIC_TITLE_TBL.CLI_ID&#13;&#10;WHERE        (TRANSACTION_TBL.T_CODE = @T_CODE) AND (TRANSACTION_TBL.CLI_ID = @CLI_ID) AND (TRANSACTION_TBL.T_NAME &lt;&gt; 'كشف علاجي')">
        <Column Name="T_ID" DataType="System.Decimal"/>
        <Column Name="T_CODE" DataType="System.Decimal"/>
        <Column Name="T_NAME" DataType="System.String"/>
        <Column Name="T_DATE" DataType="System.String"/>
        <Column Name="T_TIME" DataType="System.String"/>
        <Column Name="T_TYPE" DataType="System.String"/>
        <Column Name="T_PRICE" DataType="System.Decimal"/>
        <Column Name="T_DISCOUNT" DataType="System.Decimal"/>
        <Column Name="T_UNPAY" DataType="System.Decimal"/>
        <Column Name="T_TOTAL" DataType="System.Decimal"/>
        <Column Name="CUST_PRICE" DataType="System.Decimal"/>
        <Column Name="COMPANY_PRICE" DataType="System.Decimal"/>
        <Column Name="T_NOTE" DataType="System.String"/>
        <Column Name="T_STATE" DataType="System.String"/>
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="COM_ID" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="CLI_T_ID" DataType="System.Decimal"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]"/>
        <CommandParameter Name="T_CODE" DataType="22" Size="200" Expression="[T_CODE]"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="T_CODE" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
  </Dictionary>
  <ReportPage Name="Page1" PaperHeight="150" LeftMargin="0" TopMargin="0" RightMargin="0" FirstPageSource="15" OtherPagesSource="15" StartPageEvent="Page1_StartPage">
    <PageHeaderBand Name="PageHeader1" Width="793.8" Height="368.55">
      <TextObject Name="Text36" Left="151.2" Top="9.45" Width="491.4" Height="56.7" Text="[repTRANSACTION_REPORT.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt"/>
      <PictureObject Name="Picture1" Left="9.45" Top="9.45" Width="132.3" Height="141.75" SizeMode="StretchImage"/>
      <PictureObject Name="Picture2" Left="652.05" Top="9.45" Width="132.3" Height="141.75" SizeMode="StretchImage"/>
      <TextObject Name="Text37" Left="151.2" Top="66.15" Width="491.4" Height="47.25" Text="[repTRANSACTION_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <TextObject Name="Text35" Left="217.35" Top="141.75" Width="340.2" Height="28.35" Fill.Color="255, 255, 192" Text="تقرير قيد حسابي" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Underline"/>
      <LineObject Name="Line1" Top="179.55" Width="793.8" Border.Width="2"/>
      <TextObject Name="Text13" Left="387.45" Top="189" Width="302.4" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[repTRANSACTION_REPORT.T_ID]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text15" Left="18.9" Top="189" Width="274.05" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[repTRANSACTION_REPORT.T_TYPE]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text17" Left="689.85" Top="189" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": رقم القيد" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text41" Left="292.95" Top="189" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": نوع القيد" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text14" Left="387.45" Top="217.35" Width="302.4" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[repTRANSACTION_REPORT.CLI_NAME]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text40" Left="689.85" Top="217.35" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم العيادة" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text19" Left="18.9" Top="245.7" Width="670.95" Height="37.8" Border.Lines="All" Fill.Color="White" Text="[repTRANSACTION_REPORT.T_NAME]" Format="Date" Format.Format="d" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text20" Left="510.3" Top="292.95" Width="179.55" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[repTRANSACTION_REPORT.T_PRICE]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="3" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text43" Left="689.85" Top="245.7" Width="85.05" Height="37.8" Border.Lines="All" Fill.Color="255, 255, 192" Text=": البيان" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text44" Left="689.85" Top="292.95" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": القيمة" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text21" Left="179.55" Top="217.35" Width="113.4" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[repTRANSACTION_REPORT.T_DATE]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text45" Left="292.95" Top="217.35" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": تاريخ القيد" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text1" Left="18.9" Top="217.35" Width="85.05" Height="18.9" Border.Lines="All" Text="[repTRANSACTION_REPORT.T_TIME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text47" Left="510.3" Top="311.85" Width="179.55" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[repTRANSACTION_REPORT.T_DISCOUNT]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="3" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text48" Left="689.85" Top="311.85" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": الخصم" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text49" Left="510.3" Top="330.75" Width="179.55" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[repTRANSACTION_REPORT.T_UNPAY]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="3" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text50" Left="689.85" Top="330.75" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": الباقي" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text51" Left="510.3" Top="349.65" Width="179.55" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[repTRANSACTION_REPORT.T_TOTAL]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="3" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text52" Left="689.85" Top="349.65" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": المجموع" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text54" Left="103.95" Top="217.35" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": وقت القيد" Font="Arial, 11pt, style=Bold"/>
    </PageHeaderBand>
    <PageFooterBand Name="PageFooter1" Top="371.88" Width="793.8" Height="103.95">
      <TextObject Name="Text53" Left="359.1" Width="415.8" Height="28.35" Fill.Color="White" Text="................................................................... : اسم وتوفيع المحاسب" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text2" Left="18.9" Width="207.9" Height="28.35" Text="[Date]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text55" Left="226.8" Width="132.3" Height="28.35" Fill.Color="White" Text=": تاريخ ووقت الطباعة" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line2" Width="793.8" Border.Width="2"/>
      <TextObject Name="Text56" Left="18.9" Top="37.8" Width="652.05" Height="37.8" Border.Lines="All" Fill.Color="255, 255, 192" CanBreak="false" Text="[repTRANSACTION_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="670.95" Top="37.8" Width="103.95" Height="37.8" Border.Lines="All" Fill.Color="224, 224, 224" CanBreak="false" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text57" Left="18.9" Top="75.6" Width="652.05" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" CanBreak="false" Text="[repTRANSACTION_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="670.95" Top="75.6" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" CanBreak="false" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
