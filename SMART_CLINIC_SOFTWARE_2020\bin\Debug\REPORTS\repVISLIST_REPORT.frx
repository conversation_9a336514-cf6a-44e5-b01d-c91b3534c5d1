﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="08/15/2021 12:26:58" ReportInfo.Modified="10/08/2022 23:24:47" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
      if(((String)Report.GetParameterValue(&quot;CLI_NAME&quot;)) == &quot;&quot;)
      {
        Text48.Visible = false;
        Text3.Visible = false;
      }
      else
      {
        Text48.Visible = true;
        Text3.Visible = true;
      }
      if(((String)Report.GetParameterValue(&quot;CUST_NAME&quot;)) == &quot;&quot;)
      {
        Text47.Visible = false;
        Text4.Visible = false;
      }
      else
      {
        Text47.Visible = true;
        Text4.Visible = true;
      }
      if(((String)Report.GetParameterValue(&quot;DOC_NAME&quot;)) == &quot;&quot;)
      {
        Text49.Visible = false;
        Text5.Visible = false;
      }
      else
      {
        Text49.Visible = true;
        Text5.Visible = true;
      }
      if(((String)Report.GetParameterValue(&quot;VIS_ID&quot;)) == &quot;&quot;)
      {
        Text50.Visible = false;
        Text6.Visible = false;
      }
      else
      {
        Text50.Visible = true;
        Text6.Visible = true;
      }
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHHXnAs6UptHoCIQfpmi+ANM">
      <TableDataSource Name="Table" Alias="repVISLIST_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        VISIT_TBL.VIS_ID, VISIT_TBL.VIS_CODE, VISIT_TBL.VIS_NAME, convert(varchar , VISIT_TBL.VIS_DATE, 103) as VIS_DATE, convert(varchar(5) , VISIT_TBL.VIS_TIME, 108) as VIS_TIME, VISIT_TBL.VIS_TYPE, VISIT_TBL.CUST_ID, &#13;&#10;                         VISIT_TBL.CLI_ID, VISIT_TBL.DOC_ID, VISIT_TBL.VIS_PRICE, VISIT_TBL.VIS_DISCOUNT, VISIT_TBL.VIS_TOTAL, VISIT_TBL.VIS_NOTE, VISIT_TBL.VIS_PAY_TYPE, &#13;&#10;                         VISIT_TBL.VIS_UNPAY, CLINC_TBL.CLI_NAME, CUST_TBL.CUST_CODE, CUST_TBL.CUST_AGE, CUST_TBL.CUST_BD, CUST_TBL.CUST_MOBILE1, &#13;&#10;                         CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_SAVE_STATE, CUST_TBL.CUST_GENDER, CUST_TBL.CUST_NATION, &#13;&#10;                         CUST_TBL.CUST_AGE_MONTH, &#13;&#10;                         CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME, &#13;&#10;                         DOCTORS_TBL.DOC_NAME, DOCTORS_TBL.DOC_MAJOR, DOCTORS_TBL.DOC_BD, DOCTORS_TBL.DOC_EXP, DOCTORS_TBL.DOC_MOBILE, &#13;&#10;                         DOCTORS_TBL.DOC_ADDRESS, CLINIC_TITLE_TBL.CLI_T_ID, CLINIC_TITLE_TBL.CLI_T_NAME, CLINIC_TITLE_TBL.CLI_T_TITLE, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_ADDRESS, CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE&#13;&#10;FROM            VISIT_TBL INNER JOIN&#13;&#10;                         CLINC_TBL ON VISIT_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         CUST_TBL ON VISIT_TBL.CUST_ID = CUST_TBL.CUST_ID INNER JOIN&#13;&#10;                         DOCTORS_TBL ON VISIT_TBL.DOC_ID = DOCTORS_TBL.DOC_ID AND CLINC_TBL.CLI_ID = DOCTORS_TBL.CLI_ID INNER JOIN&#13;&#10;                         CLINIC_TITLE_TBL ON CLINC_TBL.CLI_ID = CLINIC_TITLE_TBL.CLI_ID&#13;&#10;WHERE        (VISIT_TBL.VIS_DATE BETWEEN @F_DATE AND @S_DATE) AND (CLINC_TBL.CLI_NAME LIKE '%' + @CLI_NAME + '%') AND &#13;&#10;                         (DOCTORS_TBL.DOC_NAME LIKE '%' + @DOC_NAME + '%') AND (CONVERT(VARCHAR(100), VISIT_TBL.VIS_ID) LIKE '%' + @VIS_ID + '%') AND &#13;&#10;                         (CUST_TBL.CUST_F_NAME +  CUST_TBL.CUST_S_NAME +  CUST_TBL.CUST_T_NAME +  CUST_TBL.CUST_L_NAME LIKE '%' + REPLACE(@CUST_NAME, &#13;&#10;                         ' ', '%') + '%')">
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="VIS_CODE" DataType="System.Decimal"/>
        <Column Name="VIS_NAME" DataType="System.String"/>
        <Column Name="VIS_DATE" DataType="System.String"/>
        <Column Name="VIS_TIME" DataType="System.String"/>
        <Column Name="VIS_TYPE" DataType="System.String"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="DOC_ID" DataType="System.Decimal"/>
        <Column Name="VIS_PRICE" DataType="System.Decimal"/>
        <Column Name="VIS_DISCOUNT" DataType="System.Decimal"/>
        <Column Name="VIS_TOTAL" DataType="System.Decimal"/>
        <Column Name="VIS_NOTE" DataType="System.String"/>
        <Column Name="VIS_PAY_TYPE" DataType="System.String"/>
        <Column Name="VIS_UNPAY" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="CUST_CODE" DataType="System.Decimal"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_SAVE_STATE" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <Column Name="DOC_NAME" DataType="System.String"/>
        <Column Name="DOC_MAJOR" DataType="System.String"/>
        <Column Name="DOC_BD" DataType="System.DateTime"/>
        <Column Name="DOC_EXP" DataType="System.String"/>
        <Column Name="DOC_MOBILE" DataType="System.String"/>
        <Column Name="DOC_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_ID" DataType="System.Decimal"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <CommandParameter Name="F_DATE" DataType="22" Size="200" Expression="[F_DATE]"/>
        <CommandParameter Name="S_DATE" DataType="22" Size="200" Expression="[S_DATE]"/>
        <CommandParameter Name="CLI_NAME" DataType="22" Size="200" Expression="[CLI_NAME]"/>
        <CommandParameter Name="CUST_NAME" DataType="22" Size="200" Expression="[CUST_NAME]"/>
        <CommandParameter Name="DOC_NAME" DataType="22" Size="200" Expression="[DOC_NAME]"/>
        <CommandParameter Name="VIS_ID" DataType="22" Size="200" Expression="[VIS_ID]"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="F_DATE" DataType="System.String"/>
    <Parameter Name="S_DATE" DataType="System.String"/>
    <Parameter Name="CLI_NAME" DataType="System.String"/>
    <Parameter Name="CUST_NAME" DataType="System.String"/>
    <Parameter Name="DOC_NAME" DataType="System.String"/>
    <Parameter Name="VIS_ID" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
    <Total Name="COUNT_VIS" TotalType="Count" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_PRICE" Expression="[repVISLIST_REPORT.VIS_PRICE]" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_DISCOUNT" Expression="[repVISLIST_REPORT.VIS_DISCOUNT]" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_UNPAY" Expression="[repVISLIST_REPORT.VIS_UNPAY]" Evaluator="Data1" PrintOn="ReportSummary1"/>
    <Total Name="SUM_TOTAL" Expression="[repVISLIST_REPORT.VIS_TOTAL]" Evaluator="Data1" PrintOn="ReportSummary1"/>
  </Dictionary>
  <ReportPage Name="Page1" Landscape="true" PaperWidth="297" PaperHeight="210" RawPaperSize="9" LeftMargin="0" TopMargin="0" RightMargin="0" FirstPageSource="15" OtherPagesSource="15" StartPageEvent="Page1_StartPage">
    <ReportTitleBand Name="ReportTitle1" Width="1122.66" Height="170.1">
      <TextObject Name="Text35" Left="391.23" Top="141.75" Width="340.2" Height="28.35" Text="تقرير الكشوفات الطبية" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Underline"/>
      <TextObject Name="Text36" Left="217.35" Top="9.45" Width="670.95" Height="47.25" Text="[repVISLIST_REPORT.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt"/>
      <TextObject Name="Text37" Left="217.35" Top="56.7" Width="670.95" Height="47.25" Text="[repVISLIST_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <PictureObject Name="Picture1" Left="18.9" Top="9.45" Width="160.65" Height="141.75" SizeMode="StretchImage"/>
      <PictureObject Name="Picture2" Left="926.1" Top="9.45" Width="170.1" Height="141.75" SizeMode="StretchImage"/>
    </ReportTitleBand>
    <PageHeaderBand Name="PageHeader1" Top="173.43" Width="1122.66" Height="85.05">
      <TextObject Name="Text3" Left="435.64" Top="47.25" Width="236.25" Height="18.9" Border.Lines="All" Text="[CLI_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text4" Left="435.64" Top="18.9" Width="236.25" Height="18.9" Border.Lines="All" Text="[CUST_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text5" Left="76.54" Top="18.9" Width="245.7" Height="18.9" Border.Lines="All" Text="[DOC_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text6" Left="76.54" Top="47.25" Width="245.7" Height="18.9" Border.Lines="All" Text="[VIS_ID]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text1" Left="813.64" Top="18.9" Width="160.65" Height="18.9" Border.Lines="All" Text="[F_DATE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text2" Left="813.64" Top="47.25" Width="160.65" Height="18.9" Border.Lines="All" Text="[S_DATE]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text45" Left="974.29" Top="18.9" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": من تاريخ" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text46" Left="974.29" Top="47.25" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": الى تاريخ" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text47" Left="671.89" Top="18.9" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم المريض" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text48" Left="671.89" Top="47.25" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم العيادة" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text49" Left="322.24" Top="18.9" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم الطبيب" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text50" Left="322.24" Top="47.25" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": رقم الزيارة" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line1" Left="27.41" Top="9.45" Width="1086.75" Border.Width="2"/>
      <LineObject Name="Line2" Left="28.35" Top="75.6" Width="1086.75" Border.Width="2"/>
    </PageHeaderBand>
    <DataBand Name="Data1" Top="284.05" Width="1122.66" Height="18.9" DataSource="Table">
      <TextObject Name="Text7" Left="1039.5" Width="66.15" Height="18.9" Border.Lines="All" Text="[repVISLIST_REPORT.VIS_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text9" Left="945" Width="94.5" Height="18.9" Border.Lines="All" Text="[repVISLIST_REPORT.VIS_DATE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text11" Left="859.95" Width="85.05" Height="18.9" Border.Lines="All" Text="[repVISLIST_REPORT.VIS_TIME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text13" Left="670.95" Width="189" Height="18.9" Border.Lines="All" Text="[repVISLIST_REPORT.CUST_ID] / [repVISLIST_REPORT.CUST_NAME]" VertAlign="Center" RightToLeft="true" Font="Arial, 11pt"/>
      <TextObject Name="Text15" Left="519.75" Width="151.2" Height="18.9" Border.Lines="All" Text="[repVISLIST_REPORT.CLI_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text17" Left="340.2" Width="179.55" Height="18.9" Border.Lines="All" Text="[repVISLIST_REPORT.DOC_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text19" Left="264.6" Width="75.6" Height="18.9" Border.Lines="All" Text="[repVISLIST_REPORT.VIS_PRICE]" Format="Currency" Format.UseLocale="true" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text21" Left="179.55" Width="85.05" Height="18.9" Border.Lines="All" Text="[repVISLIST_REPORT.VIS_DISCOUNT]" Format="Currency" Format.UseLocale="true" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text23" Left="103.95" Width="75.6" Height="18.9" Border.Lines="All" Text="[repVISLIST_REPORT.VIS_UNPAY]" Format="Currency" Format.UseLocale="true" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text25" Left="18.9" Width="85.05" Height="18.9" Border.Lines="All" Text="[repVISLIST_REPORT.VIS_TOTAL]" Format="Currency" Format.UseLocale="true" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
      <DataHeaderBand Name="DataHeader1" Top="261.82" Width="1122.66" Height="18.9">
        <TextObject Name="Text8" Left="1039.5" Width="66.15" Height="18.9" Border.Lines="All" Border.Width="1.5" Fill.Color="LightGray" Text="رقم الزيارة" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text10" Left="945" Width="94.5" Height="18.9" Border.Lines="All" Border.Width="1.5" Fill.Color="LightGray" Text="تاريخ الزيارة" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text12" Left="859.95" Width="85.05" Height="18.9" Border.Lines="All" Border.Width="1.5" Fill.Color="LightGray" Text="وقت الزيارة" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text14" Left="670.95" Width="189" Height="18.9" Border.Lines="All" Border.Width="1.5" Fill.Color="LightGray" Text="رقم / اسم المريض" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text16" Left="519.75" Width="151.2" Height="18.9" Border.Lines="All" Border.Width="1.5" Fill.Color="LightGray" Text="اسم العيادة" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text18" Left="340.2" Width="179.55" Height="18.9" Border.Lines="All" Border.Width="1.5" Fill.Color="LightGray" Text="اسم الطبيب" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text20" Left="264.6" Width="75.6" Height="18.9" Border.Lines="All" Border.Width="1.5" Fill.Color="LightGray" Text="السعر" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text22" Left="179.55" Width="85.05" Height="18.9" Border.Lines="All" Border.Width="1.5" Fill.Color="LightGray" Text="الخصم" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text24" Left="103.95" Width="75.6" Height="18.9" Border.Lines="All" Border.Width="1.5" Fill.Color="LightGray" Text="الباقي" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text26" Left="18.9" Width="85.05" Height="18.9" Border.Lines="All" Border.Width="1.5" Fill.Color="LightGray" Text="المجموع" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <ReportSummaryBand Name="ReportSummary1" Top="306.28" Width="1122.66" Height="132.3" KeepWithData="true">
      <LineObject Name="Line3" Left="28.35" Top="9.45" Width="1077.3" Border.Width="2"/>
      <TextObject Name="Text27" Left="793.8" Top="18.9" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Text="[COUNT_VIS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text28" Left="793.8" Top="37.8" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Text="[SUM_PRICE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text29" Left="793.8" Top="56.7" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Text="[SUM_DISCOUNT]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text30" Left="793.8" Top="75.6" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Text="[SUM_UNPAY]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text31" Left="793.8" Top="94.5" Width="189" Height="18.9" Border.Lines="All" Border.Width="2" Text="[SUM_TOTAL]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text51" Left="982.8" Top="18.9" Width="103.95" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": عدد الكشوفات" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text52" Left="982.8" Top="37.8" Width="103.95" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": مجموع السعر" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text53" Left="982.8" Top="56.7" Width="103.95" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": مجموع الخصم" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text54" Left="982.8" Top="75.6" Width="103.95" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": مجموع الباقي" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text55" Left="982.8" Top="94.5" Width="103.95" Height="18.9" Border.Lines="All" Border.Width="2" Fill.Color="Gainsboro" Text=": مجموع الاجمالي" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line4" Left="28.35" Top="122.85" Width="1077.3" Border.Width="2"/>
    </ReportSummaryBand>
    <PageFooterBand Name="PageFooter1" Top="441.92" Width="1122.66" Height="122.85">
      <TextObject Name="Text56" Left="452.66" Top="103.95" Width="160.65" Height="18.9" Text="[TotalPages#] / [Page#]" HorzAlign="Right" VertAlign="Center" Font="Arial, 10pt, style=Bold"/>
      <TextObject Name="Text57" Left="28.35" Top="28.35" Width="963.9" Height="37.8" Border.Lines="All" Fill.Color="255, 255, 192" Text="[repVISLIST_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="992.25" Top="28.35" Width="103.95" Height="37.8" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text58" Left="28.35" Top="66.15" Width="963.9" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[repVISLIST_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="992.25" Top="66.15" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
