﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" ReportInfo.Created="10/02/2022 14:05:11" ReportInfo.Modified="10/08/2022 23:14:57" ReportInfo.CreatorVersion="2018.4.7.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    public string IMG_LOC (string IMG_LOC)
    {
      Picture1.ImageLocation = IMG_LOC;
      Picture2.ImageLocation = IMG_LOC;
      return IMG_LOC;
    }

    private void Page1_StartPage(object sender, EventArgs e)
    {
      IMG_LOC(((String)Report.GetParameterValue(&quot;IMG_LOC&quot;)));
      if(((String)Report.GetColumnValue(&quot;VIS_SER_PRICE_REPORT.COM_NAME&quot;)) == &quot;&quot;)
      {
        Text23.Text = &quot;لا يوجد تأمين&quot;;
        Text51.Visible = false;
        Text52.Visible = false;
        Text11.Visible = false;
        Text18.Visible = false;
        Text57.Visible = false;
        Text24.Visible = false;
      }
      else
      {
        Text51.Visible = true;
        Text52.Visible = true;
        Text11.Visible = true;
        Text18.Visible = true;
        Text57.Visible = true;
        Text24.Visible = true;
      }
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqM7gJFg/iaLrqMhRfGy5lGnWsoLqEeWCFa1CsF4g0Z231JIQJjZYjR3MhVqIop9naIu+NtozHnIqlbBbgp7Cd6lIFj2sam0qqIhgKHnq/RtZgcon9JhG2F2PioAJrvJ5N9FPftSO6TEVjIqVTznI9PFVPLaINarwFty2Z6dsN8g6eAyupR4YXmWg1atH4rPIx9P64IidgbmnJARwfyVtSw8T5aGPITBnmthUfiBJfEHEzMJCKJLCdajrwhdvfpezC">
      <TableDataSource Name="Table" Alias="VIS_SER_PRICE_REPORT" DataType="System.Int32" Enabled="true" SelectCommand="SELECT        VISIT_TBL.VIS_ID, VISIT_TBL.VIS_CODE, VISIT_TBL.VIS_NAME, CONVERT(varchar, VISIT_TBL.VIS_DATE, 103) AS VIS_DATE, CONVERT(varchar(5), VISIT_TBL.VIS_TIME, 108) AS VIS_TIME, VISIT_TBL.VIS_TYPE, &#13;&#10;                         VISIT_TBL.CUST_ID, VISIT_TBL.CLI_ID, VISIT_TBL.DOC_ID, VISIT_TBL.VIS_PRICE, VISIT_TBL.VIS_DISCOUNT, VISIT_TBL.VIS_TOTAL, VISIT_TBL.VIS_NOTE, VISIT_TBL.VIS_PAY_TYPE, VISIT_TBL.VIS_UNPAY, &#13;&#10;                         CLINC_TBL.CLI_NAME, CLINC_TBL.CLI_LOC, TRANSACTION_TBL.T_ID, TRANSACTION_TBL.T_CODE, TRANSACTION_TBL.T_NAME, TRANSACTION_TBL.T_DATE, TRANSACTION_TBL.T_TIME, TRANSACTION_TBL.T_TYPE, &#13;&#10;                         TRANSACTION_TBL.T_PRICE, TRANSACTION_TBL.T_DISCOUNT, TRANSACTION_TBL.T_UNPAY, TRANSACTION_TBL.T_TOTAL, TRANSACTION_TBL.CUST_PRICE, TRANSACTION_TBL.COMPANY_PRICE, &#13;&#10;                         TRANSACTION_TBL.T_NOTE, TRANSACTION_TBL.T_STATE, COMPANY_TBL.COM_ID, COMPANY_TBL.COM_CODE, COMPANY_TBL.COM_NAME, COMPANY_TBL.COM_ADDRESS, COMPANY_TBL.COM_MOBILE, &#13;&#10;                         CARD_TBL.CARD_ID, CARD_TBL.CARD_CODE, CARD_TBL.CARD_NAME, CARD_TBL.CARD_DATE, CARD_TBL.CARD_STATE, CARD_TBL.CARD_PER, CARD_TBL.CARD_NOTE, DOCTORS_TBL.DOC_CODE, &#13;&#10;                         DOCTORS_TBL.DOC_NAME, DOCTORS_TBL.DOC_MAJOR, DOCTORS_TBL.DOC_MOBILE, DOCTORS_TBL.DOC_ADDRESS, CUST_TBL.CUST_CODE, CUST_TBL.CUST_AGE, CUST_TBL.CUST_BD, CUST_TBL.CUST_MOBILE1, &#13;&#10;                         CUST_TBL.CUST_MOBILE2, CUST_TBL.CUST_ADDRESS, CUST_TBL.CUST_SAVE_STATE, CUST_TBL.CUST_GENDER, CUST_TBL.CUST_NATION, CUST_TBL.CUST_AGE_MONTH, SERVICE_TBL.SER_CODE, &#13;&#10;                         SERVICE_TBL.SER_NAME, SERVICE_TBL.SER_TYPE, SERVICE_TBL.SER_PRICE, SERVICE_TBL.SER_NOTE, SERLIST_TBL.SERLIST_ID, SERLIST_TBL.SERLIST_CODE, SERLIST_TBL.SERLIST_NAME, &#13;&#10;                         SERLIST_TBL.SERLIST_DATE, SERLIST_TBL.SERLIST_TIME, SERLIST_TBL.SER_PRICE_TOTAL, SERLIST_TBL.SERLIST_NOTE, CLINIC_TITLE_TBL.CLI_T_ID, CLINIC_TITLE_TBL.CLI_T_NAME, CLINIC_TITLE_TBL.CLI_T_TITLE, &#13;&#10;                         CLINIC_TITLE_TBL.CLI_T_ADDRESS, CLINIC_TITLE_TBL.CLI_T_MOBILE, CLINIC_TITLE_TBL.CLI_T_NOTE, &#13;&#10;                         CUST_TBL.CUST_F_NAME + ' ' + CUST_TBL.CUST_S_NAME + ' ' + CUST_TBL.CUST_T_NAME + ' ' + CUST_TBL.CUST_L_NAME AS CUST_NAME&#13;&#10;FROM            VISIT_TBL INNER JOIN&#13;&#10;                         CLINC_TBL ON VISIT_TBL.CLI_ID = CLINC_TBL.CLI_ID INNER JOIN&#13;&#10;                         TRANSACTION_TBL ON VISIT_TBL.VIS_ID = TRANSACTION_TBL.VIS_ID AND CLINC_TBL.CLI_ID = TRANSACTION_TBL.CLI_ID INNER JOIN&#13;&#10;                         CUST_TBL ON VISIT_TBL.CUST_ID = CUST_TBL.CUST_ID LEFT OUTER JOIN&#13;&#10;                         COMPANY_TBL ON TRANSACTION_TBL.COM_ID = COMPANY_TBL.COM_ID LEFT OUTER JOIN&#13;&#10;                         CARD_TBL ON COMPANY_TBL.COM_ID = CARD_TBL.COM_ID AND CUST_TBL.CARD_ID = CARD_TBL.CARD_ID INNER JOIN&#13;&#10;                         DOCTORS_TBL ON VISIT_TBL.DOC_ID = DOCTORS_TBL.DOC_ID AND CLINC_TBL.CLI_ID = DOCTORS_TBL.CLI_ID LEFT OUTER JOIN&#13;&#10;                         SERLIST_TBL ON VISIT_TBL.VIS_ID = SERLIST_TBL.VIS_ID AND CLINC_TBL.CLI_ID = SERLIST_TBL.CLI_ID AND CUST_TBL.CUST_ID = SERLIST_TBL.CUST_ID INNER JOIN&#13;&#10;                         SERVICE_TBL ON SERLIST_TBL.SER_ID = SERVICE_TBL.SER_ID AND CLINC_TBL.CLI_ID = SERVICE_TBL.CLI_ID INNER JOIN&#13;&#10;                         CLINIC_TITLE_TBL ON CLINC_TBL.CLI_ID = CLINIC_TITLE_TBL.CLI_ID&#13;&#10;WHERE        (VISIT_TBL.VIS_ID = @VIS_ID) AND (VISIT_TBL.CUST_ID = @CUST_ID) AND (VISIT_TBL.CLI_ID = @CLI_ID)">
        <Column Name="VIS_ID" DataType="System.Decimal"/>
        <Column Name="VIS_CODE" DataType="System.Decimal"/>
        <Column Name="VIS_NAME" DataType="System.String"/>
        <Column Name="VIS_DATE" DataType="System.String"/>
        <Column Name="VIS_TIME" DataType="System.String"/>
        <Column Name="VIS_TYPE" DataType="System.String"/>
        <Column Name="CUST_ID" DataType="System.Decimal"/>
        <Column Name="CLI_ID" DataType="System.Decimal"/>
        <Column Name="DOC_ID" DataType="System.Decimal"/>
        <Column Name="VIS_PRICE" DataType="System.Decimal"/>
        <Column Name="VIS_DISCOUNT" DataType="System.Decimal"/>
        <Column Name="VIS_TOTAL" DataType="System.Decimal"/>
        <Column Name="VIS_NOTE" DataType="System.String"/>
        <Column Name="VIS_PAY_TYPE" DataType="System.String"/>
        <Column Name="VIS_UNPAY" DataType="System.Decimal"/>
        <Column Name="CLI_NAME" DataType="System.String"/>
        <Column Name="CLI_LOC" DataType="System.String"/>
        <Column Name="T_ID" DataType="System.Decimal"/>
        <Column Name="T_CODE" DataType="System.Decimal"/>
        <Column Name="T_NAME" DataType="System.String"/>
        <Column Name="T_DATE" DataType="System.DateTime"/>
        <Column Name="T_TIME" DataType="System.TimeSpan"/>
        <Column Name="T_TYPE" DataType="System.String"/>
        <Column Name="T_PRICE" DataType="System.Decimal"/>
        <Column Name="T_DISCOUNT" DataType="System.Decimal"/>
        <Column Name="T_UNPAY" DataType="System.Decimal"/>
        <Column Name="T_TOTAL" DataType="System.Decimal"/>
        <Column Name="CUST_PRICE" DataType="System.Decimal"/>
        <Column Name="COMPANY_PRICE" DataType="System.Decimal"/>
        <Column Name="T_NOTE" DataType="System.String"/>
        <Column Name="T_STATE" DataType="System.String"/>
        <Column Name="COM_ID" DataType="System.Decimal"/>
        <Column Name="COM_CODE" DataType="System.Decimal"/>
        <Column Name="COM_NAME" DataType="System.String"/>
        <Column Name="COM_ADDRESS" DataType="System.String"/>
        <Column Name="COM_MOBILE" DataType="System.String"/>
        <Column Name="CARD_ID" DataType="System.Decimal"/>
        <Column Name="CARD_CODE" DataType="System.Decimal"/>
        <Column Name="CARD_NAME" DataType="System.String"/>
        <Column Name="CARD_DATE" DataType="System.DateTime"/>
        <Column Name="CARD_STATE" DataType="System.String"/>
        <Column Name="CARD_PER" DataType="System.Int32"/>
        <Column Name="CARD_NOTE" DataType="System.String"/>
        <Column Name="DOC_CODE" DataType="System.Decimal"/>
        <Column Name="DOC_NAME" DataType="System.String"/>
        <Column Name="DOC_MAJOR" DataType="System.String"/>
        <Column Name="DOC_MOBILE" DataType="System.String"/>
        <Column Name="DOC_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_CODE" DataType="System.Decimal"/>
        <Column Name="CUST_AGE" DataType="System.String"/>
        <Column Name="CUST_BD" DataType="System.DateTime"/>
        <Column Name="CUST_MOBILE1" DataType="System.String"/>
        <Column Name="CUST_MOBILE2" DataType="System.String"/>
        <Column Name="CUST_ADDRESS" DataType="System.String"/>
        <Column Name="CUST_SAVE_STATE" DataType="System.String"/>
        <Column Name="CUST_GENDER" DataType="System.String"/>
        <Column Name="CUST_NATION" DataType="System.String"/>
        <Column Name="CUST_AGE_MONTH" DataType="System.String"/>
        <Column Name="SER_CODE" DataType="System.Decimal"/>
        <Column Name="SER_NAME" DataType="System.String"/>
        <Column Name="SER_TYPE" DataType="System.String"/>
        <Column Name="SER_PRICE" DataType="System.Decimal"/>
        <Column Name="SER_NOTE" DataType="System.String"/>
        <Column Name="SERLIST_ID" DataType="System.Decimal"/>
        <Column Name="SERLIST_CODE" DataType="System.Decimal"/>
        <Column Name="SERLIST_NAME" DataType="System.String"/>
        <Column Name="SERLIST_DATE" DataType="System.DateTime"/>
        <Column Name="SERLIST_TIME" DataType="System.TimeSpan"/>
        <Column Name="SER_PRICE_TOTAL" DataType="System.Decimal"/>
        <Column Name="SERLIST_NOTE" DataType="System.String"/>
        <Column Name="CLI_T_ID" DataType="System.Decimal"/>
        <Column Name="CLI_T_NAME" DataType="System.String"/>
        <Column Name="CLI_T_TITLE" DataType="System.String"/>
        <Column Name="CLI_T_ADDRESS" DataType="System.String"/>
        <Column Name="CLI_T_MOBILE" DataType="System.String"/>
        <Column Name="CLI_T_NOTE" DataType="System.String"/>
        <Column Name="CUST_NAME" DataType="System.String"/>
        <CommandParameter Name="VIS_ID" DataType="22" Size="200" Expression="[VIS_ID]" DefaultValue="0"/>
        <CommandParameter Name="CUST_ID" DataType="22" Size="200" Expression="[CUST_ID]" DefaultValue="0"/>
        <CommandParameter Name="CLI_ID" DataType="22" Size="200" Expression="[CLI_ID]" DefaultValue="0"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="VIS_ID" DataType="System.String"/>
    <Parameter Name="CUST_ID" DataType="System.String"/>
    <Parameter Name="CLI_ID" DataType="System.String"/>
    <Parameter Name="IMG_LOC" DataType="System.String"/>
  </Dictionary>
  <ReportPage Name="Page1" PaperHeight="210" LeftMargin="0" TopMargin="0" RightMargin="0" StartPageEvent="Page1_StartPage">
    <PageHeaderBand Name="PageHeader1" Width="793.8" Height="264.6">
      <TextObject Name="Text36" Left="103.95" Top="9.45" Width="576.45" Height="66.15" Text="[VIS_SER_PRICE_REPORT.CLI_T_NAME]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 18pt"/>
      <TextObject Name="Text37" Left="103.95" Top="75.6" Width="576.45" Height="47.25" Text="[VIS_SER_PRICE_REPORT.CLI_T_TITLE]" HorzAlign="Center" VertAlign="Center" Font="Times New Roman, 16pt, style=Italic"/>
      <PictureObject Name="Picture1" Top="9.45" Width="103.95" Height="132.3" SizeMode="StretchImage"/>
      <PictureObject Name="Picture2" Left="680.4" Top="9.45" Width="103.95" Height="122.85" SizeMode="StretchImage"/>
      <TextObject Name="Text35" Left="274.05" Top="132.3" Width="245.7" Height="28.35" Text="كشف طبي" HorzAlign="Center" VertAlign="Center" Font="Arial Black, 14pt, style=Bold, Underline"/>
      <LineObject Name="Line1" Top="160.65" Width="793.8"/>
      <TextObject Name="Text12" Left="633.15" Top="170.1" Width="56.7" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[VIS_SER_PRICE_REPORT.CUST_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text13" Left="415.8" Top="170.1" Width="217.35" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[VIS_SER_PRICE_REPORT.CUST_NAME]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text15" Left="283.5" Top="170.1" Width="66.15" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[VIS_SER_PRICE_REPORT.CUST_GENDER]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text16" Left="160.65" Top="170.1" Width="75.6" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[VIS_SER_PRICE_REPORT.CUST_AGE_MONTH]/ [VIS_SER_PRICE_REPORT.CUST_AGE]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text17" Left="689.85" Top="170.1" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم المريض" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text41" Left="349.65" Top="170.1" Width="56.7" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": الجنس" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text42" Left="236.25" Top="170.1" Width="47.25" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": العمر" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text14" Left="415.8" Top="198.45" Width="274.05" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[VIS_SER_PRICE_REPORT.CLI_NAME]" HorzAlign="Right" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text40" Left="689.85" Top="198.45" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": اسم العيادة" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text21" Left="595.35" Top="226.8" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[VIS_SER_PRICE_REPORT.VIS_ID]" Format="Number" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text45" Left="689.85" Top="226.8" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": رقم الزيارة" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text19" Left="198.45" Top="198.45" Width="122.85" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[VIS_SER_PRICE_REPORT.VIS_DATE]" Format="Date" Format.Format="d" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text20" Left="18.9" Top="198.45" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[VIS_SER_PRICE_REPORT.VIS_TIME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text44" Left="113.4" Top="198.45" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": وقت الزيارة" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text22" Left="415.8" Top="226.8" Width="94.5" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[VIS_SER_PRICE_REPORT.VIS_PAY_TYPE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text46" Left="510.3" Top="226.8" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": طريقة الدفع" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text43" Left="321.3" Top="198.45" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": تاريخ الزيارة" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line2" Top="255.15" Width="793.8"/>
    </PageHeaderBand>
    <DataBand Name="Data1" Top="290.17" Width="793.8" Height="18.9" DataSource="Table" KeepTogether="true" KeepDetail="true">
      <TextObject Name="Text1" Left="415.8" Width="340.2" Height="18.9" Border.Lines="All" Text="[VIS_SER_PRICE_REPORT.SER_NAME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text3" Left="160.65" Width="255.15" Height="18.9" Border.Lines="All" Text="[VIS_SER_PRICE_REPORT.SER_TYPE]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt"/>
      <TextObject Name="Text5" Left="47.25" Width="113.4" Height="18.9" Border.Lines="All" Text="[VIS_SER_PRICE_REPORT.SER_PRICE]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="3" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="د.ع.‏" Format.PositivePattern="1" Format.NegativePattern="0" HorzAlign="Center" VertAlign="Center" WordWrap="false" Font="Arial, 11pt" Trimming="EllipsisCharacter"/>
      <DataHeaderBand Name="DataHeader1" Top="267.93" Width="793.8" Height="18.9" KeepWithData="true">
        <TextObject Name="Text2" Left="415.8" Width="340.2" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="اسم الاجراء" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text4" Left="160.65" Width="255.15" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="نوع الاجرء" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
        <TextObject Name="Text6" Left="47.25" Width="113.4" Height="18.9" Border.Lines="All" Fill.Color="LightGray" Text="سعر الاجراء" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      </DataHeaderBand>
    </DataBand>
    <PageFooterBand Name="PageFooter1" Top="312.4" Width="793.8" Height="245.7" CanBreak="true">
      <TextObject Name="Text53" Left="9.45" Top="170.1" Width="661.5" Height="37.8" Border.Lines="All" Fill.Color="255, 255, 192" Text="[VIS_SER_PRICE_REPORT.CLI_T_ADDRESS]" HorzAlign="Right" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text38" Left="670.95" Top="170.1" Width="103.95" Height="37.8" Border.Lines="All" Fill.Color="224, 224, 224" Text=": عنوان العيادة" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text54" Left="9.45" Top="207.9" Width="661.5" Height="28.35" Border.Lines="All" Fill.Color="255, 255, 192" Text="[VIS_SER_PRICE_REPORT.CLI_T_MOBILE]" HorzAlign="Right" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text39" Left="670.95" Top="207.9" Width="103.95" Height="28.35" Border.Lines="All" Fill.Color="224, 224, 224" Text=": رقم الحجز" VertAlign="Center" Font="Arial, 12pt, style=Bold"/>
      <TextObject Name="Text25" Left="4.73" Top="132.3" Width="283.5" Height="28.35" Fill.Color="White" Text="[VIS_SER_PRICE_REPORT.DOC_NAME]" HorzAlign="Center" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text55" Left="4.73" Top="103.95" Width="283.5" Height="18.9" Fill.Color="White" Text="أسم و توقيع الطبيب" HorzAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <LineObject Name="Line3" Width="793.8"/>
      <TextObject Name="Text9" Left="463.05" Top="66.15" Width="198.45" Height="18.9" Border.Lines="All" Text="[VIS_SER_PRICE_REPORT.T_UNPAY]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text18" Left="37.8" Top="66.15" Width="236.25" Height="18.9" Border.Lines="All" Text="[VIS_SER_PRICE_REPORT.COMPANY_PRICE]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text49" Left="661.5" Top="66.15" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": الباقي" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text52" Left="274.05" Top="66.15" Width="151.2" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": المطلوب من شركة التأمين" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text10" Left="463.05" Top="47.25" Width="198.45" Height="18.9" Border.Lines="All" Text="[VIS_SER_PRICE_REPORT.T_TOTAL]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text11" Left="37.8" Top="47.25" Width="236.25" Height="18.9" Border.Lines="All" Text="[VIS_SER_PRICE_REPORT.CUST_PRICE]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text50" Left="661.5" Top="47.25" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": المجموع" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text51" Left="274.05" Top="47.25" Width="151.2" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": المطلوب من المريض" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text24" Left="37.8" Top="28.35" Width="236.25" Height="18.9" Border.Lines="All" Fill.Color="White" Text="% [VIS_SER_PRICE_REPORT.CARD_PER]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text8" Left="463.05" Top="28.35" Width="198.45" Height="18.9" Border.Lines="All" Text="[VIS_SER_PRICE_REPORT.T_DISCOUNT]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text48" Left="661.5" Top="28.35" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": الخصم" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text57" Left="274.05" Top="28.35" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": نسبة التأمين" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text23" Left="37.8" Top="9.45" Width="236.25" Height="18.9" Border.Lines="All" Fill.Color="White" Text="[VIS_SER_PRICE_REPORT.COM_NAME]" HorzAlign="Right" VertAlign="Center" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text7" Left="463.05" Top="9.45" Width="198.45" Height="18.9" Border.Lines="All" Text="[VIS_SER_PRICE_REPORT.T_PRICE]" Format="Currency" Format.UseLocale="false" Format.DecimalDigits="0" Format.DecimalSeparator="." Format.GroupSeparator="" Format.CurrencySymbol="" Format.PositivePattern="0" Format.NegativePattern="0" HorzAlign="Right" WordWrap="false" Font="Arial, 11pt, style=Bold" Trimming="EllipsisCharacter"/>
      <TextObject Name="Text47" Left="661.5" Top="9.45" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": القيمة" Font="Arial, 11pt, style=Bold"/>
      <TextObject Name="Text56" Left="274.05" Top="9.45" Width="85.05" Height="18.9" Border.Lines="All" Fill.Color="255, 255, 192" Text=": شركة التأمين" Font="Arial, 11pt, style=Bold"/>
    </PageFooterBand>
  </ReportPage>
</Report>
