﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="SMART_CLINIC_SOFTWARE_2020.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <connectionStrings>
    <add name="SMART_CLINIC_SOFTWARE_2020.Properties.Settings.ClinicDataBase_2020ConnectionString"
      connectionString="Data Source=DESKTOP-TB02KDT;Initial Catalog=ClinicDataBase_2020;User ID=CLINIC_USER_2020;Password=******************"
      providerName="System.Data.SqlClient" />
    <add name="SMART_CLINIC_SOFTWARE_2020.Properties.Settings.ClinicDataBase_2020ConnectionString1"
      connectionString="Data Source=DESKTOP-TB02KDT;Initial Catalog=ClinicDataBase_2020;User ID=CLINIC_USER_2020;Password=******************"
      providerName="System.Data.SqlClient" />
  </connectionStrings>
  <applicationSettings>
    <DevExpress.LookAndFeel.Design.AppSettings>
      <setting name="DPIAwarenessMode" serializeAs="String">
        <value>System</value>
      </setting>
      <setting name="RegisterBonusSkins" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="DefaultAppSkin" serializeAs="String">
        <value>Skin/DevExpress Style</value>
      </setting>
      <setting name="DefaultPalette" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="CompactUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchScaleFactor" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DirectX" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterUserSkins" serializeAs="String">
        <value></value>
      </setting>
      <setting name="FontBehavior" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DefaultAppFont" serializeAs="String">
        <value></value>
      </setting>
      <setting name="CustomPaletteCollection" serializeAs="Xml">
        <value />
      </setting>
    </DevExpress.LookAndFeel.Design.AppSettings>
  </applicationSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
  <userSettings>
    <SMART_CLINIC_SOFTWARE_2020.Properties.Settings>
      <setting name="SERVER_NAME" serializeAs="String">
        <value />
      </setting>
      <setting name="DATABASE_NAME" serializeAs="String">
        <value />
      </setting>
      <setting name="DB_USER_NAME" serializeAs="String">
        <value />
      </setting>
      <setting name="DB_PASSWORD" serializeAs="String">
        <value />
      </setting>
      <setting name="CON_STRING" serializeAs="String">
        <value />
      </setting>
      <setting name="L_S_DATE" serializeAs="String">
        <value>2010-01-01</value>
      </setting>
      <setting name="L_E_DATE" serializeAs="String">
        <value>2010-01-01</value>
      </setting>
      <setting name="L_KEY" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="Active_Date" serializeAs="String">
        <value>2010-01-01</value>
      </setting>
      <setting name="DEFAULT_DATABASE" serializeAs="String">
        <value>Data Source=DESKTOP-TB02KDT;Initial Catalog=ClinicDataBase_2020;User ID=CLINIC_USER_2020;Password=******************</value>
      </setting>
      <setting name="L_CLI_ID" serializeAs="String">
        <value />
      </setting>
      <setting name="L_CLI_NAME" serializeAs="String">
        <value />
      </setting>
      <setting name="SYS_PRINTER_NAME" serializeAs="String">
        <value />
      </setting>
    </SMART_CLINIC_SOFTWARE_2020.Properties.Settings>
  </userSettings>
</configuration>