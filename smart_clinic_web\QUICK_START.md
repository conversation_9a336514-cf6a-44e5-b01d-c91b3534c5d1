# 🚀 دليل التشغيل السريع
## Quick Start Guide

دليل سريع لتشغيل نظام إدارة العيادة الذكية في أقل من 5 دقائق!

---

## ⚡ التشغيل السريع (5 دقائق)

### 1️⃣ تحميل المتطلبات
```bash
# تأكد من وجود Python 3.8+
python --version

# إنشاء مجلد المشروع
mkdir smart_clinic_web
cd smart_clinic_web

# نسخ جميع ملفات المشروع إلى المجلد
```

### 2️⃣ إعداد البيئة
```bash
# إنشاء البيئة الافتراضية
python -m venv clinic_env

# تفعيل البيئة
# Windows:
clinic_env\Scripts\activate
# Linux/Mac:
source clinic_env/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 3️⃣ تشغيل التطبيق
```bash
# الطريقة الأولى: تشغيل مع إعداد تلقائي
python run.py

# الطريقة الثانية: إعداد يدوي
python init_db.py
python app.py
```

### 4️⃣ الوصول للتطبيق
- افتح المتصفح
- انتقل إلى: `http://localhost:5000`
- استخدم: `admin` / `admin123`

---

## 🎯 أوامر سريعة

### تشغيل التطبيق
```bash
python run.py                    # تشغيل مع إعداد تلقائي
python app.py                    # تشغيل مباشر
flask run                        # تشغيل باستخدام Flask CLI
```

### إدارة قاعدة البيانات
```bash
python init_db.py               # إنشاء قاعدة بيانات جديدة
python -c "from app import db; db.create_all()"  # إنشاء الجداول فقط
```

### Docker (اختياري)
```bash
docker build -t smart-clinic .   # بناء الصورة
docker run -p 5000:5000 smart-clinic  # تشغيل الحاوية
docker-compose up                # تشغيل مع docker-compose
```

---

## 🔑 بيانات تسجيل الدخول

| المستخدم | كلمة المرور | الدور |
|----------|-------------|-------|
| `admin` | `admin123` | 👑 مدير النظام |
| `doctor1` | `doctor123` | 👨‍⚕️ طبيب |
| `nurse1` | `nurse123` | 👩‍⚕️ ممرضة |
| `reception1` | `reception123` | 👩‍💼 استقبال |

---

## 📊 البيانات التجريبية

### المرضى: 8 مرضى
- أحمد محمد علي
- فاطمة حسن محمد
- عمر عبدالله أحمد
- زينب علي حسين
- وآخرون...

### الأطباء: 5 أطباء
- د. محمد أحمد الطائي (طب عام)
- د. فاطمة علي الحسني (طب أطفال)
- د. عمر حسن الكربلائي (طب قلب)
- وآخرون...

### المواعيد: 15 موعد
- 8 مواعيد مكتملة
- 7 مواعيد مجدولة
- مواعيد لليوم الحالي

### الخزائن: 3 خزائن
- الخزنة الرئيسية: 52,750,000 د.ع
- خزنة الطوارئ: 15,000,000 د.ع
- خزنة المصروفات: 8,500,000 د.ع

---

## 🎨 المميزات الرئيسية

### ✅ لوحة المعلومات
- **إحصائيات فورية** للمرضى والمواعيد والخزنة
- **تحديث تلقائي** كل 30 ثانية
- **أيقونات كبيرة** بحجم 36F
- **تنسيق عربي** من اليمين إلى اليسار

### ✅ إدارة المرضى
- **عدد المرضى الكلي**: عرض إجمالي المراجعين
- **المرضى الجدد**: عدد المرضى المسجلين اليوم
- **تتبع فوري**: للإحصائيات

### ✅ نظام المواعيد
- **مواعيد اليوم**: العدد الكلي لمواعيد اليوم
- **الموعد القادم**: أقرب موعد لم يحن وقته
- **المواعيد المتبقية**: عدد المواعيد الباقية
- **المواعيد المكتملة**: التي انتهى وقتها

### ✅ إدارة الخزنة
- **المبلغ الكلي**: مجموع جميع الخزائن
- **تنسيق بالملايين**: عرض مبسط (52.75 مليون د.ع)
- **عدد الخزائن**: إجمالي الخزائن النشطة
- **الخزنة الرئيسية**: اسم الخزنة الأكبر

---

## 🔧 استكشاف الأخطاء

### مشكلة: Python غير موجود
```bash
# تثبيت Python من python.org
# أو استخدام مدير الحزم
# Windows: winget install Python.Python.3
# Mac: brew install python
# Ubuntu: sudo apt install python3
```

### مشكلة: pip غير موجود
```bash
# تثبيت pip
python -m ensurepip --upgrade
# أو
curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
python get-pip.py
```

### مشكلة: خطأ في المتطلبات
```bash
# تحديث pip
pip install --upgrade pip
# تثبيت المتطلبات واحداً تلو الآخر
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.0.5
# إلخ...
```

### مشكلة: المنفذ 5000 مستخدم
```bash
# تغيير المنفذ في app.py
app.run(debug=True, host='0.0.0.0', port=8000)
# أو إيقاف العملية المستخدمة للمنفذ
# Windows: netstat -ano | findstr :5000
# Linux/Mac: lsof -i :5000
```

### مشكلة: قاعدة البيانات تالفة
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm smart_clinic.db
python init_db.py
```

---

## 📱 الوصول من الأجهزة الأخرى

### الشبكة المحلية
```bash
# تشغيل التطبيق على جميع الواجهات
python app.py  # يعمل على 0.0.0.0:5000

# معرفة عنوان IP الخاص بك
# Windows: ipconfig
# Linux/Mac: ifconfig

# الوصول من جهاز آخر
# http://*************:5000
```

### الهاتف المحمول
- تأكد من اتصال الهاتف بنفس الشبكة
- استخدم عنوان IP الخاص بالكمبيوتر
- مثال: `http://*************:5000`

---

## 🚀 الخطوات التالية

### 1. استكشاف الواجهة
- جرب تسجيل الدخول بحسابات مختلفة
- اكتشف لوحة المعلومات التفاعلية
- لاحظ التحديث التلقائي كل 30 ثانية

### 2. تخصيص البيانات
- عدّل البيانات في `init_db.py`
- أضف مرضى وأطباء جدد
- غيّر أرصدة الخزائن

### 3. تطوير المميزات
- أضف صفحات جديدة
- طوّر نظام التقارير
- أضف ميزة البحث

### 4. النشر للإنتاج
- استخدم قاعدة بيانات PostgreSQL
- فعّل HTTPS
- استخدم خادم Gunicorn

---

## 💡 نصائح مفيدة

### الأداء
- استخدم قاعدة بيانات خارجية للبيانات الكبيرة
- فعّل التخزين المؤقت للاستعلامات
- استخدم CDN للملفات الثابتة

### الأمان
- غيّر المفتاح السري في الإنتاج
- استخدم HTTPS دائماً
- فعّل النسخ الاحتياطية

### التطوير
- استخدم بيئة افتراضية دائماً
- اتبع معايير PEP 8 للكود
- اكتب اختبارات للمميزات الجديدة

---

**🎉 مبروك! أصبح لديك نظام إدارة عيادة احترافي يعمل على الويب!**

للمساعدة أو الاستفسارات، راجع ملف `README.md` الكامل.
