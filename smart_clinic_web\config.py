#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات التطبيق
Application Configuration

ملف الإعدادات المتقدمة للتطبيق مع دعم بيئات مختلفة
"""

import os
from datetime import timedelta

class Config:
    """الإعدادات الأساسية"""
    
    # إعدادات Flask الأساسية
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'smart_clinic_2024_secret_key_change_in_production'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False  # True في الإنتاج مع HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # إعدادات التطبيق
    CLINIC_NAME = 'نظام إدارة العيادة الذكية'
    CLINIC_VERSION = '1.0.0'
    CLINIC_AUTHOR = 'Smart Clinic Team'
    
    # إعدادات التحديث التلقائي
    AUTO_REFRESH_INTERVAL = 30  # ثانية
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # ساعة واحدة
    
    # إعدادات الملفات
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = 'uploads'
    
    # إعدادات البريد الإلكتروني (للمستقبل)
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # إعدادات اللغة والمنطقة
    LANGUAGES = ['ar', 'en']
    DEFAULT_LANGUAGE = 'ar'
    TIMEZONE = 'Asia/Baghdad'
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق مع الإعدادات"""
        pass

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.abspath(os.path.dirname(__file__)), 'smart_clinic_dev.db')
    
    # تفعيل أدوات التطوير
    SQLALCHEMY_ECHO = True  # طباعة استعلامات SQL
    TEMPLATES_AUTO_RELOAD = True
    
    # إعدادات أمان مخففة للتطوير
    SESSION_COOKIE_SECURE = False
    WTF_CSRF_ENABLED = False  # تعطيل CSRF في التطوير

class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or 'sqlite:///:memory:'
    
    # تعطيل الحماية للاختبارات
    WTF_CSRF_ENABLED = False
    LOGIN_DISABLED = True

class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    
    DEBUG = False
    
    # قاعدة بيانات الإنتاج
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.abspath(os.path.dirname(__file__)), 'smart_clinic.db')
    
    # إعدادات أمان مشددة
    SESSION_COOKIE_SECURE = True  # يتطلب HTTPS
    WTF_CSRF_ENABLED = True
    
    # تحسينات الأداء
    SQLALCHEMY_ECHO = False
    SQLALCHEMY_POOL_RECYCLE = 3600
    
    @classmethod
    def init_app(cls, app):
        """تهيئة خاصة لبيئة الإنتاج"""
        Config.init_app(app)
        
        # تسجيل الأخطاء
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        file_handler = RotatingFileHandler(
            'logs/smart_clinic.log',
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Smart Clinic startup')

class DockerConfig(ProductionConfig):
    """إعدادات Docker"""
    
    @classmethod
    def init_app(cls, app):
        """تهيئة خاصة لـ Docker"""
        ProductionConfig.init_app(app)
        
        # تسجيل إلى stdout للحاويات
        import logging
        
        stream_handler = logging.StreamHandler()
        stream_handler.setLevel(logging.INFO)
        app.logger.addHandler(stream_handler)

# خريطة الإعدادات
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'docker': DockerConfig,
    'default': DevelopmentConfig
}

def get_config():
    """الحصول على إعدادات البيئة الحالية"""
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])

# إعدادات خاصة بالعيادة
CLINIC_SETTINGS = {
    'currency': {
        'code': 'IQD',
        'symbol': 'د.ع',
        'name': 'دينار عراقي',
        'decimal_places': 0
    },
    'date_format': {
        'short': '%d/%m/%Y',
        'long': '%A، %d %B %Y',
        'time': '%H:%M'
    },
    'working_hours': {
        'start': '08:00',
        'end': '18:00',
        'break_start': '12:00',
        'break_end': '14:00'
    },
    'appointment_duration': 30,  # دقيقة
    'max_appointments_per_day': 50,
    'backup_frequency': 'daily',  # daily, weekly, monthly
}

# إعدادات الواجهة
UI_SETTINGS = {
    'theme': {
        'primary_color': '#2c3e50',
        'secondary_color': '#3498db',
        'success_color': '#27ae60',
        'warning_color': '#f39c12',
        'danger_color': '#e74c3c',
        'dark_color': '#2c3e50',
        'light_color': '#ecf0f1'
    },
    'fonts': {
        'primary': 'Cairo',
        'secondary': 'Arial'
    },
    'icons': {
        'size': '36px',
        'library': 'font-awesome'
    },
    'animations': {
        'enabled': True,
        'duration': 300  # ميلي ثانية
    }
}

# إعدادات API
API_SETTINGS = {
    'version': 'v1',
    'rate_limit': '100/hour',
    'pagination': {
        'default_per_page': 20,
        'max_per_page': 100
    }
}

# إعدادات التقارير
REPORTS_SETTINGS = {
    'formats': ['pdf', 'excel', 'csv'],
    'default_format': 'pdf',
    'max_records': 10000,
    'cache_duration': 3600  # ثانية
}
