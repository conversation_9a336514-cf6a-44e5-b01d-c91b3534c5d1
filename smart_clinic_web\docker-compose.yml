version: '3.8'

services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
    volumes:
      - .:/app
      - /app/clinic_env
    command: python app.py
    
  # قاعدة بيانات PostgreSQL للإنتاج (اختيارية)
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: smart_clinic
      POSTGRES_USER: clinic_user
      POSTGRES_PASSWORD: clinic_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
