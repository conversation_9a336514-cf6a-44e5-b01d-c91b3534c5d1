#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل سريع للتطبيق
Quick Run Script

يقوم هذا الملف بتشغيل التطبيق مع إعداد قاعدة البيانات تلقائياً إذا لم تكن موجودة
"""

import os
import sys
from app import app, db

def setup_and_run():
    """إعداد وتشغيل التطبيق"""
    
    print("🏥 نظام إدارة العيادة الذكية - نسخة الويب")
    print("=" * 50)
    
    # التحقق من وجود قاعدة البيانات
    db_path = 'smart_clinic.db'
    
    if not os.path.exists(db_path):
        print("📋 قاعدة البيانات غير موجودة، سيتم إنشاؤها...")
        
        # تشغيل إعداد قاعدة البيانات
        try:
            import init_db
            print("✅ تم إنشاء قاعدة البيانات بنجاح!")
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            sys.exit(1)
    else:
        print("✅ قاعدة البيانات موجودة")
    
    print("\n🚀 بدء تشغيل التطبيق...")
    print("🌐 الرابط: http://localhost:5000")
    print("🔑 بيانات تسجيل الدخول:")
    print("   👑 المدير: admin / admin123")
    print("   👨‍⚕️ الطبيب: doctor1 / doctor123")
    print("   👩‍⚕️ الممرضة: nurse1 / nurse123")
    print("   👩‍💼 الاستقبال: reception1 / reception123")
    print("\n⏹️ لإيقاف التطبيق: اضغط Ctrl+C")
    print("=" * 50)
    
    # تشغيل التطبيق
    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بنجاح!")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")

if __name__ == '__main__':
    setup_and_run()
